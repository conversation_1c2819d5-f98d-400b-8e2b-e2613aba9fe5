import en from '~/locales/en.json'
import zh from '~/locales/zh.json'
import ja from '~/locales/ja.json'
import vi from '~/locales/vi.json'

import dayjs from 'dayjs'
import 'dayjs/locale/en'
import 'dayjs/locale/zh'
import 'dayjs/locale/ja'
import 'dayjs/locale/vi'
export default defineI18nConfig(() => ({
    legacy: false,
    globalInjection: true,
    locale: window.localStorage.getItem('locale') || 'en',
    silentFallbackWarn: true,
    missingWarn: false,
    fallbackWarn: false,
    messages: {
        en,
        zh,
        ja,
        vi,
    },
}))
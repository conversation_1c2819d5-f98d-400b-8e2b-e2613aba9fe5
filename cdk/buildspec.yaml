version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
    # Install dependencies for both site content and for CDK
    # - npm install -g npm@latest
    - npm install -g aws-cdk
    - npm install -g pnpm
    - export NODE_OPTIONS="--max-old-space-size=7168"
    - echo $ENV_FILE | tr " " "\n" > .env
    - cd cdk
    - npm install
    - cd ..
    - pnpm install
  build:
    commands:
    # Compile the site content
    - pnpm run generate
    # Deploy via the CDK
    - cd cdk
    - cdk deploy TTSFrontend -c contxt=$CONTXT_ENV --require-approval never

    # Clear cache
    # - echo "[+] Clear cache ..."
    - aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths '/*'

#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import { CdkStack } from '../lib/cdk-stack';
import { GptFrontendPipeline } from '../lib/tts-frontend-pipeline';

const app = new cdk.App();

const inputContext = app.node.tryGetContext('contxt')
const globalContext = app.node.tryGetContext('global')
const context = app.node.tryGetContext(inputContext)
const env = context['env']


new CdkStack(app, 'TTSFrontend', context ,{
  stackName: `${globalContext.prefix}-${env.environment}-frontend`,
  description: "Create Frontend stack",
  env: {
    account: `${env.account}`,
    region: `${env.region}`
  }
});

new GptFrontendPipeline(app, 'TTSFrontendPipeline', context, {
  stackName: `${globalContext.prefix}-${env.environment}-frontend-pipeline-stack`,
  description: "Stack for creating pipeline deploy TTS Frontend",
  env: {
    account: `${env.account}`,
    region: `${env.region}`
  }
})
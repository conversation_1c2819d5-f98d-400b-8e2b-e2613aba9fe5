{"app": "npx ts-node --prefer-ts-exts bin/cdk.ts", "watch": {"include": ["**"], "exclude": ["README.md", "cdk*.json", "**/*.d.ts", "**/*.js", "tsconfig.json", "package*.json", "yarn.lock", "node_modules", "test"]}, "context": {"@aws-cdk/aws-lambda:recognizeLayerVersion": true, "@aws-cdk/core:checkSecretUsage": true, "@aws-cdk/core:target-partitions": ["aws", "aws-cn"], "global": {"prefix": "tts-openai"}, "dev": {"env": {"account": "************", "region": "eu-central-1", "environment": "dev"}, "route53": {"domainName": "ttsopenai.com", "certificateArn": "arn:aws:acm:us-east-1:************:certificate/7912bd0a-262b-48ae-826d-698b8ab71c3d", "recordName": "dev"}, "codepipelinetts": {"name": "tts-frontend", "branch": "develop", "repo": "tts-frontend", "owner": "AINNATE-TTS", "connectionArn": "arn:aws:codestar-connections:eu-central-1:************:connection/fea8e41e-93d2-4577-a3ac-272a12f45305", "DISTRIBUTION_ID": "E1MSL9HKP8E9XM", "paramaterStoreEnv": "cdk-tts-openai-frontend-dev"}}, "stg": {"env": {"account": "************", "region": "eu-central-1", "environment": "stg"}, "route53": {"domainName": "ttsopenai.com", "certificateArn": "arn:aws:acm:us-east-1:************:certificate/7912bd0a-262b-48ae-826d-698b8ab71c3d", "recordName": "stg"}, "codepipelinetts": {"name": "tts-frontend", "branch": "staging", "repo": "tts-frontend", "owner": "AINNATE-TTS", "connectionArn": "arn:aws:codestar-connections:eu-central-1:************:connection/fea8e41e-93d2-4577-a3ac-272a12f45305", "DISTRIBUTION_ID": "E2JU40RB8WP7ZS", "paramaterStoreEnv": "cdk-tts-openai-frontend-stg"}}, "prod": {"env": {"account": "************", "region": "eu-central-1", "environment": "prod"}, "route53": {"domainName": "ttsopenai.com", "certificateArn": "arn:aws:acm:us-east-1:************:certificate/f7af6dbe-f95d-4637-95c8-0a654a19d1cb", "recordName": ""}, "codepipelinetts": {"name": "tts-frontend", "branch": "main", "repo": "tts-frontend", "owner": "AINNATE-TTS", "connectionArn": "arn:aws:codestar-connections:eu-central-1:************:connection/fea8e41e-93d2-4577-a3ac-272a12f45305", "DISTRIBUTION_ID": "EATGCGWI2WY4V", "paramaterStoreEnv": "cdk-tts-openai-frontend-prod"}}, "@aws-cdk-containers/ecs-service-extensions:enableDefaultLogDriver": true, "@aws-cdk/aws-ec2:uniqueImdsv2TemplateName": true, "@aws-cdk/aws-ecs:arnFormatIncludesClusterName": true, "@aws-cdk/aws-iam:minimizePolicies": true, "@aws-cdk/core:validateSnapshotRemovalPolicy": true, "@aws-cdk/aws-codepipeline:crossAccountKeyAliasStackSafeResourceName": true, "@aws-cdk/aws-s3:createDefaultLoggingPolicy": true, "@aws-cdk/aws-sns-subscriptions:restrictSqsDescryption": true, "@aws-cdk/aws-apigateway:disableCloudWatchRole": true, "@aws-cdk/core:enablePartitionLiterals": true, "@aws-cdk/aws-events:eventsTargetQueueSameAccount": true, "@aws-cdk/aws-iam:standardizedServicePrincipals": true, "@aws-cdk/aws-ecs:disableExplicitDeploymentControllerForCircuitBreaker": true, "@aws-cdk/aws-iam:importedRoleStackSafeDefaultPolicyName": true, "@aws-cdk/aws-s3:serverAccessLogsUseBucketPolicy": true, "@aws-cdk/aws-route53-patters:useCertificate": true, "@aws-cdk/customresources:installLatestAwsSdkDefault": false, "@aws-cdk/aws-rds:databaseProxyUniqueResourceName": true, "@aws-cdk/aws-codedeploy:removeAlarmsFromDeploymentGroup": true, "@aws-cdk/aws-apigateway:authorizerChangeDeploymentLogicalId": true, "@aws-cdk/aws-ec2:launchTemplateDefaultUserData": true, "@aws-cdk/aws-secretsmanager:useAttachedSecretResourcePolicyForSecretTargetAttachments": true, "@aws-cdk/aws-redshift:columnId": true, "@aws-cdk/aws-stepfunctions-tasks:enableEmrServicePolicyV2": true, "@aws-cdk/aws-ec2:restrictDefaultSecurityGroup": true, "@aws-cdk/aws-apigateway:requestValidatorUniqueId": true, "@aws-cdk/aws-kms:aliasNameRef": true, "@aws-cdk/aws-autoscaling:generateLaunchTemplateInsteadOfLaunchConfig": true, "@aws-cdk/core:includePrefixInUniqueNameGeneration": true, "@aws-cdk/aws-efs:denyAnonymousAccess": true, "@aws-cdk/aws-opensearchservice:enableOpensearchMultiAzWithStandby": true, "@aws-cdk/aws-lambda-nodejs:useLatestRuntimeVersion": true, "@aws-cdk/aws-efs:mountTargetOrderInsensitiveLogicalId": true, "@aws-cdk/aws-rds:auroraClusterChangeScopeOfInstanceParameterGroupWithEachParameters": true, "@aws-cdk/aws-appsync:useArnForSourceApiAssociationIdentifier": true, "@aws-cdk/aws-rds:preventRenderingDeprecatedCredentials": true, "@aws-cdk/aws-codepipeline-actions:useNewDefaultBranchForCodeCommitSource": true, "@aws-cdk/aws-cloudwatch-actions:changeLambdaPermissionLogicalIdForLambdaAction": true}}
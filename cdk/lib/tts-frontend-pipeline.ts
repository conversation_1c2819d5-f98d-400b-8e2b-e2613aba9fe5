
import { CfnOutput, RemovalPolicy, SecretValue, Stack } from "aws-cdk-lib";
import { Construct } from "constructs";
import {
  aws_codepipeline as codepipeline,
  aws_codepipeline_actions as actions,
  aws_codebuild as codebuild,
  aws_iam as iam,
} from 'aws-cdk-lib';
import * as cdk from 'aws-cdk-lib';
import { BlockPublicAccess, Bucket } from "aws-cdk-lib/aws-s3";

export class GptFrontendPipeline extends Stack {
  constructor(scope: Construct, id: string, context: any, props: cdk.StackProps) {
    super(scope, id, props);

    /**
     * ########## Global context ##################
     */
    // const globalContext = this.node.tryGetContext('global')
    const globalContext = this.node.tryGetContext('global')
    const env = context['env']

    let triggerOnPush = true
    // if(globalContext.environment == "prod"){
    //   triggerOnPush = false
    // }

    
    /**
    * ########## Codepipeline context ##################
    */
    const codepipelineContext = context['codepipelinetts']

    /**
    * ########### Codepipeline ################
    */
    const artifactWebAppBucket = new Bucket(this, 'ArtifactBucket', {
      bucketName: `${globalContext.prefix}-${env.environment}-gpt-frontend-s3-artifact-codepipeline`,
      blockPublicAccess: BlockPublicAccess.BLOCK_ALL,
      removalPolicy: RemovalPolicy.DESTROY,
      autoDeleteObjects: true
    });

    const pipeline = new codepipeline.Pipeline(this, 'Pipeline', {
      pipelineName: `${globalContext.prefix}-${env.environment}-${codepipelineContext.name}-pipeline`,
      artifactBucket: artifactWebAppBucket
    })

    const sourceOutput = new codepipeline.Artifact('SourceArtifact');
    const sourceAction = new actions.CodeStarConnectionsSourceAction({
      actionName: 'Github_Souce',
      owner: `${codepipelineContext.owner}`,
      repo: `${codepipelineContext.repo}`,
      branch: `${codepipelineContext.branch}`,
      connectionArn: `${codepipelineContext.connectionArn}`,
      output: sourceOutput,
      triggerOnPush: triggerOnPush,
    });

    pipeline.addStage({
      stageName: 'Source',
      actions: [sourceAction],
    });

    /**
    * ############### CodeBuild ###############
    */
    const deployProject = new codebuild.PipelineProject(this, 'BuildProject', {
      projectName: `tts-openai-frontend-${env.environment}`,
      buildSpec: codebuild.BuildSpec.fromSourceFilename('cdk/buildspec.yaml'),
      environment: {
        buildImage: codebuild.LinuxBuildImage.fromCodeBuildImageId('aws/codebuild/standard:7.0'),
        environmentVariables: {
          ENV_FILE: {
            type: codebuild.BuildEnvironmentVariableType.PARAMETER_STORE,
            value: `${codepipelineContext.paramaterStoreEnv}`
          },
          CONTXT_ENV: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: `${env.environment}`
          },
          DISTRIBUTION_ID: {
            type: codebuild.BuildEnvironmentVariableType.PLAINTEXT,
            value: `${codepipelineContext.DISTRIBUTION_ID}`
          }
        },
      }
    })

    deployProject.addToRolePolicy(new iam.PolicyStatement({
      actions: [
        "s3:*",
        "cloudformation:*",
        "ssm:*",
        "iam:PassRole",
        "sts:AssumeRole",
        "ssm:GetParameters",
        "acm:ListCertificates",
        "cloudfront:GetDistribution",
        "cloudfront:GetStreamingDistribution",
        "cloudfront:GetDistributionConfig",
        "cloudfront:ListDistributions",
        "cloudfront:ListCloudFrontOriginAccessIdentities",
        "cloudfront:CreateInvalidation",
        "cloudfront:GetInvalidation",
        "cloudfront:ListInvalidations",
        "elasticloadbalancing:DescribeLoadBalancers",
        "iam:ListServerCertificates",
        "sns:ListSubscriptionsByTopic",
        "sns:ListTopics",
        "waf:GetWebACL",
        "waf:ListWebACLs"
      ],
      resources: ['*']
    }));

    const deployAction = new actions.CodeBuildAction({
      actionName: 'Deploy',
      project: deployProject,
      input: sourceOutput,
    })

    // const buildArtifact = new codepipeline.Artifact('BuildArtifact');
    // const deployAction = new actions.CodeBuildAction({
    //   actionName: 'Deploy',
    //   project: deployProject,
    //   input: sourceOutput1,
    // })

    pipeline.addStage({
      stageName: 'Deploy',
      actions: [deployAction]
    })
  }
}
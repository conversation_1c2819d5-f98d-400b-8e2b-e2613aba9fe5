<script setup lang="ts">
import type { NuxtError } from '#app'

const props = defineProps({
  error: Object as () => NuxtError
})

import * as validators from '@vuelidate/validators'
import { useVuelidate } from '@vuelidate/core'
const i18n = useI18n()
const { t, locale } = useI18n()
const authStore = useAuthStore()
const appStore = useAppStore()
const route = useRoute()
const { createI18nMessage } = validators
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const { isLoggingIn, signInError } = storeToRefs(authStore)
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)
const minLength = (v: any) => withI18nMessage(validators.minLength(v))

definePageMeta({
    layout: false,
    layoutTransition: true,
})

const fields = computed(() => [] as any[])

const formLoginRules = computed(() => ({
    username: { required, email },
    password: { required, minLength: minLength(6) },
}))

const validate = async (state: any) => {
    const v$ = useVuelidate(formLoginRules, state)
    const result = await v$.value.$validate()
    const errors = [] as any[]
    v$.value.$errors.forEach((error: any) => {
        errors.push({ path: error.$propertyPath, message: error.$message })
    })
    return errors
}

const providers = []

async function onSubmit(data: any) {
    const success = await authStore.login({
        username: data.username,
        password: data.password,
        remember_me: true,
    })
    if (success) {
        navigateTo('/')
    }
}

</script>

<!-- eslint-disable vue/multiline-html-element-content-newline -->
<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
    <NuxtLoadingIndicator />
    <NuxtLayout name="auth">
        <UCard class="mx-auto max-w-sm w-full justify-center text-center bg-white/75 dark:bg-gray-900/80 backdrop-blur">
            <UAuthForm
                :key="locale"
                :fields="fields"
                :validate="validate"
                :title="$t('Error')"
                align="top"
                icon="i-heroicons-exclamation-circle"
                :ui="{ base: 'text-center gap-4', footer: 'text-center' }"
            >
                <template #description>
                    <div>
                        {{ $t('Something went wrong. Please try again later or contact support.') }}
                    </div>
                    <div>
                        {{ error }}
                    </div>
                </template>
            </UAuthForm>
        </UCard>
    </NuxtLayout>
</template>

<template>
    <div>
        <NuxtLoadingIndicator />
        <NuxtLayout>
            <div>
                <ClientOnly>
                    <Loading :active="loading" :is-full-page="true">
                        <div class="bg-gray-50/40 py-5 px-4 rounded-full">
                            <BaseSoundWave />
                        </div>
                    </Loading>
                </ClientOnly>
                <NuxtPage />
                <UNotifications />
                <BaseConfirmDialog />
                <RemoveAdsPopup v-if="!isBeta && enableAds" />
                <PaymentModal v-bind="paymentInfo" />
                <CommonManualModal />
                <PopupMaintenance />
                <PopupReferFriends />
            </div>
        </NuxtLayout>
    </div>
</template>

<script setup>
import Loading from 'vue-loading-overlay'
// import 'flowbite'
// import { initFlowbite } from 'flowbite'
import 'vue-loading-overlay/dist/css/index.css'
import { PROTECTED_ROUTES } from '~/middleware/auth.global'
import { useModal } from 'vue-final-modal'
import SessionExpired from './base-components/SessionExpired.vue'

const router = useRouter()
const route = useRoute()
const config = useRuntimeConfig()
const isBeta = config.public.isBeta
const enableAds = config.public.enableAds
const { open, close } = useModal({
    component: SessionExpired,
    attrs: {
        onClose() {
            isExpired.value = false
            close()
            router.push('/signin')
        },
    },
})
const paymentsStore = usePaymentsStore()
const { paymentInfo, isOpen, noAdsProduct } = storeToRefs(paymentsStore)

const appStore = useAppStore()
const authStore = useAuthStore()
const { user, isExpired, referralInfo } = storeToRefs(authStore)
const { loading } = storeToRefs(appStore)
const paymentStore = usePaymentsStore()
onMounted(() => {
    nextTick(() => {
        // initFlowbite()

        const { ps_partner_key, ps_xid, gsxid, gspk } = route.query
        if (ps_partner_key || ps_xid || gsxid || gspk) {
            referralInfo.value = {
                ps_partner_key,
                ps_xid,
                gsxid,
                gspk,
            }
        }
    })

    paymentStore.getAllProductsAndPlans()
    paymentStore.getDiscountConfigs('BUY_CREDITS')

    // {
    //     src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************',
    //     async: true,
    //     crossorigin: 'anonymous',
    //     tagPosition: 'bodyOpen',
    // },

    // const script = document.createElement("script");
    // script.src =
    //   "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************";
    // script.async = true;
    // script.crossOrigin = "anonymous";
    // // append script tag to body
    // document.body.appendChild(script);

    // setTimeout(() => {
    //   (adsbygoogle = window.adsbygoogle || []).push({});
    // }, 5000);
})

useHead({
    bodyAttrs: {
        class: 'bg-gray-50 dark:bg-slate-950 max-w-screen overflow-x-hidden',
    },
})

watch(
    () => route.query.access_token,
    (value) => {
        if (value) {
            localStorage.setItem('access_token', value)
            nextTick(async () => {
                await authStore.fetchUserInfo()
                navigateTo('/')
            })
        }
    },
    {
        immediate: true,
        deep: true,
    }
)

watch(user, () => {
    if (!user.value) {
        const route = useRoute()
        const router = useRouter()
        if (PROTECTED_ROUTES.includes(route.name)) {
            router.push('/signin')
        }
    }
})

watch(isExpired, () => {
    if (isExpired.value) {
        open()
    }
})

useSeoMeta({
    title: 'OpenAI Text To Speech | Advanced Voice Engine Technology',
    ogTitle: 'OpenAI Text To Speech | Advanced Voice Engine Technology',
    description:
        'Discover the future of digital communication with our cutting-edge Text To Speech OpenAI technology. Our advanced Voice Engine transforms text into natural-sounding speech, seamlessly bridging the gap between humans and machines. Ideal for developers, creators, and businesses, our platform offers an intuitive API for easy integration, ensuring your applications and services are more accessible and engaging than ever. Experience unparalleled voice quality and flexibility with our solution, designed to elevate your digital interactions to new heights',
    ogDescription:
        'Discover the future of digital communication with our cutting-edge Text To Speech OpenAI technology. Our advanced Voice Engine transforms text into natural-sounding speech, seamlessly bridging the gap between humans and machines. Ideal for developers, creators, and businesses, our platform offers an intuitive API for easy integration, ensuring your applications and services are more accessible and engaging than ever. Experience unparalleled voice quality and flexibility with our solution, designed to elevate your digital interactions to new heights',
    ogImage: 'https://ttsopenai.com/_nuxt/logo-tts.png',
})
</script>

<style>
.vfm__content {
    width: 100%;
}
</style>

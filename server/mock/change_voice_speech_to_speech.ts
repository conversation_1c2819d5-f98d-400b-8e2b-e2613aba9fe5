export default {
    name: 'Change Your Voice With Speech To Speech',
    page: 'home',
    section: 'change_voice_speech_to_speech',
    basic: {
        title: 'Change Your Voice With Speech To Speech',
        description:
            'Edit and fine-tune your voiceovers using Speech to Speech. Get consistent, clear results that keep the feel and nuance of your original message.',
        link: {
            title: 'Change your voice',
            icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" class="inline-flex w-4 h-4 ml-2"><path fill-rule="evenodd" d="M12.97 3.97a.75.75 0 011.06 0l7.5 7.5a.75.75 0 010 1.06l-7.5 7.5a.75.75 0 11-1.06-1.06l6.22-6.22H3a.75.75 0 010-1.5h16.19l-6.22-6.22a.75.75 0 010-1.06z" clip-rule="evenodd"></path></svg>',
            url: 'http://localhost:3000/home',
        },
    },
    content: {
        video_src: 'https://eleven-public-cdn.Text To Speech OpenAI.io/video/voice-changer/voice_changer_temp_demo.mp4',
        video_tag: 'Your browser does not support the video tag.',
        video_created_by: 'Video created by',
        video_target_link: {
            title: 'Mike Russell',
            url: 'https://www.youtube.com/watch?v=0UVppC0Ihjk',
        },
        introduction: [
            {
                title: 'Emotional Range',
                icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" class="absolute left-1 top-1 h-5 w-5 text-black"><path stroke-linecap="round" stroke-linejoin="round" d="M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z"></path></svg>',
                description: 'Maintain the exact emotions of your content with our diverse range of voice profiles.',
            },
            {
                title: 'Nuance Preservation',
                icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" class="absolute left-1 top-1 h-5 w-5 text-black"><path fill-rule="evenodd" d="M4.5 3.75a3 3 0 00-3 3v10.5a3 3 0 003 3h15a3 3 0 003-3V6.75a3 3 0 00-3-3h-15zm4.125 3a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5zm-3.873 8.703a4.126 4.126 0 017.746 0 .75.75 0 01-.351.92 7.47 7.47 0 01-3.522.877 7.47 7.47 0 01-3.522-.877.75.75 0 01-.351-.92zM15 8.25a.75.75 0 000 1.5h3.75a.75.75 0 000-1.5H15zM14.25 12a.75.75 0 01.75-.75h3.75a.75.75 0 010 1.5H15a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5h3.75a.75.75 0 000-1.5H15z" clip-rule="evenodd"></path></svg>',
                description: 'Ensure that every inflection, pause and modulation is captured and reproduced perfectly.',
            },
            {
                title: 'Consistent Quality',
                icon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true" class="absolute left-1 top-1 h-5 w-5 text-black"><path fill-rule="evenodd" d="M10.5 3.798v5.02a3 3 0 01-.879 2.121l-2.377 2.377a9.845 9.845 0 015.091 1.013 8.315 8.315 0 005.713.636l.285-.071-3.954-3.955a3 3 0 01-.879-2.121v-5.02a23.614 23.614 0 00-3 0zm4.5.138a.75.75 0 00.093-1.495A24.837 24.837 0 0012 2.25a25.048 25.048 0 00-3.093.191A.75.75 0 009 3.936v4.882a1.5 1.5 0 01-.44 1.06l-6.293 6.294c-1.62 1.621-.903 4.475 1.471 4.88 2.686.46 5.447.698 8.262.698 2.816 0 5.576-.239 8.262-.697 2.373-.406 3.092-3.26 1.47-4.881L15.44 9.879A1.5 1.5 0 0115 8.818V3.936z" clip-rule="evenodd"></path></svg>',
                description: 'Use Speech to Speech to create complex audio sequences with consistent quality.',
            },
        ],
    },
}

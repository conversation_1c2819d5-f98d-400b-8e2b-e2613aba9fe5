import natural_and_ai_text_to_speech from '~/server/mock/natural_and_ai_text_to_speech'
import ai_text_to_speech from '~/server/mock/ai_text_to_speech'
import dubbing_studio from '~/server/mock/dubbing_studio'
import change_voice_speech_to_speech from '~/server/mock/change_voice_speech_to_speech'
import long_form_voice_generation from '~/server/mock/long_form_voice_generation'
import powered_cut_research from '~/server/mock/powered_cut_research'
import review from '~/server/mock/review'
import ai_voice_generator_language from '~/server/mock/ai_voice_generator_language'
import ai_voice_lab from '~/server/mock/ai_voice_lab'
import faq from '~/server/mock/faq'

export default defineEventHandler(async (event) => {
    return [
        natural_and_ai_text_to_speech,
        ai_text_to_speech,
        dubbing_studio,
        change_voice_speech_to_speech,
        long_form_voice_generation,
        powered_cut_research,
        review,
        ai_voice_generator_language,
        ai_voice_lab,
        faq,
    ]
})

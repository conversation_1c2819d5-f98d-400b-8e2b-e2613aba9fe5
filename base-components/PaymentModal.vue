<template>
    <!-- Main modal -->
    <UModal v-model="isOpen">
        <!-- Large Modal -->
        <div class="relative w-full max-w-lg max-h-full mx-auto">
            <!-- Modal content -->
            <div class="relative rounded-lg shadow bg-gray-100 text-black">
                <!-- Modal header -->
                <div class="flex items-center justify-between p-5 border-b rounded-t dark:border-gray-400">
                    <h3 class="flex items-center text-xl font-medium">
                        <span class="flex items-center justify-center w-8 h-8 rounded-full bg-primary-600 mr-3">
                            <svg
                                class="w-5 h-5 text-white"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                viewBox="0 0 20 14"
                            >
                                <path
                                    d="M18 0H2a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2ZM2 12V6h16v6H2Z"
                                />
                                <path d="M6 8H4a1 1 0 0 0 0 2h2a1 1 0 0 0 0-2Zm8 0H9a1 1 0 0 0 0 2h5a1 1 0 1 0 0-2Z" />
                            </svg>
                        </span>
                        {{ $t('Checkout') }}
                    </h3>
                    <button
                        @click="onClose"
                        type="button"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        data-modal-hide="large-modal"
                    >
                        <svg
                            class="w-3 h-3"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 14 14"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
                            />
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="p-6 space-y-6 overflow-auto scrollbar-thin" style="max-height: 80vh">
                    <div v-if="type === 'change-plan'">
                        <div class="mb-2">{{ $t('Plan detail') }}</div>
                        <div class="flex flex-col space-y-1">
                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div>
                                    {{ plan.title }}
                                </div>
                                <div class="font-semibold text-xl">$ {{ plan.price }}</div>
                            </div>
                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div>
                                    {{ formatUserTokens(+plan.base_credit) }}
                                    <small class="text-xs">{{ $t('credits') }}</small>

                                    <span
                                        class="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded border border-yellow-300"
                                        >{{ $t('Bonus') }}</span
                                    >
                                </div>
                                <div class="font-semibold text-xl">$ 0</div>
                            </div>
                            <hr class="h-px my-4 bg-gray-200 border-0" />
                            <!-- <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div>
                                    {{ $t('Monthly Subscription') }}
                                </div>
                                <label class="relative inline-flex items-center mr-5 cursor-pointer">
                                    <input type="checkbox" v-model="isSubscribed" class="sr-only peer" />
                                    <div
                                        class="w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-primary-300 dark:peer-focus:ring-primary-800 dark:bg-gray-200 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary-600 ring-2"
                                    ></div>
                                </label>
                            </div> -->
                            <div class="py-3 dark:bg-gray-800 dark:text-gray-50 px-2">
                                <div class="flex flex-col md:flex-row gap-4 md:items-center">
                                    <UButton
                                        icon="i-heroicons-plus-circle"
                                        size="sm"
                                        color="primary"
                                        variant="ghost"
                                        :label="$t('Buy more credits')"
                                        :padded="false"
                                        :trailing="false"
                                        @click="moreCreditTimes++"
                                    />
                                    <UButton
                                        v-if="moreCreditTimes"
                                        icon="i-heroicons-minus-circle"
                                        size="sm"
                                        color="gray"
                                        variant="ghost"
                                        :label="$t('Reduce the additional credits')"
                                        :padded="false"
                                        :trailing="false"
                                        @click="moreCreditTimes--"
                                    />
                                </div>
                                <div
                                    v-if="moreCreditTimes"
                                    class="w-full flex flex-inline items-center justify-between text-lg mt-3"
                                >
                                    <div>
                                        {{ formatUserTokens(moreCredits) }}
                                        <small class="text-xs">{{ $t('credits') }}</small>
                                        <span
                                            class="ml-2 bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded border border-primary-300"
                                            >{{ $t('Additional') }}</span
                                        >
                                    </div>

                                    <div class="font-semibold text-xl">$ {{ moreCreditTimes * plan?.price }}</div>
                                </div>
                            </div>
                            <hr class="h-px my-4 bg-gray-200 border-0" />
                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div class="font-semibold text-xl">
                                    {{ $t('Total') }}
                                </div>
                                <div
                                    class="font-semibold text-3xl"
                                    :class="isSubscribed ? 'text-blue-500' : 'text-yellow-400'"
                                >
                                    $ {{ plan.price + moreCreditTimes * plan?.price }}
                                    <span v-if="isSubscribed" class="text-base">/ {{ $t('month') }}</span>
                                </div>
                            </div>
                            <p v-if="isSubscribed" class="text-sm font-thin mt-4">
                                {{
                                    $t('You will be charged $ monthly. You can cancel your subscription at any time.', {
                                        price: plan.price,
                                    })
                                }}
                            </p>
                            <p v-else-if="currentSubscriptionPlan" class="text-sm font-thin mt-4 text-red-500">
                                {{
                                    $t(
                                        'Your current subscription will be canceled and you will be charged $ once for this plan.',
                                        {
                                            price: plan.price,
                                        }
                                    )
                                }}
                            </p>
                        </div>
                    </div>
                    <div v-else-if="type === 'no-ads'">
                        <div class="mb-2">{{ $t('Remove Ads!') }}</div>
                        <div class="flex flex-col space-y-1">
                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div>
                                    {{ $t('{month} month', { month: quantity }) }}
                                </div>
                                <div class="font-semibold text-xl">$ {{ price }}</div>
                            </div>
                            <hr class="h-px my-4 bg-gray-200 border-0" />

                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div class="font-semibold text-xl">
                                    {{ $t('Total') }}
                                </div>
                                <div class="font-semibold text-3xl" :class="'text-yellow-400'">$ {{ price }}</div>
                            </div>
                            <p class="text-sm font-thin mt-4">
                                {{
                                    $t('Ads will be hidden for {month} months.', {
                                        month: quantity,
                                    })
                                }}
                            </p>
                        </div>
                    </div>
                    <div v-else>
                        <div class="font-thin mb-2">{{ $t('Deposit detail') }}</div>
                        <div class="flex flex-col space-y-1">
                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div>
                                    {{ new Intl.NumberFormat().format(tokens) }}
                                    <small class="text-xs">{{ $t('credits') }}</small>
                                </div>
                                <div class="font-semibold text-xl">$ {{ price }}</div>
                            </div>
                            <div v-if="bonus > 0" class="w-full flex flex-inline items-center justify-between text-lg">
                                <div>
                                    {{ new Intl.NumberFormat().format((tokens * bonus) / 100) }}
                                    <small class="text-xs">{{ $t('credits') }}</small>

                                    <span
                                        class="ml-2 bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded border border-yellow-300"
                                        >{{ $t('Bonus') }} {{ bonus }}%</span
                                    >
                                </div>
                                <div class="font-semibold text-xl">$ 0</div>
                            </div>
                            <hr class="h-px my-4 bg-gray-200 border-0" />
                            <div class="w-full flex flex-inline items-center justify-between text-lg">
                                <div class="font-semibold text-xl">
                                    {{ $t('Total') }}:
                                    {{ new Intl.NumberFormat().format(tokens * (1 + bonus / 100)) }}
                                    <small class="text-xs">{{ $t('credits') }}</small>
                                </div>
                                <div class="font-semibold text-3xl text-yellow-400">$ {{ price }}</div>
                            </div>
                        </div>
                    </div>

                    <hr class="h-px my-4 bg-gray-200 border-0" />
                    <div v-if="isBeta || paymentOff || !paypalId">
                        <div
                            class="flex items-center p-4 mb-4 text-sm text-yellow-800 border border-yellow-300 rounded-lg bg-yellow-50"
                            role="alert"
                        >
                            <svg
                                class="flex-shrink-0 inline w-4 h-4 mr-3"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"
                                />
                            </svg>
                            <span class="sr-only">Info</span>
                            <div>
                                <span class="font-medium">{{ $t("We're Sorry!") }}</span>
                                {{
                                    $t('Our payment system is currently under maintenance. Please check back shortly!')
                                }}
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div v-if="Object.keys(error).length > 0">
                            <div
                                class="flex items-center p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800"
                                role="alert"
                            >
                                <svg
                                    class="flex-shrink-0 inline w-4 h-4 mr-3"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"
                                    />
                                </svg>
                                <span class="sr-only">Info</span>
                                <div v-for="errorObject in error">
                                    <span v-if="errorObject">{{ $t(errorObject?.error_code || '') }}</span>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <div class="font-thin mb-2">{{ $t('Payment') }}</div>

                            <div
                                v-if="isUnverified"
                                class="w-full text-center cursor-pointer text-white bg-gray-700 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 focus:outline-none dark:focus:ring-gray-800"
                            >
                                {{ $t('Account unverified') }}
                            </div>
                            <div v-if="!isUnverified && renderingButton" role="status" class="w-full text-center my-4">
                                <svg
                                    aria-hidden="true"
                                    class="inline w-8 h-8 mr-2 text-gray-200 animate-spin dark:text-gray-200 fill-primary-600"
                                    viewBox="0 0 100 101"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                        fill="currentColor"
                                    />
                                    <path
                                        d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                        fill="currentFill"
                                    />
                                </svg>
                                <span class="sr-only">Loading...</span>
                            </div>
                            <div v-if="features.googlePay" class="mb-4">
                                <GooglePay :total-price="price" @paymentSuccess="googlePaySuccess" />
                            </div>
                            <div v-if="features.cryptoPay" class="mb-4">
                                <CryptoPay
                                    :product-id="props.id || 'BC0001'"
                                    :quantity="
                                        props.type === 'change-plan' ? totalPrice / tokenUnitPrice : props.quantity || 1
                                    "
                                    :special-bonus-percent="specialBonusPercent"
                                    :extra-percentage="cryptoExtraPercent"
                                    :total-price="price || plan.price + moreCreditTimes * plan?.price"
                                    :total-credits="tokens || moreCredits + +plan.base_credit"
                                    :specialBonusCredits="cryptoExtraCredits"
                                />
                            </div>
                            <div v-if="features.stripePay && canUseStripe" class="mb-4">
                                <StripePay
                                    :product-id="props.id || 'BC0001'"
                                    :quantity="
                                        props.type === 'change-plan' ? totalPrice / tokenUnitPrice : props.quantity || 1
                                    "
                                    :special-bonus-percent="specialBonusPercent"
                                    :extra-percentage="stripeExtraPercent"
                                    :total-price="price || plan.price + moreCreditTimes * plan?.price"
                                    :total-credits="tokens || moreCredits + +plan.base_credit"
                                    :specialBonusCredits="stripeExtraCredits"
                                />
                            </div>
                            <div v-show="!isUnverified && !renderingButton">
                                <div v-show="!isSubscribed" id="btnPaypal" class="text-center"></div>
                                <div v-show="isSubscribed" id="btnPaypalSubcribe"></div>
                            </div>
                            <div v-if="extraPaypayPercentage > 0" class="text-sm text-center">
                                <div class="group flex flex-row gap-2">
                                    <div>
                                        Paypal: 
                                    </div>
                                    <UBadge v-if="extraPaypayPercentage > 0" :ui="{ rounded: 'rounded-full' }" color="green">
                                        <div class="group-hover:hidden">
                                            {{
                                                $t('+{per}% credit bonus', {
                                                    per: extraPaypayPercentage > 0 ? extraPaypayPercentage : 0,
                                                })
                                            }}
                                        </div>
                                        <div class="hidden group-hover:block">
                                            <div class="w-full flex flex-inline items-center gap-1">
                                                <div class="font-semibold">${{ price || plan?.price + moreCreditTimes * plan?.price }}</div>
                                                <UIcon name="i-ep-right" />
                                                <div>
                                                    {{
                                                        new Intl.NumberFormat().format(
                                                            (tokens || moreCredits + + props.plan?.base_credit) + extraPaypayCredits *  props.quantity
                                                        )
                                                    }}
                                                    <small class="text-xs">{{ $t('credits') }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </UBadge>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </UModal>
</template>

<script lang="ts" setup>
let paypal = null as any
import { VueFinalModal } from 'vue-final-modal'
import { loadScript } from '@paypal/paypal-js'

const paymentsStore = usePaymentsStore()
const authStore = useAuthStore()

const { error, isSubscribed, isOpen, moreCreditTimes, tokenUnitPrice, discountConfigs } = storeToRefs(paymentsStore)
const { canUseStripe, isUnverified, currentSubscriptionPlan } = storeToRefs(authStore)
const config = useRuntimeConfig()
const isBeta = config.public.isBeta
const features = config.public.features
watch(currentSubscriptionPlan, (value) => {
    if (value) {
        isSubscribed.value = true
    } else {
        isSubscribed.value = false
    }
})

const emit = defineEmits(['submit', 'close'])
const props = defineProps({
    title: {
        type: String,
    },
    type: {
        type: String,
        default: 'tokens',
    },
    tokens: {
        type: Number,
    },
    price: {
        type: Number,
    },
    bonus: {
        type: Number,
    },
    plan: {
        type: Object,
    },
    id: {
        type: String,
    },
    quantity: {
        type: Number,
    },
    specialBonusCredit: {
        type: Number,
    },
})

const paymentOff = ref(config.public.NUXT_PAYMENT_MAINTENANCE || false)
const paypalId = ref(config.public.NUXT_PAYPAL_ID)
const totalPrice = computed(() => props.plan?.price + moreCreditTimes.value * props.plan?.price)
const formatUserTokens = (number) => {
    return new Intl.NumberFormat().format(number.toFixed(0))
}

const moreCredits = computed(() => {
    return moreCreditTimes.value * props.plan?.base_credit
})

const specialBonusPercent = computed(() => {
    return Math.round((props.specialBonusCredit / props.plan?.base_credit) * 100)
})

// Get extra percentage for different payment methods
const cryptoExtraPercent = computed(() => {
    return paymentsStore.extraPercentageByPaymentMethod('Cryptomus')
})

const cryptoExtraCredits = computed(() => {
    return Math.round((props.plan?.base_credit * cryptoExtraPercent.value) / 100)
})

const stripeExtraPercent = computed(() => {
    return paymentsStore.extraPercentageByPaymentMethod('Stripe')
})

const stripeExtraCredits = computed(() => {
    return Math.round((props.plan?.base_credit * stripeExtraPercent.value) / 100)
})

const extraPaypayPercentage = computed(() => {
    return paymentsStore.extraPercentageByPaymentMethod('PayPal')
})

const extraPaypayCredits = computed(() => {
    return Math.round((props.plan?.base_credit * extraPaypayPercentage.value) / 100)
})

const router = useRouter()

const onSubmit = async () => {}
const onClose = () => {
    isOpen.value = false
    emit('close')
}
const renderingButton = ref(false)
const renderPaypalButton = async () => {
    error.value = {}
    renderingButton.value = true
    if (paypal) {
        try {
            await paypal
                .Buttons({
                    // Set up the transaction
                    createOrder: async function (data, actions) {
                        return paymentsStore.createPaypalOrder({
                            product_id: props.id || 'BC0001',
                            quantity:
                                props.type === 'change-plan'
                                    ? totalPrice.value / tokenUnitPrice.value
                                    : props.quantity || 1,
                        })
                    },

                    // Finalize the transaction
                    onApprove: async function (data, actions) {
                        if (data?.paymentID) {
                            const result = await paymentsStore.approvePaypalOrder()
                            if (result && result?.success) {
                                //redirect to success page
                                isOpen.value = false
                                router.push('/profile/thank-you?payment=success&id=' + paymentsStore.orderUUID)
                            }
                        } else {
                            return await paymentsStore.cancelPaypalOrder()
                        }
                    },

                    onCancel: async function (data) {
                        console.log('The payment was cancelled!', data)
                        return paymentsStore.cancelPaypalOrder()
                    },
                })
                .render('#btnPaypal')
        } catch (error) {
            console.error('failed to render the PayPal Buttons', error)
        } finally {
            renderingButton.value = false
        }
    }
}

const googlePaySuccess = (paymentToken: string) => {
    isOpen.value = false
    router.push('/profile/thank-you?payment=success&id=' + paymentToken)
}

watch(
    () => isOpen.value,
    async (value) => {
        if (value) {
            try {
                paypal = await loadScript({
                    clientId: paypalId.value,
                    vault: true,
                    disableFunding: 'card',
                })
            } catch (error) {
                console.error('failed to load the PayPal JS SDK script', error)
            }

            await renderPaypalButton()
        }
    }
)

onMounted(async () => {})
</script>

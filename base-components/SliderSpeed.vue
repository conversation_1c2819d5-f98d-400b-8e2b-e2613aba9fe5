<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import slider from 'vue3-slider'
import * as yup from 'yup'

const { t, locale } = useI18n()



const props = defineProps({
    modelValue: {
        type: Number,
        required: true,
  },
    customCss: {
        type: Boolean,
        required: false,
    },
    size: {
        type: String,
        required: false,
        default: 'xs',
    }
})

const speedState = ref(props.modelValue)
const speedStateInput = ref(props.modelValue)
const errorMessage = ref('')

const speedSchema = yup
    .number()
    .test(
        'is-valid-speed',
        'Speed must be between 0.25 and 4',
        (value) => value !== undefined && value >= 0.25 && value <= 4
    )

const validateSpeed = async (value: number) => {
    try {
        await speedSchema.validate(value)
        errorMessage.value = ''
        return true
    } catch (error: any) {
        errorMessage.value = t('Speed must be between 0.25 and 4')
        return false
    }
}

const emit = defineEmits([
  "onChangeSpeed",
  "onError"
]);

const formattedSpeed = (value: number) => {
    const formattedSpeed = Number.isInteger(value) ? value.toString() : value.toFixed(2)
    return `${formattedSpeed}`
}

const onChangeSpeed = async (value: number) => {
    if (await validateSpeed(value)) {
        const roundedValue = Math.round(value * 100) / 100
        speedStateInput.value = roundedValue
        emit("onChangeSpeed", roundedValue)
    }
}

watch(speedStateInput, async (newValue) => {
  if (await validateSpeed(newValue)) {
    // round to 2 decimal places
    const roundedValue = Math.round(newValue * 100) / 100;
    if (roundedValue) {
      speedState.value = roundedValue;
      emit("onChangeSpeed", roundedValue);
    }
  }
});

watch(errorMessage, (newValue) => {
  emit("onError", newValue)
})


</script>

<style lang="scss">
.form-group-custom {
  .relative {
    margin-top: v-bind("props.customCss ? '-3px' : 'unset'") !important;
  }
}
</style>


<template>
  <UFormGroup :key="locale" class="col-span-2 form-group-custom" :size="size as any" :label="$t('Speed')">
    <div class="flex flex-row items-center">
      <slider
          v-model="speedState"
          :min="0.25"
          :max="4"
          :step="0.01"
          height="8"
          color="#3a9284"
          track-color="#CCD7D5"
          tooltip="true"
          alwaysShowHandle="true"
          tooltipTextColor="#3a9284"
          handleScale="1.5"
          :formatTooltip="formattedSpeed"
          @change="onChangeSpeed"
      />
      <UInput maxlength="4" class="ml-2 text-center" size="2xs" v-model="speedStateInput" :ui="{ rounded: 'xl w-14' }" />
    </div>
  </UFormGroup>
</template>

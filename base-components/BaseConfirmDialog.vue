<template>
  <UModal v-model="confirmDialog.open" prevent-close>
    <UCard
      :ui="{
        ring: '',
        divide: 'divide-y divide-gray-100 dark:divide-gray-800',
      }"
    >
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h3
            class="text-base font-semibold leading-6 text-gray-900 dark:text-white"
          >
            {{ confirmDialog.title || $t("Confirmation") }}
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="confirmDialog.open = false"
          />
        </div>
        <div class="break-words">
          {{ confirmDialog.message || $t("Are you sure?") }}
        </div>
        <div class="flex flex-inline items-center justify-end space-x-2">
          <UButton class="px-6" variant="soft" color="gray" @click="confirmDialog.onCancel() || dialogsStore.onCloseConfirmDialog()">
            {{ confirmDialog.cancelButtonText || $t("No") }}
          </UButton>
          <UButton class="min-w-28 justify-center" color="red" @click="onConfirm">
            {{ confirmDialog.confirmButtonText || $t("Yes") }}
          </UButton>
        </div>
      </div>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useDialogsStore } from '~/stores/dialogs'

const dialogsStore = useDialogsStore()
const { confirmDialog } = storeToRefs(dialogsStore)

const onConfirm = () => {
  confirmDialog.value.onConfirm()
  dialogsStore.onCloseConfirmDialog()
}
</script>

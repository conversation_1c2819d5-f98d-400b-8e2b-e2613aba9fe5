<template>
    <!-- Main modal -->
    <VueFinalModal
        class="fixed bottom-10 left-0 right-0 z-50 flex p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-full max-h-full"
    >
        <div class="relative w-full max-w-md max-h-full mx-auto">
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <button
                    @click="emit('cancel')"
                    type="button"
                    class="absolute top-3 right-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-800 dark:hover:text-white"
                    data-modal-hide="popup-modal"
                >
                    <svg
                        aria-hidden="true"
                        class="w-5 h-5"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            fill-rule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    <span class="sr-only">Close modal</span>
                </button>
                <div class="p-6 text-center">
                    <svg
                        aria-hidden="true"
                        class="mx-auto mb-4 text-gray-400 w-14 h-14 dark:text-gray-200"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        ></path>
                    </svg>
                    <h3 class="mb-2 text-lg font-normal text-gray-500 dark:text-gray-400">{{ props.title }}</h3>
                    
                    <div class="px-3 mb-6 text-lg font-normal text-gray-500 dark:text-gray-400">
                        <UCheckbox :label="$t('Replace the history audio file?')" v-model="isReplaceAudio" />
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 px-4 gap-4">
                        <button
                            @click="emit('cancel')"
                            type="button"
                            class="text-gray-500 justify-center bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"
                        >
                            {{ $t('No') }}
                        </button>
                        <button
                            @click="emit('confirm', isReplaceAudio)"
                            type="button"
                            class="text-white justify-center bg-primary-600 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 dark:focus:ring-primary-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center"
                        >
                            {{ $t("Yes") }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </VueFinalModal>
</template>

<script lang="ts" setup>
import { VueFinalModal } from 'vue-final-modal'
import { ref, onMounted, watch } from 'vue'
import { Modal } from 'flowbite'
const isReplaceAudio = ref(false)
const emit = defineEmits(['confirm', 'cancel'])
let modal = null
const props = defineProps({
    title: {
        type: String,
    },
})
</script>

import { storeToRefs } from 'pinia'
import { useDialogsStore } from '~/stores/dialogs'

export default defineNuxtPlugin((nuxtApp) => {
  const dialogsStore = useDialogsStore()

  const { confirmDialog } = storeToRefs(dialogsStore)
  nuxtApp.vueApp.directive('confirm', {
    mounted (el, { value }) {
      el.onclick = () => {
        confirmDialog.value.title = value.title
        confirmDialog.value.message = value.message
        confirmDialog.value.open = true
        confirmDialog.value.confirmButtonText = value.confirmButtonText
        confirmDialog.value.cancelButtonText = value.cancelButtonText
        confirmDialog.value.onConfirm = value.onConfirm
        confirmDialog.value.onCancel = value.onCancel
      }
    }
  })
})

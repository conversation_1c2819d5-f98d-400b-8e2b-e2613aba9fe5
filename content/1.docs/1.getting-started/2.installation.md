---
title: Installation
description: Get started with Nuxt UI Pro SaaS template.
---

## Quick Start

You can start a fresh new project with:

```bash [Terminal]
npx nuxi init -t github:nuxt-ui-pro/saas
```

or create a new repository from GitHub:

1. Open <https://github.com/nuxt-ui-pro/saas>
2. Click on `Use this template` button
3. Enter repository name and click on `Create repository from template` button
4. Clone your new repository
5. Install dependencies with your favorite package manager
6. Start development server

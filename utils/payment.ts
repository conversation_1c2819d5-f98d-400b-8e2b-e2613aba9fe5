import { PaymentStatus } from '@/stores/payments'

/**
 * Get payment status label, color and class
 * @param i18n - i18n instance
 * @param status - payment status
 * @returns - payment status label, color and class
 */
export const paymentStatus = (
    i18n: any,
    status: PaymentStatus | string
):
    | {
          label: string
          color: string
          class: string
          icon: string
      }
    | undefined => {
    switch (Number(status)) {
        case PaymentStatus.UNAVAILABLE:
            return {
                label: i18n.t('payment.status.unavailable'),
                color: 'text-gray-500 dark:text-gray-200',
                class: 'bg-gray-100 text-gray-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gray-900 dark:text-gray-300',
                icon: 'iconamoon:unavailable-thin',
            }
        case PaymentStatus.CREATED:
            return {
                label: i18n.t('payment.status.created'),
                color: 'text-blue-500',
                class: 'bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300',
                icon: 'eos-icons:subscriptions-created-outlined',
            }
        case PaymentStatus.COMPLETED:
            return {
                label: i18n.t('payment.status.completed'),
                color: 'text-green-500',
                class: 'bg-green-100 text-green-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300',
                icon: 'fa:check',
            }
        case PaymentStatus.FAILED:
            return {
                label: i18n.t('payment.status.failed'),
                color: 'text-red-500',
                class: 'bg-red-100 text-red-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300',
                icon: 'bx:error',
            }
        case PaymentStatus.CANCELED:
            return {
                label: i18n.t('payment.status.canceled'),
                color: 'text-gray-500 dark:text-gray-300',
                class: 'bg-gray-100 text-gray-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gray-900 dark:text-gray-300',
                icon: 'material-symbols:cancel-outline',
            }
        case PaymentStatus.PROCESSING:
            return {
                label: i18n.t('payment.status.processing'),
                color: 'text-yellow-500 dark:text-yellow-300',
                class: 'bg-yellow-100 text-yellow-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300',
                icon: 'arcticons:goodtime',
            }
        case PaymentStatus.REFUND:
            return {
                label: i18n.t('payment.status.refund'),
                color: 'text-gray-900 dark:text-gray-300',
                class: 'bg-gray-100 text-gray-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gray-900 dark:text-gray-300',
                icon: 'gridicons:refund',
            }
        case PaymentStatus.PARTIAL_PAID:
            return {
                label: i18n.t('payment.status.partial_paid'),
                color: 'text-green-500',
                class: 'bg-green-100 text-green-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300',
                icon: 'subway:part-of-circle-5',
            }
        default:
            return undefined
    }
}

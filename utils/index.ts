import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import UTC from 'dayjs/plugin/utc'
import CryptoJS from 'crypto-js'

dayjs.extend(UTC)
dayjs.extend(relativeTime)

export const formatDatetime = (datetime: string | undefined, format: string = 'YYYY/MM/DD HH:mm:ss'): string => {
    if (!datetime) {
        return ''
    }
    return dayjs.utc(datetime).local().format(format)
}

export const diffDatetime = (datetime: string | undefined): string => {
    if (!datetime) {
        return ''
    }
    return dayjs().diff(dayjs.utc(datetime).local(), 'second').toString()
}

export const constructBody = (data: any): any => {
    const body: any = {}
    for (const key in data) {
        if (data[key]) {
            body[key] = data[key]
        }
    }
    return body
}

export const aesDecrypt = (encryptedMessage: string, key: any, secondKey: any): any => {
    try {
        // Decode the Base64 encoded message
        const encryptedData = CryptoJS.enc.Base64.parse(encryptedMessage)

        // Extract the IV (first 16 bytes)
        const iv = CryptoJS.lib.WordArray.create(encryptedData.words.slice(0, 4))

        // Extract the ciphertext
        const ciphertext = CryptoJS.lib.WordArray.create(encryptedData.words.slice(4))
        let decrypted: any = null
        try {
            // Decrypt using AES-256-CBC

            decrypted = CryptoJS.AES.decrypt({ ciphertext: ciphertext }, CryptoJS.enc.Utf8.parse(key), {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7,
            })

            // Convert the decrypted WordArray to a UTF-8 string
            return JSON.parse(CryptoJS.enc.Utf8.stringify(decrypted))
        } catch (error) {
            console.log('decrypt failed, try second key')
            decrypted = CryptoJS.AES.decrypt({ ciphertext: ciphertext }, CryptoJS.enc.Utf8.parse(secondKey), {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7,
            })

            // Convert the decrypted WordArray to a UTF-8 string
            return JSON.parse(CryptoJS.enc.Utf8.stringify(decrypted))
        }
    } catch (error) {
        console.error('🚀 ~ aesDecrypt ~ error:', error)
    }
}

export const formatMsToTime = (ms: number): string => {
    // if ms < 1000, return ms
    if (ms < 1000) {
        return `${ms}ms`
    }
    // if ms < 60000, return seconds
    if (ms < 60000) {
        return `${Math.floor(ms / 1000)}s`
    }
    // if ms < 3600000, return minutes:seconds
    if (ms < 3600000) {
        return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`
    }
    // if ms < 86400000, return hours:minutes:seconds
    if (ms < 86400000) {
        return `${Math.floor(ms / 3600000)}h ${Math.floor((ms % 3600000) / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`
    }
    // if ms < 604800000, return days:hours:minutes:seconds
    if (ms < 604800000) {
        return `${Math.floor(ms / 86400000)}d ${Math.floor((ms % 86400000) / 3600000)}h ${Math.floor(
            (ms % 3600000) / 60000
        )}m ${Math.floor((ms % 60000) / 1000)}s`
    }
    return ms.toString() + 'ms'
}

// shorten the string with start and end
export const shortenString = (str: string, start: number = 7, end: number = 7): string => {
    // return "" if str is empty or undefined
    if (!str) {
        return ''
    }
    if (str.length <= start + end + 16) {
        return str
    }
    return str.slice(0, start) + '...' + str.slice(-end)
}

export const parseJson = (str: string): any => {
    try {
        return JSON.parse(str)
    } catch (error) {
        return str
    }
}

const formatUserTokens = (number: number) => {
    return new Intl.NumberFormat().format(number.toFixed(0))
}

const { emotionList } = useEmotions()
export const emotionObject = (emotion: string): any => {
    const inputTextProStore = useInputTextProStore()
    const emotions = inputTextProStore.emotions
    return emotions?.find((item: any) => item.name?.toLowerCase() === emotion?.toLowerCase())
}

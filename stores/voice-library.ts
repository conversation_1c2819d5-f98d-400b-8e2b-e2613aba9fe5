import { v4 as uuidv4 } from 'uuid'

const sampleRoot = 'https://cdn.ttsopenai.com/samples/openai_voices/'
// const sampleRoot = 'https://pub-f365373877dc460c96164c26d32ac908.r2.dev/samples/emotions/openai_voices/'
const OpenAIVoices = [
    {
        id: 'OA001',
        gender: 'Male',
        accent: 'English',
        audio_path: null,
        type: 'openai_voice',
        training_type: null,
        user_model_path: null,
        status: 2,
        updated_at: '2024-04-26T05:25:50',
        age: 'Young',
        speaker_id: 205,
        speaker_name: '<PERSON><PERSON>',
        description: 'Neutral, professional, and clear',
        sample_audio_path: sampleRoot + 'alloy.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '2024-04-26T05:25:50',
    },
    {
        id: 'OA002',
        gender: 'Male',
        accent: 'English',
        audio_path: null,
        type: 'openai_voice',
        training_type: null,
        user_model_path: null,
        status: 2,
        updated_at: '2024-04-26T05:25:50',
        age: 'Young',
        speaker_id: 206,
        speaker_name: '<PERSON>',
        description: 'Warm, friendly, and engaging',
        sample_audio_path: sampleRoot + 'echo.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '2024-04-26T05:25:50',
    },
    {
        id: 'OA003',
        gender: 'Male',
        accent: 'English',
        audio_path: null,
        type: 'openai_voice',
        training_type: null,
        user_model_path: null,
        status: 2,
        updated_at: '2024-04-26T05:25:50',
        age: 'Young',
        speaker_id: 207,
        speaker_name: 'Fable',
        description: 'Energetic, expressive, and engaging',
        sample_audio_path: sampleRoot + 'fable.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '2024-04-26T05:25:50',
    },
    {
        id: 'OA004',
        gender: 'Male',
        accent: 'English',
        audio_path: null,
        type: 'openai_voice',
        training_type: null,
        user_model_path: null,
        status: 2,
        updated_at: '2024-04-26T05:26:18',
        age: 'Old',
        speaker_id: 208,
        speaker_name: 'Onyx',
        description: 'Older, mature, and experienced',
        sample_audio_path: sampleRoot + 'onyx.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '2024-04-26T05:26:18',
    },
    {
        id: 'OA005',
        gender: 'Female',
        accent: 'English',
        audio_path: null,
        type: 'openai_voice',
        training_type: null,
        user_model_path: null,
        status: 2,
        updated_at: '2024-04-26T05:26:18',
        age: 'Young',
        speaker_id: 209,
        speaker_name: 'Nova',
        description: 'Young, energetic, and engaging',
        sample_audio_path: sampleRoot + 'nova.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '2024-04-26T05:26:18',
    },
    {
        id: 'OA006',
        gender: 'Female',
        accent: 'English',
        audio_path: null,
        type: 'openai_voice',
        training_type: null,
        user_model_path: null,
        status: 2,
        updated_at: '2024-04-26T05:26:18',
        age: 'Young',
        speaker_id: 210,
        speaker_name: 'Shimmer',
        description: 'Lively, vibrant, and dynamic',
        sample_audio_path: sampleRoot + 'shimmer.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '2024-04-26T05:26:18',
    },
    {
        id: 'OA007',
        gender: 'Male',
        accent: 'American',
        audio_path: null,
        type: 'openai_voice_pro',
        training_type: null,
        user_model_path: null,
        status: 1,
        updated_at: '12/21/2024 5:25:00',
        age: 'Young',
        speaker_id: 200,
        speaker_name: 'Ash',
        description: 'Enthusiastic, energetic, and lively',
        sample_audio_path: sampleRoot + 'ash.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '12/21/2024 5:25:00',
        badge: 'PRO',
    },
    // {
    //     id: 'OA008',
    //     gender: 'Male',
    //     accent: 'American',
    //     audio_path: null,
    //     type: 'openai_voice_pro',
    //     training_type: null,
    //     user_model_path: null,
    //     status: 1,
    //     updated_at: '12/21/2024 5:25:00',
    //     age: 'Young',
    //     speaker_id: 201,
    //     speaker_name: 'Ballad',
    //     description: 'Romantic, emotional, with a gentle rhythm',
    //     sample_audio_path: sampleRoot + 'ballad.mp3',
    //     premium: false,
    //     user_id: null,
    //     embedding_path: null,
    //     created_at: '12/21/2024 5:25:00',
    //     badge: 'PRO',
    // },
    // generate full object
    {
        id: 'OA009',
        gender: 'Female',
        accent: 'American',
        audio_path: null,
        type: 'openai_voice_pro',
        training_type: null,
        user_model_path: null,
        status: 1,
        updated_at: '12/21/2024 5:25:00',
        age: 'Young',
        speaker_id: 202,
        speaker_name: 'Coral',
        description: 'Cheerful, friendly, and community-oriented',
        sample_audio_path: sampleRoot + 'coral.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '12/21/2024 5:25:00',
        badge: 'PRO',
    },
    {
        id: 'OA010',
        gender: 'Female',
        accent: 'American',
        audio_path: null,
        type: 'openai_voice_pro',
        training_type: null,
        user_model_path: null,
        status: 1,
        updated_at: '12/21/2024 5:25:00',
        age: 'Young',
        speaker_id: 203,
        speaker_name: 'Sage',
        description: 'Wise, calm, and knowledgeable',
        sample_audio_path: sampleRoot + 'sage.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '12/21/2024 5:25:00',
        badge: 'PRO',
    },
    // {
    //     id: 'OA011',
    //     gender: 'Male',
    //     accent: 'American',
    //     audio_path: null,
    //     type: 'openai_voice_pro',
    //     training_type: null,
    //     user_model_path: null,
    //     status: 1,
    //     updated_at: '12/21/2024 5:25:00',
    //     age: 'Young',
    //     speaker_id: 204,
    //     speaker_name: 'Verse',
    //     description: 'Poetic, creative, and artistic',
    //     sample_audio_path: sampleRoot + 'verse.mp3',
    //     premium: false,
    //     user_id: null,
    //     embedding_path: null,
    //     created_at: '12/21/2024 5:25:00',
    //     badge: 'PRO',
    // },
]

// id;speaker_id;speaker_name;gender;age;accent;description;audio_path;sample_audio_path;type;premium;training_type;user_model_path;embedding_path;created_at;updated_at;user_id;status;training_status;used_credit
// OA007;200;Ash;Male;Young;American;Enthusiastic, energetic, and lively;NULL;samples/ash.mp3;openai_voice_pro;0;openai_voice;NULL;NULL;2024-12-21 05:25;2024-12-21 05:25;NULL;1;NULL;0
// OA008;201;Ballad;Male;Young;American;Romantic, emotional, with a gentle rhythm;NULL;samples/ballad.mp3;openai_voice_pro;0;openai_voice;NULL;NULL;2024-12-21 05:25;2024-12-21 05:25;NULL;1;NULL;0
// OA009;202;Coral;Female;Young;American;Cheerful, friendly, and community-oriented;NULL;samples/coral.mp3;openai_voice_pro;0;openai_voice;NULL;NULL;2024-12-21 05:25;2024-12-21 05:25;NULL;1;NULL;0
// OA010;203;Sage;Female;Young;American;Wise, calm, and knowledgeable;NULL;samples/sage.mp3;openai_voice_pro;0;openai_voice;NULL;NULL;2024-12-21 05:25;2024-12-21 05:25;NULL;1;NULL;0
// OA011;204;Verse;Male;Young;American;Poetic, creative, and artistic;NULL;samples/verse.mp3;openai_voice_pro;0;openai_voice;NULL;NULL;2024-12-21 05:25;2024-12-21 05:25;NULL;1;NULL;0
const OpenAIVoicesPro = [
    // {
    //     id: 'OA007',
    //     gender: 'Male',
    //     accent: 'American',
    //     audio_path: null,
    //     type: 'openai_voice_pro',
    //     training_type: null,
    //     user_model_path: null,
    //     status: 1,
    //     updated_at: '12/21/2024 5:25:00',
    //     age: 'Young',
    //     speaker_id: 200,
    //     speaker_name: 'Ash',
    //     description: 'Enthusiastic, energetic, and lively',
    //     sample_audio_path: sampleRoot + 'ash.mp3',
    //     premium: false,
    //     user_id: null,
    //     embedding_path: null,
    //     created_at: '12/21/2024 5:25:00',
    //     badge: 'PRO'
    // },
    {
        id: 'OA008',
        gender: 'Male',
        accent: 'American',
        audio_path: null,
        type: 'openai_voice_pro',
        training_type: null,
        user_model_path: null,
        status: 1,
        updated_at: '12/21/2024 5:25:00',
        age: 'Young',
        speaker_id: 201,
        speaker_name: 'Ballad',
        description: 'Romantic, emotional, with a gentle rhythm',
        sample_audio_path: sampleRoot + 'ballad.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '12/21/2024 5:25:00',
        badge: 'PRO',
    },
    // // generate full object
    // {
    //     id: 'OA009',
    //     gender: 'Female',
    //     accent: 'American',
    //     audio_path: null,
    //     type: 'openai_voice_pro',
    //     training_type: null,
    //     user_model_path: null,
    //     status: 1,
    //     updated_at: '12/21/2024 5:25:00',
    //     age: 'Young',
    //     speaker_id: 202,
    //     speaker_name: 'Coral',
    //     description: 'Cheerful, friendly, and community-oriented',
    //     sample_audio_path: sampleRoot + 'coral.mp3',
    //     premium: false,
    //     user_id: null,
    //     embedding_path: null,
    //     created_at: '12/21/2024 5:25:00',
    //     badge: 'PRO'
    // },
    // {
    //     id: 'OA010',
    //     gender: 'Female',
    //     accent: 'American',
    //     audio_path: null,
    //     type: 'openai_voice_pro',
    //     training_type: null,
    //     user_model_path: null,
    //     status: 1,
    //     updated_at: '12/21/2024 5:25:00',
    //     age: 'Young',
    //     speaker_id: 203,
    //     speaker_name: 'Sage',
    //     description: 'Wise, calm, and knowledgeable',
    //     sample_audio_path: sampleRoot + 'sage.mp3',
    //     premium: false,
    //     user_id: null,
    //     embedding_path: null,
    //     created_at: '12/21/2024 5:25:00',
    //     badge: 'PRO'
    // },
    {
        id: 'OA011',
        gender: 'Male',
        accent: 'American',
        audio_path: null,
        type: 'openai_voice_pro',
        training_type: null,
        user_model_path: null,
        status: 1,
        updated_at: '12/21/2024 5:25:00',
        age: 'Young',
        speaker_id: 204,
        speaker_name: 'Verse',
        description: 'Poetic, creative, and artistic',
        sample_audio_path: sampleRoot + 'verse.mp3',
        premium: false,
        user_id: null,
        embedding_path: null,
        created_at: '12/21/2024 5:25:00',
        badge: 'PRO',
    },
]

export const useVoiceLibraryStore = defineStore('voiceLibraryStore', {
    persist: [
        {
            paths: ['showFilter', 'filter', 'selectedVoiceLibrary', 'voiceLibrariesAll'],
            storage: window.localStorage,
        },
    ],
    state: () => ({
        showFilter: false,
        openAIVoices: OpenAIVoices,
        openAIVoicesPro: OpenAIVoicesPro,
        showVoiceLibrariesModal: false,
        filter: {
            keyword: '',
            voiceTypes: 'openai_voice',
            genders: [] as any,
            ages: [] as any,
            languages: [] as any,
            accents: [] as any,
        },
        loadings: {
            toggleFavorite: {} as any,
            fetchVoiceLibraryByType: {} as any,
            removeVoice: false as boolean,
        } as any,
        playingId: '' as string,
        voiceLibrariesAll: {
            openai_voice: OpenAIVoices,
            system_voice: [],
            user_voice: [],
            favorite_voice: [],
        } as any,
        filteredVoiceLibraries: [] as any[],
        selectedVoiceLibrary: null as any,
    }),

    getters: {
        hasFilter(state) {
            return Object.values(state.filter).some((value) => value.length > 0)
        },
        filterDescription(state) {
            // check if vue not setup
            return Object.values(state.filter)
                .map((value) => {
                    if (!value) return
                    if (typeof value === 'string') return [value]
                    return value?.map((value: any) => value.label).join(' / ')
                })
                .filter(Boolean)
                .join(' / ')
        },
        filterValues(state) {
            const filterValues = {
                keyword: state.filter.keyword,
                voiceTypes: state.filter.voiceTypes,
                genders: state.filter.genders?.map((obj: any) => obj.value),
                ages: state.filter.ages?.map((obj: any) => obj.value),
                languages: state.filter.languages?.map((obj: any) => obj.value),
                accents: state.filter.accents?.map((obj: any) => obj.value),
            }
            return filterValues
        },
        voiceLibraries(state) {
            return state.voiceLibrariesAll[state.filter.voiceTypes]
        },
        arrayVoiceLibraries(state) {
            return Object.values(state.voiceLibrariesAll).flat() as any[]
        },
        // filteredVoiceLibraries(): any {
        //     const voiceLibraries = this.voiceLibrariesAll[this.filter.voiceTypes]
        //     return voiceLibraries?.filter((voiceLibrary: any) => {
        //         const isMatchKeyword = voiceLibrary.speaker_name
        //             .toLowerCase()
        //             .includes(this.filterValues.keyword.toLowerCase())
        //         // const isMatchVoiceType =
        //         //     this.filterValues.voiceTypes.length === 0 ||
        //         //     this.filterValues.voiceTypes.includes(voiceLibrary.type)
        //         const isMatchGender =
        //             this.filterValues.genders.length === 0 ||
        //             this.filterValues.genders.includes(voiceLibrary.gender?.toLowerCase())
        //         const isMatchAge =
        //             this.filterValues.ages.length === 0 ||
        //             this.filterValues.ages.includes(voiceLibrary.age?.toLowerCase())
        //         const isMatchLanguage =
        //             this.filterValues.languages.length === 0 ||
        //             this.filterValues.languages.includes(voiceLibrary.language?.toLowerCase())
        //         const isMatchAccent =
        //             this.filterValues.accents.length === 0 ||
        //             this.filterValues.accents.includes(voiceLibrary.accent?.toLowerCase())
        //         return (
        //             isMatchKeyword &&
        //             // isMatchVoiceType &&
        //             isMatchGender &&
        //             isMatchAge &&
        //             isMatchLanguage &&
        //             isMatchAccent
        //         )
        //     })
        // },

        getVoiceLibraryById: (state) => (id: string) => {
            // return state.voiceLibraries.find((voiceLibrary: any) => voiceLibrary.id === id)
            return state.voiceLibrariesAll[state.filter.voiceTypes].find((voiceLibrary: any) => voiceLibrary.id === id)
        },
        canImportFromCsv(state) {
            return Object.values(state.loadings.fetchVoiceLibraryByType).every((loading) => loading === false)
        },
        myVoiceLibraries(state) {
            return state.voiceLibrariesAll.user_voice
        },
        getAllVoices(state) {
            return Object.values(state.voiceLibrariesAll).flat()
        },
        getVoiceTypeByVoiceId: (state) => (voice_id: string) => {
            // for all keys in voiceLibrariesAll and return the key that has the voice_id
            return Object.keys(state.voiceLibrariesAll).find((key) =>
                state.voiceLibrariesAll[key].some((voice: any) => voice.id === voice_id)
            )
        },
    },

    actions: {
        async fetchVoiceLibrary(): Promise<any> {
            // const appStore = useAppStore()
            const authStore = useAuthStore()
            if (!authStore.user) {
                if (this.filter.voiceTypes === 'openai_voice') {
                    this.voiceLibraries = this.openAIVoices
                    return true
                }
                this.voiceLibraries = []
                return false
            }
            if (this.filter.voiceTypes !== 'user_voice') {
                if (this.voiceLibrariesAll[this.filter.voiceTypes]?.length) {
                    return true
                }
            }
            try {
                this.loadings['fetchVoiceLibrary'] = true
                const voice_type =
                    this.filter.voiceTypes && this.filter.voiceTypes !== 'favorite_voice'
                        ? this.filter.voiceTypes
                        : undefined
                const { data }: { data: Ref<any> } = await useAPI(`voice-library/all`, {
                    method: 'GET',
                    server: false,
                    query: {
                        items_per_page: 500,
                        voice_type,
                        favorite: this.filter.voiceTypes === 'favorite_voice' ? 1 : undefined,
                        openai_voice_pro: true,
                    },
                })
                this.voiceLibraries = data.value?.result
                return true
            } catch (error) {
                return false
            } finally {
                this.loadings['fetchVoiceLibrary'] = false
            }
        },
        async fetchVoiceLibraryByType(voiceTypes: string, hardReload = false): Promise<any> {
            // const appStore = useAppStore()
            const authStore = useAuthStore()
            if (!authStore.user && voiceTypes === 'openai_voice') {
                this.voiceLibrariesAll[voiceTypes] = this.openAIVoices
                return true
            }
            if (this.voiceLibrariesAll[voiceTypes]?.length && !hardReload) {
                return true
            }
            try {
                this.loadings['fetchVoiceLibraryByType'][voiceTypes] = hardReload
                const voice_type = voiceTypes && voiceTypes !== 'favorite_voice' ? voiceTypes : undefined
                const { data }: { data: Ref<any> } = await useAPI(`voice-library/all`, {
                    method: 'GET',
                    server: false,
                    query: {
                        items_per_page: 500,
                        voice_type,
                        favorite: voiceTypes === 'favorite_voice' ? 1 : undefined,
                        openai_voice_pro: true,
                    },
                })
                this.voiceLibrariesAll[voiceTypes] = data.value?.result
                return true
            } catch (error) {
                return false
            } finally {
                this.loadings['fetchVoiceLibraryByType'][voiceTypes] = false
                this.filterVoiceLibraries()
                if (voiceTypes === 'user_voice') {
                    const myVoiceStore = useMyVoiceStore()
                    myVoiceStore.showNewSettingVoiceModal = false
                    myVoiceStore.showSelectVoiceTypeModal = false
                }
            }
        },
        filterVoiceLibraries(): any {
            const route = useRoute()
            const history = useHistoryStore()
            const voiceLibraries = this.voiceLibrariesAll[this.filter.voiceTypes]
            this.filteredVoiceLibraries = voiceLibraries?.filter((voiceLibrary: any) => {
                const isMatchKeyword =
                    voiceLibrary.speaker_name.toLowerCase().includes(this.filterValues.keyword.toLowerCase()) ||
                    (voiceLibrary.description &&
                        voiceLibrary.description.toLowerCase().includes(this.filterValues.keyword.toLowerCase())) ||
                    (voiceLibrary.id && voiceLibrary.id.toLowerCase().includes(this.filterValues.keyword.toLowerCase()))

                const isMatchGender =
                    this.filterValues.genders.length === 0 || this.filterValues.genders.includes(voiceLibrary.gender)
                const isMatchAge =
                    this.filterValues.ages.length === 0 || this.filterValues.ages.includes(voiceLibrary.age)
                const isMatchLanguage =
                    this.filterValues.languages.length === 0 ||
                    this.filterValues.languages.includes(voiceLibrary.language)
                const isMatchAccent =
                    this.filterValues.accents.length === 0 || this.filterValues.accents.includes(voiceLibrary.accent)
                const isUseOpenAIVoicesPro = route.name === 'emotion-text' || history.isRetryInputPro
                return (
                    isMatchKeyword &&
                    // isMatchVoiceType &&
                    isMatchGender &&
                    isMatchAge &&
                    isMatchLanguage &&
                    isMatchAccent &&
                    ((isUseOpenAIVoicesPro && voiceLibrary.training_type === 'openai_voice_pro') ||
                        voiceLibrary.training_type !== 'openai_voice_pro')
                )
            })
            // Make favorite
            this.makeFavorite()
        },
        makeFavorite() {
            const favoriteVoices = this.voiceLibrariesAll['favorite_voice']

            this.filteredVoiceLibraries = this.filteredVoiceLibraries?.map((voiceLibrary: any) => {
                const isFavorite = favoriteVoices?.length
                    ? favoriteVoices?.some((voice: any) => voice?.id === voiceLibrary?.id)
                    : voiceLibrary?.is_favorite
                return { ...voiceLibrary, is_favorite: isFavorite }
            })
        },
        async toggleFavorite(voice_id: string, is_favorite: boolean): Promise<any> {
            // const appStore = useAppStore()
            try {
                const url = is_favorite ? `voice-library/remove-favorite` : `voice-library/add-favorite`
                this.loadings.toggleFavorite[voice_id] = true
                const { data }: { data: Ref<any> } = await useAPI(url, {
                    method: is_favorite ? 'DELETE' : 'POST',
                    server: false,
                    body: {
                        voice_id,
                    },
                })
                this.voiceLibrariesAll[this.filter.voiceTypes || 'user_voice'] = this.voiceLibrariesAll[
                    this.filter.voiceTypes || 'user_voice'
                ].map((voice: any) => {
                    if (voice.id === voice_id) {
                        voice.is_favorite = !is_favorite
                    }
                    return voice
                })

                this.voiceLibrariesAll['user_voice'] = this.voiceLibrariesAll['user_voice'].map((voice: any) => {
                    if (voice.id === voice_id) {
                        voice.is_favorite = !is_favorite
                    }
                    return voice
                })
                const index = this.voiceLibrariesAll.favorite_voice.findIndex((voice: any) => voice.id === voice_id)
                if (index > -1) {
                    // Remove from favorite
                    this.voiceLibrariesAll.favorite_voice.splice(index, 1)
                } else {
                    this.voiceLibrariesAll.favorite_voice.push(
                        this.voiceLibrariesAll[this.filter.voiceTypes].find((voice: any) => voice.id === voice_id)
                    )
                }
                this.voiceLibrariesAll.favorite_voice = this.voiceLibrariesAll.favorite_voice.filter(
                    (voice: any) => voice?.is_favorite
                )
                return true
            } catch (error) {
                return false
            } finally {
                this.makeFavorite()
                this.loadings.toggleFavorite[voice_id] = false
            }
        },

        clearFilter() {
            this.filter = {
                ...this.filter,
                keyword: '',
                genders: [],
                ages: [],
                languages: [],
                accents: [],
            }
        },
        resetWhenLogout() {
            this.voiceLibrariesAll = {
                openai_voice: OpenAIVoices,
                system_voice: [],
                user_voice: [],
                favorite_voice: [],
            }
            this.filteredVoiceLibraries = []
            this.clearFilter()
            this.filterVoiceLibraries()
        },

        async removeVoiceById(voice_id: string): Promise<boolean> {
            try {
                if (!voice_id) return false
                this.loadings.removeVoice = true
                const { data, error } = await useAPI(`voice-library/custom-voice/${voice_id}/delete`, {
                    method: 'DELETE',
                    server: false,
                    // body: {
                    //     voice_id,
                    // },
                })
                if (error.value) {
                    return false
                }
                return true
            } catch (error) {
                return false
            } finally {
                this.loadings.removeVoice = false
            }
        },
    },
})

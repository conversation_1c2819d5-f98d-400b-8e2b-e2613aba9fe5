import { CommonResponse, ServerResponseError } from '~/interface/common-interface'

interface UserInfo {
    full_name: string | undefined
}

interface ChangePasswordInfo {
    password: string
    new_password: string
}

export const useUserStore = defineStore('userStore', {
    state: () => ({
        isError: false,
        updateInfoError: null as ServerResponseError | null,
        changePasswordError: null as ServerResponseError | null,
        isChangingPassword: false,
        isUpdatingUserInfo: false,
        loadings: {
            deleteAccount: false,
            updateUserSettings: {} as Record<string, boolean>,
        },
        isOpenDeleteAccountModal: false,
    }),

    actions: {
        async updateInfo(payload: UserInfo) {
            this.isUpdatingUserInfo = true
            const { data, error }: { data: Ref<CommonResponse>; error: any } = await useAPI('user/update-info', {
                method: 'PUT',
                body: payload,
                // lazy: true,
                server: false,
            })

            if (data.value && data.value.success) {
                this.isUpdatingUserInfo = false
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.updateInfoError = error.value.data as ServerResponseError
                this.isUpdatingUserInfo = false
                return false
            }
        },
        async changePassword(payload: ChangePasswordInfo) {
            this.isChangingPassword = true
            const { data, error }: { data: Ref<CommonResponse>; error: any } = await useAPI('user/change-password', {
                method: 'PUT',
                body: payload,
                // lazy: true,
                server: false,
            })

            if (data.value && data.value.success) {
                this.isChangingPassword = false
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.changePasswordError = error.value.data as ServerResponseError
                this.isChangingPassword = false
                return false
            }
        },

        async deleteAccount() {
            const appStore = useAppStore()
            appStore.loading = true
            const authStore = useAuthStore()
            try {
                this.loadings.deleteAccount = true
                const { data, error }: { data: Ref<CommonResponse>; error: any } = await useAPI('user/delete-me', {
                    method: 'DELETE',
                    // lazy: true,
                    server: false,
                })
                authStore.logout()
                this.isOpenDeleteAccountModal = false
                return true
            } catch (error) {
                console.log(error)
                return false
            } finally {
                this.loadings.deleteAccount = false
                appStore.loading = false
            }
        },

        async updateUserSettings(setting_key: string, setting_value: string) {
            try {
                this.loadings.updateUserSettings[setting_key] = true
                const { data, error }: { data: Ref<CommonResponse>; error: any } = await useAPI('user/update-setting', {
                    method: 'PUT',
                    // lazy: true,
                    server: false,
                    body: {
                        setting_key,
                        setting_value,
                    },
                })
                return true
            } catch (error) {
                console.log(error)
                return false
            } finally {
                this.loadings.updateUserSettings[setting_key] = false
            }
        },
    },
})

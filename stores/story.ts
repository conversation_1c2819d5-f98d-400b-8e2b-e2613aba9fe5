import { v4 as uuidv4 } from 'uuid'
import { debounce, remove } from 'lodash'
import srtParser2 from 'srt-parser-2'
import { AccessEnum, ModeSelectDurationEnum } from '~/types'
import axios from 'axios'
import Papa from 'papaparse'

export const useStoryStore = defineStore('storyStore', {
    persist: [
        {
            paths: ['showFilter'],
            storage: window.localStorage,
        },
    ],
    state: () => ({
        activeStory: null as any,
        stories: [] as any[],
        storiesElm: null as any,
        voiceLibraryDragArea: null as any,
        showFilter: false,
        showVoiceLibrariesModal: false,
        blockIndex: 0,
        srtFile: null as any,
        mode: 'edit',
        selecteds: [] as any[],
        isOpenBatchUpdateModal: false,
        showVoiceLibrariesForBatchModal: false,
        csvFile: null as any,
        loadings: {} as any,
        errors: {} as any,
        storyDetail: null as any,

        isOpenCreateStoryModal: false,
        storyOptions: {
            name: '',
            format: 'mp3',
            model: 'tts-1',
            output_channel: 'mono',
        } as any,
        maxLengthTextStory: 3850,
        isOpenSelectDurationModal: false,
        modeSelectDuration: ModeSelectDurationEnum.MANUAL,
        inputFile: null as any,
        isOpenStoryCloneFromHistoryModal: false,
    }),

    getters: {
        hasItemImportBySrt(): boolean {
            return this.stories.some((story) => story.byImportSrt)
        },
        blocks: (state): any[] => {
            console.log(state.stories)
            return state.stories.map((story) => {
                return {
                    name: story.name,
                    voice_id: story.voice_id,
                    model: state.storyOptions?.model,
                    input: story?.isSoundEffect ? '' : story.input,
                    speed: +story.speed,
                    silence_before: +story.silence_before,
                    duration: +story.duration,
                }
            })
        },
        totalDuration(): number {
            return this.stories.reduce((acc: any, story: any) => {
                return acc + +story.duration + +story.silence_before
            }, 0)
        },
        totalListeningTime(): number {
            return this.stories.reduce((acc: any, story: any) => {
                let duration = +story.duration || 0
                if (story.isSoundEffect) {
                    duration = 0
                }
                return acc + duration
            }, 0)
        },
        totalSilenceTime(): number {
            return this.stories.reduce((acc: any, story: any) => {
                let silence = +story.silence_before || 0
                if (story.isSoundEffect) {
                    silence += +story.duration || 0
                }
                return acc + silence
            }, 0)
        },
        totalTextLength(): number {
            return this.stories.reduce((acc: any, story: any) => {
                return acc + (story.isSoundEffect ? 0 : +story.input?.length)
            }, 0)
        },
        voicesSummary(): any {
            const voices = this.stories.map((story) => story.voice)
            // remove duplicate voices
            const uniqueVoices = voices.filter(
                (voice, index, self) => index === self.findIndex((v) => v?.id === voice?.id)
            )
            return uniqueVoices
        },
        isFirstBlockHasSilenceMoreThan2Minutes(): boolean {
            return +this.stories?.[0]?.silence_before > 120
        },
        isHasMoreThan100Blocks(): boolean {
            return this.stories.length > 100
        }
    },

    actions: {
        addStory(story?: any) {
            const voiceLibraryStore = useVoiceLibraryStore()
            const activeIndex =
                this.stories.findIndex((story) => story.id === this.activeStory?.id) || this.stories.length - 1
            this.blockIndex++
            const newStory = {
                id: uuidv4(),
                text: '',
                input: '',
                speed: 1,
                name: 'Block ' + (this.stories?.length + 1),
                silence_before: 0,
                silence_after: 0,
                voice_id: voiceLibraryStore.openAIVoices[0].id,
                voice: {
                    ...voiceLibraryStore.openAIVoices[0],
                    avatar: {
                        src: `assets/images/avatars/${voiceLibraryStore.openAIVoices[0].speaker_name.toLowerCase()}.svg`,
                    },
                },
                ...story,
            }
            if (activeIndex === -1) {
                this.stories.push(newStory)
            } else {
                this.stories.splice(activeIndex + 1, 0, newStory)
            }
            this.activeStory = newStory

            nextTick(() => {
                this.scrollToActiveStory()
            })
        },
        removeStory(id: string) {
            this.stories = this.stories.filter((story) => story.id !== id)
        },
        duplicateStory(story: any) {
            this.blockIndex++
            const index = this.stories.findIndex((s) => s.id === story.id)
            this.stories.splice(index + 1, 0, { ...story, id: uuidv4(), name: story.name + ' Copy' })
            this.activeStory = this.stories[index + 1]
            nextTick(() => {
                this.scrollToActiveStory()
            })
        },
        scrollToActiveStory() {
            if (!this.activeStory) return
            // scroll to active story by id
            const activeElement = document.getElementById(this.activeStory.id)
            if (activeElement) {
                activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
            }
        },
        downloadTemplate() {
            const aTag = document.createElement('a')
            const link = document.createTextNode('download')
            aTag.appendChild(link)
            aTag.href = '/template/story-maker-input.csv'
            aTag.download = 'story-maker-input.csv'
            aTag.click()
        },

        setActiveStory(voice: any) {
            if (voice) {
                this.activeStory = voice
                this.activeStory.voice_id = voice?.id
            }
        },

        setVoiceToActiveStory(voice_id: any) {
            const voiceLibraryStore = useVoiceLibraryStore()
            if (!this.activeStory) return
            const voice = voiceLibraryStore.voiceLibraries.find((v: any) => v.id === voice_id)
            this.activeStory.voice_id = voice_id
            this.activeStory.voice = {
                ...voice,
                avatar: {
                    src:
                        voice?.type === 'openai_voice'
                            ? `assets/images/avatars/${voice?.speaker_name?.toLowerCase()}.svg`
                            : '',
                },
            }

            this.stories = this.stories.map((story) => {
                if (story.id === this.activeStory.id) {
                    return {
                        ...story,
                        voice_id,
                        voice: {
                            ...voice,
                            avatar: {
                                src:
                                    voice?.type === 'openai_voice'
                                        ? `assets/images/avatars/${voice?.speaker_name?.toLowerCase()}.svg`
                                        : '',
                            },
                        },
                    }
                }
                return story
            })
        },
        removeAllStories() {
            this.stories = []
            this.selecteds = []
        },

        removeSelectedStories() {
            this.stories = this.stories.filter((story) => !this.selecteds.includes(story.id))
            this.selecteds = []
        },

        selectStory(story: any) {
            // if story is already selected, remove it from selecteds
            if (this.selecteds.includes(story?.id)) {
                this.selecteds = this.selecteds.filter((s) => s !== story?.id)
            } else {
                this.selecteds.push(story?.id)
            }
        },

        toggleSelectAll() {
            if (this.selecteds.length === this.stories.length) {
                this.selecteds = []
            } else {
                this.selecteds = this.stories.map((s) => s.id)
            }
        },

        unselectAll() {
            this.selecteds = []
        },

        selectAllOdd() {
            this.selecteds = this.stories.filter((_, index) => index % 2 === 0).map((s) => s.id)
        },

        selectAllEven() {
            this.selecteds = this.stories.filter((_, index) => index % 2 !== 0).map((s) => s.id)
        },

        selectByVoice(voice_id: string) {
            this.selecteds = this.stories.filter((s) => s.voice_id === voice_id).map((s) => s.id)
        },

        batchUpdateStorySettings(settings: any) {
            this.stories = this.stories.map((story) => {
                if (this.selecteds.includes(story.id)) {
                    return { ...story, ...settings, name: settings.name || story.name }
                }
                return story
            })
            this.isOpenBatchUpdateModal = false
        },

        importFromSrtFile(file: any) {
            this.inputFile = file
            const voiceLibraryStore = useVoiceLibraryStore()
            // read srt file content
            const reader = new FileReader()
            reader.onload = (e) => {
                const text = e.target?.result
                var parser = new srtParser2()
                var srt_array = parser.fromSrt(text as string)
                const stories = [] as any[]
                let silence_after = 0
                let silence_before = 0
                let duration = 0
                srt_array.forEach((srt: any, index: number) => {
                    // if text is empty, calculate silence duration and plus it to silence_before
                    if (!srt.text) {
                        silence_before += srt.endSeconds - srt.startSeconds
                        return
                    } else {
                        if (srt.text.length > this.maxLengthTextStory) {
                            srt.text = srt.text.slice(0, this.maxLengthTextStory)
                        }

                        if (index === 0) {
                            console.log('first block', srt)
                            silence_before = srt.startSeconds
                        } else if (index > 0) {
                            // silence_before is startSeconds - endSeconds of previous block
                            silence_before = srt.startSeconds - srt_array[index - 1].endSeconds
                        }

                        // [xxxx] is music or sound effect
                        const isSoundEffect = srt.text.match(/\[.*\]/)
                        duration = srt.endSeconds - srt.startSeconds
                        if (isSoundEffect) {
                            duration = duration + silence_before
                            silence_before = 0
                        } // if index is 0, silence_before is startSeconds

                        stories.push({
                            id: uuidv4(),
                            text: srt.text,
                            input: srt.text,
                            speed: 1,
                            name: isSoundEffect ? 'Sound effect ' : 'Block ' + (index + 1),
                            silence_before: silence_before.toFixed(4),
                            duration: duration.toFixed(3),
                            isSoundEffect,
                            voice_id: voiceLibraryStore.openAIVoices[0].id,
                            voice: {
                                ...voiceLibraryStore.openAIVoices[0],
                                avatar: {
                                    src: `assets/images/avatars/${voiceLibraryStore.openAIVoices[0].speaker_name.toLowerCase()}.svg`,
                                },
                            },
                            byImportSrt: true,
                        })
                        silence_before = 0
                    }
                })
                this.stories = stories
            }
            reader.readAsText(file)
        },

        importFromCsvFile(file: any) {
            const voiceLibraryStore = useVoiceLibraryStore()
            // read csv file content
            // Block name; Input text; Silence before; Duration; Speed; Voice

            Papa.parse(file, {
                complete: (result: any) => {
                    // remove header
                    result.data.shift()
                    this.stories = result.data.map((row: any, index: number) => {
                        const isSoundEffect = row[1]?.match(/\[.*\]/)
                        const voice_id = row[5]?.trim() || voiceLibraryStore.openAIVoices[0].id
                        const voice = voiceLibraryStore.arrayVoiceLibraries.find((v: any) => v.id === voice_id)
                        let input = row[1]?.trim() || ''
                        if (input.length > this.maxLengthTextStory) {
                            input = input.slice(0, this.maxLengthTextStory)
                        }
                        let speed = row[4]?.trim() || 1
                        if (speed < 0.25) {
                            speed = 0.25
                        } else if (speed > 4) {
                            speed = 4
                        }
                        return {
                            id: uuidv4(),
                            name: row[0]?.trim() || 'Block ' + (index + 1),
                            text: input,
                            input: input,
                            silence_before: row[2]?.trim() || 0,
                            duration: row[3]?.trim() || 0,
                            speed: speed,
                            voice_id: voice_id,
                            voice: {
                                ...voice,
                                avatar: {
                                    src: `assets/images/avatars/${voice?.speaker_name?.toLowerCase()}.svg`,
                                },
                            },
                            isSoundEffect,
                        }
                    })
                },
            })
        },

        async createStoryBlocks(): Promise<any> {
            // const appStore = useAppStore()
            const authStore = useAuthStore()
            const { canAccess } = authStore
            const translateStore = useTranslateStore()
            if (!canAccess(AccessEnum.IS_CREATE_STORY)) {
                this.isOpenCreateStoryModal = false
                navigateTo('/pricing')
                return
            }
            try {
                this.loadings['createStoryBlocks'] = true
                let uploadFileLink = {} as any

                if (this.inputFile) {
                    try {
                        uploadFileLink = await this.getUploadFileLink(
                            this.inputFile?.name || '',
                            this.inputFile?.size || 0
                        )
                    } catch (error: any) {}
                }

                const { url, s3_file_path } = uploadFileLink
                if (this.inputFile) {
                    if (!uploadFileLink) {
                    } else {
                        try {
                            const response = await axios.put(url, this.inputFile, {
                                headers: {
                                    'Content-Type': this.inputFile?.type || 'text/plain',
                                },
                                onUploadProgress: (progressEvent: any) => {
                                    const uploadProgress = Math.round(
                                        (progressEvent.loaded * 100) / progressEvent.total
                                    )
                                    console.log(uploadProgress)
                                },
                            })
                        } catch (error: any) {}
                    }
                }
                const blocks = this.blocks.map((block) => {
                    return {
                        ...block,
                        duration: this.modeSelectDuration === ModeSelectDurationEnum.AUTO ? 0 : block.duration,
                        model: this.storyOptions?.model,
                    }
                })
                const { data, error } = await useAPI(`stories`, {
                    method: 'POST',
                    server: false,
                    body: {
                        blocks,
                        name: this.storyOptions.name,
                        input_file_path: s3_file_path,
                        model: this.storyOptions?.model,
                        output_format: this.storyOptions.format,
                        output_channel: this.storyOptions.output_channel,
                    },
                })
                if (error.value) {
                    this.errors['createStoryBlocks'] = error.value?.data?.detail?.error_code || 'SYSTEM_ERROR'
                    return false
                }
                const response = data.value as any
                const { history_uuid } = response as { history_uuid: string }
                if (history_uuid) {
                    translateStore.setTtsResult(history_uuid, 'story-maker')
                }
                return response
            } catch (error: any) {
                this.errors['createStoryBlocks'] = error?.response?.data?.message || 'Error'
                const toast = useToast()
                toast.add({
                    id: 'createStoryBlocks',
                    title: 'Error',
                    description: error?.response?.data?.message || 'Error',
                })
                return false
            } finally {
                this.loadings['createStoryBlocks'] = false
            }
        },
        clearErrors() {
            console.log('Clear errors')
            this.errors = {}
        },
        setModeSelectDuration(mode: ModeSelectDurationEnum) {
            this.modeSelectDuration = mode
            this.isOpenSelectDurationModal = false
        },
        setSelectedFile(file: File | null) {
            // show select duration modal
            // this.isOpenSelectDurationModal = true
            this.setModeSelectDuration(ModeSelectDurationEnum.MANUAL)
            this.importFromSrtFile(file)
        },
        toggleModeSelectDuration() {
            this.modeSelectDuration =
                this.modeSelectDuration === ModeSelectDurationEnum.AUTO
                    ? ModeSelectDurationEnum.MANUAL
                    : ModeSelectDurationEnum.AUTO
        },
        async getUploadFileLink(file_name: string, file_size: number) {
            try {
                const { data, error } = await useAPI('get-upload-file-url', {
                    method: 'GET',
                    // lazy: true,
                    server: false,
                    params: {
                        file_name,
                        file_size,
                    },
                })

                if (data.value) {
                    return data.value
                }

                if (error.value?.data) {
                    throw error.value?.data
                }
                return false
            } catch (error) {
                throw error
            }
        },

        exportToCSV() {
            const nowDatetime = new Date().toISOString().replace(/:/g, '-')
            const csv = Papa.unparse({
                fields: ['Block name', 'Input text', 'Silence before', 'Duration', 'Speed', 'Voice'],
                data: this.stories.map((story) => {
                    return [story.name, story.input, story.silence_before, story.duration, story.speed, story.voice_id]
                }),
            })
            const blob = new Blob([csv], { type: 'text/csv' })
            const url = window.URL.createObjectURL(blob)
            const a = document.createElement('a')
            a.href = url
            a.download = `story-maker-output-${nowDatetime}.csv`
            a.click()
        },
    },
})

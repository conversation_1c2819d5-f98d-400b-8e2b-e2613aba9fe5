import { render, h, nextTick } from 'vue'
import SpeechStudioBreak from '@/components/speech-studio/SpeechStudioBreak.vue'

export enum SpeechStudioBreakType {
    STANDARD = 'standard',
    CUSTOM = 'custom',
    // TODO: Add more break types
}


export const useSpeechStudioV2Store = defineStore('speechStudioV2Store', {
    state: () => ({
        maxTextLength: 3000,
        openContextMenu: false,
        editorRef: null as HTMLDivElement | null,
        savedCursorPosition: 0, //
        selectedBreakValue: null as any,
        selectedBreakRef: null as HTMLSpanElement | null,
        // input content pass to API
        inputContent: '' as string,
        currentBreakRef: null as HTMLSpanElement | null,
        virtualElement: null as any,
    }),

    getters: {
        isSpeechStudioInputValid(): boolean {
            return true
        },
        textLength(): number {
            return 0
        },
    },

    actions: {
        saveCursorPosition() {
            this.savedCursorPosition = this.getCursorPosition()
            this.inputContent = this.extractContent()
            console.debug('this.inputContent:', JSON.stringify(this.inputContent))
        },

        addBreak(value: string, breakType: SpeechStudioBreakType) {
            const editor = this.editorRef
            if (!editor) return
            editor.focus()

            nextTick(() => {
                const cursorPosition = this.savedCursorPosition

                // Create break component
                const container = document.createElement('span')
                // container.classList.add('no-wrap')
                container.classList.add('capitalize')
                let prop = value
                switch (breakType) {
                    case SpeechStudioBreakType.STANDARD:
                        prop = `${value}`
                        break
                    case SpeechStudioBreakType.CUSTOM:
                        prop = `Custom ${formatMsToTime(Number(value))}`
                        break
                    default:
                        prop = value
                        break
                }
                const vnode = h(SpeechStudioBreak, {
                    value: prop,
                    dataBreak: value,
                    breakType: breakType,
                    onClick: (value: { value: string; breakRef: HTMLSpanElement | null; breakType: string }) => {
                        this.selectedBreakValue = value.value
                        this.selectedBreakRef = value.breakRef
                        this.currentBreakRef = value.breakRef

                        // trigger mouse right click
                        const event = new MouseEvent('contextmenu', {
                            bubbles: true,
                            cancelable: true,
                            clientX: 0,
                            clientY: 0,
                        })
                        editor.dispatchEvent(event)
                    },
                })
                render(vnode, container)

                // Insert break component in the DOM
                const range = document.createRange()
                const textNode = this.findTextNodeAtPosition(editor, cursorPosition)
                if (textNode) {
                    const nodePosition = this.getTextNodePosition(textNode)
                    const offset = Math.min(cursorPosition - nodePosition, textNode.textContent?.length || 0)
                    range.setStart(textNode, offset)
                    range.setEnd(textNode, offset)
                    range.insertNode(container)
                } else {
                    editor.appendChild(container)
                }

                // Update selection
                range.setStartAfter(container)
                range.setEndAfter(container)
                const selection = window.getSelection()
                selection?.removeAllRanges()
                selection?.addRange(range)


                editor.dispatchEvent(new Event('input', { bubbles: true }))
                // Update saved cursor position
                // editor.focus()
            })
        },

        extractContent() {
            const div = this.editorRef
            if (!div) return 'Div not found'

            let result = ''

            function processNode(node: Node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    result += node.textContent?.trim() || ''
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    if (node instanceof HTMLElement && node.classList.contains('speech-studio-break')) {
                        const dataBreak = node.querySelector('[data-break]')
                        if (dataBreak) {
                            result += ` break_data:${dataBreak.getAttribute('data-break')} `
                        }
                    } else {
                        node.childNodes.forEach(processNode)
                    }
                }
            }

            div.childNodes.forEach(processNode)
            return result.trim()
        },

        getCursorPosition() {
            const selection = window.getSelection()
            if (selection && selection.rangeCount > 0) {
                const range = selection.getRangeAt(0)
                return this.getTextNodePosition(range.startContainer) + range.startOffset
            }
            return this.getTextNodePosition(this.currentBreakRef as Node)
        },

        getTextNodePosition(node: Node): number {
            if (!this.editorRef) return 0
            let position = 0
            const walker = document.createTreeWalker(this.editorRef, NodeFilter.SHOW_TEXT)

            while (walker.nextNode() && walker.currentNode !== node) {
                position += walker.currentNode.textContent?.length || 0
            }

            return position
        },

        findTextNodeAtPosition(rootNode: Node, targetPosition: number): Node | null {
            let currentPosition = 0
            const walker = document.createTreeWalker(rootNode, NodeFilter.SHOW_TEXT)

            while (walker.nextNode()) {
                const nodeLength = walker.currentNode.textContent?.length || 0
                if (currentPosition <= targetPosition && currentPosition + nodeLength > targetPosition) {
                    return walker.currentNode
                }
                currentPosition += nodeLength
            }

            return null
        },
    },
})

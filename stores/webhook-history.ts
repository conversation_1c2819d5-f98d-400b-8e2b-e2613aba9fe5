export interface WebhookHistory {
    uuid: string
    user_id: number
    event_name: string
    http_response: string
    created_at: string
    id: number
    webhook_id: number
    event_data: string
    retry_count: null
    updated_at: string
}

interface FetchOptions {
    page?: number
    items_per_page?: number
}

const DEFAULT_OPTIONS: FetchOptions = {
    page: 1,
    items_per_page: 10,
}

export const useWebhookHistoryStore = defineStore('webhook-history', {
    state: () => ({
        webhookHistories: [] as WebhookHistory[],
        isShowDetailModal: false,
        total: 0,
    }),

    getters: {
        getWebhookHistories: (state) => state.webhookHistories,
    },

    actions: {
        async fetchWebhookHistories(options: Partial<FetchOptions> = {}) {
            const fetchOptions = {
                ...DEFAULT_OPTIONS,
                ...options
            }
            const { data } = await useAPI('/webhook_events', { method: 'GET', params: fetchOptions })
            const { result } = data.value as { result: WebhookHistory[] }
            this.webhookHistories = result
            const { total } = data.value as { total: number }
            this.total = total
        },
        setIsShowDetailModal(value: boolean) {
            this.isShowDetailModal = value
        },
        async retryWebhookHistories(uuids: string[]): Promise<boolean> {
            try {
                await Promise.all(uuids.map(uuid =>
                    useAPI(`/webhook_events/resend/${uuid}`, { method: 'GET' })
                ))
                await this.fetchWebhookHistories()
                return true
            } catch (error) {
                console.error(error)
                return false
            }
        },
    },
})

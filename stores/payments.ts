import { useAuthStore } from './auth'
const TOKEN_UNIT = 0
const TOKEN_UNIT_PRICE = 0 //$
export const TOKEN_PROD_ID = 'BC0001'

// PaymentStatus
export enum PaymentStatus {
    UNAVAILABLE = -1,
    CREATED = 1,
    COMPLETED = 2,
    FAILED = 3,
    CANCELED = 4,
    PROCESSING = 6,
    REFUND = 7,
    PARTIAL_PAID = 8,
}

interface LoadingObject {
    [key: string]: boolean
}

interface PaypalCreateOrderResponse {
    success: boolean
    order_id?: string
    order_uuid?: string
    approval_url?: string
    message: string
    error_code?: string
}

interface PaypalCreateSubscriptionResponse {
    success: boolean
    subscription_id?: string
    order_uuid?: string
    approval_url?: string
    message: string
    error_code?: string
    result?: Array<Product>
}

interface PaypalCompleteOrderResponse {
    success: boolean
}

export interface PaymentHistory {
    id: number
    uuid: string
    user_id: string
    product_id: string
    status: PaymentStatus
    amount: number
    amount_divide_100: number
    quantity: number
    type: string
    payment_method: string
    created_at: string
    updated_at: string
    isSuccess?: boolean
    order_product: {
        id: string
        name: string
        type: string
    }
}

interface OrderHistoriesResponse {
    success: boolean
    total: number
    result: Array<PaymentHistory> | []
}

export interface Product {
    id: string
    name: string
    type: number
    max_quantity: number
    max_monthly_total_file: number
    max_file_size: number
    price_divide_100: number
    base_credit: number
    bonus_credit: number
    days_of_valid: number
    paypal_plan_id: string | null
    usable_gpt_versions: Array<string>
    usable_models: string[]
    features: string[]
    price?: number
    special_bonus_credit?: number
}

export interface DiscountConfig {
    id: number
    payment_method: string
    discount_percentage: number
    extra_percentage: number
    start_at: string
    end_at: string
    type: string
}

interface DiscountConfigsResponse {
    success: boolean
    result: Array<DiscountConfig>
}

export const usePaymentsStore = defineStore('paymentsStore', {
    persist: [
        {
            paths: ['products'],
            storage: sessionStorage,
        },
    ],
    state: () => ({
        isOpen: false,
        paymentInfo: {} as any,
        loading: {} as LoadingObject,
        showPaymentModal: false,
        quickTopUpList: [1, 2, 5, 7, 10, 12, 15, 17, 20].map((num) => ({
            tokens: num * TOKEN_UNIT,
            price: num * TOKEN_UNIT_PRICE,
            quantity: num,
        })),
        tokenUnitPrice: TOKEN_UNIT_PRICE,
        topUpTokens: 69000000,
        tokenUnit: TOKEN_UNIT,
        orderUUID: '' as String,
        orderID: '' as String,
        moreCreditTimes: 0,
        // paymentHistories: [
        //     {
        //         id: 1,
        //         date: '2023-08-01 12:00:00',
        //         tokens: 10000000,
        //         price: 60,
        //         status: 'paid',
        //         txnId: '1234567890',
        //     },
        //     {
        //         id: 2,
        //         date: '2023-08-01 12:00:00',
        //         tokens: 10000000,
        //         status: 'failed',
        //         txnId: '1234567890',
        //     },
        //     {
        //         id: 3,
        //         date: '2023-08-01 12:00:00',
        //         tokens: 10000000,
        //         status: 'processing',
        //         txnId: '1234567890',
        //     },
        // ],
        paymentHistories: [] as Array<PaymentHistory> | [],
        paymentHistoriesTotal: 0,
        error: {},
        fetchOptions: {
            items_per_page: 5,
            page: 1,
        },
        isSubscribed: false,
        products: [] as Array<Product>,
        modelOptions: ['gpt-3.5', 'gpt-4', 'gpt-4-turbo'],
        discountConfigs: [] as Array<DiscountConfig>,
    }),

    getters: {
        topUpAmount: (state) => (state.topUpTokens / state.tokenUnit) * state.tokenUnitPrice,
        bonus: () => {
            return 0
        },
        planList: (state) => {
            return state.products
                .filter((item) => item.type === 1)
                .map((item: Product) => {
                    return {
                        // id: item.id,
                        product_id: item.id,
                        price: +item.price_divide_100,
                        token: +item.base_credit,
                        bonus: +(item.bonus_credit / item.base_credit) * 100,
                        plan_id: item.paypal_plan_id,
                        ...item,
                    }
                })
        },
        usableModelList: (state) => {
            const authStore = useAuthStore()
            const { user } = authStore
            const uableModels = state.products.find(
                (item) => item.type === 1 && user?.user_plan?.product?.id === item.id
            )?.usable_gpt_versions
            return uableModels || ['gpt-3.5']
        },
        noAdsProduct: (state) => {
            return state.products.find((item) => item.type === 2 && item.id === 'PP0003')
        },
        creditUnitPrice: (state) => {
            return state.products?.find((item) => item.id === TOKEN_PROD_ID)?.price_divide_100 || 15
        },
        specialBonusCredit: (state) => {
            return state.products?.find((item) => item.id === TOKEN_PROD_ID)?.special_bonus_credit || 0
        },
        buyTokenPlan: (state) => {
            return state.products.find((item) => item.id === TOKEN_PROD_ID)
        },
        discountConfigsByPaymentMethod: (state) => {
            return (paymentMethod: string) => {
                return state.discountConfigs.find(
                    (config) => config.payment_method === paymentMethod && config.type === 'BUY_CREDITS'
                )
            }
        },
        extraPercentageByPaymentMethod: (state) => {
            return (paymentMethod: string) => {
                const config = state.discountConfigs.find(
                    (config) => config.payment_method === paymentMethod && config.type === 'BUY_CREDITS'
                )
                return config?.extra_percentage || 0
            }
        },
    },

    actions: {
        async createPaypalOrder(payload: object) {
            this.loading['createPaypalOrder'] = true
            this.error = {}
            const { data, error }: { data: Ref<PaypalCreateOrderResponse>; error: any } = await useAPI(
                'order/paypal/create',
                {
                    method: 'POST',
                    server: false,
                    body: payload,
                }
            )
            this.loading['createPaypalOrder'] = false
            if (data.value?.success) {
                this.orderUUID = data.value?.order_uuid
                this.orderID = data.value?.order_id
                return data.value?.order_id
            }

            if (error.value && error.value.data) {
                this.error['createPaypalOrder'] = error.value.data
                return false
            }

            return false
        },

        async approvePaypalOrder() {
            try {
                this.loading['approvePaypalOrder'] = true
                this.error = {}
                const { data, error }: { data: Ref<PaypalCompleteOrderResponse>; error: any } = await useAPI(
                    `order/paypal/${this.orderUUID}/complete`,
                    {
                        method: 'PUT',
                        server: false,
                        query: {
                            order_uuid: this.orderUUID,
                        },
                    }
                )
                this.loading['approvePaypalOrder'] = false
                if (data.value) {
                    return data.value
                }

                if (error.value && error.value.data) {
                    this.error['approvePaypalOrder'] = error.value.data
                    return false
                } else {
                    this.error['approvePaypalOrder'] = {
                        message: 'Something went wrong',
                        error_code: 'SYSTEM_ERROR',
                    }
                }

                return false
            } catch (error) {
                this.loading['approvePaypalOrder'] = false
                this.error['approvePaypalOrder'] = {
                    message: 'Something went wrong',
                    error_code: 'SYSTEM_ERROR',
                }
                return false
            }
        },

        async cancelPaypalOrder() {
            this.loading['cancelPaypalOrder'] = true
            this.error = {}
            const { data }: { data: Ref<string> } = await useAPI(`order/paypal/${this.orderUUID}/cancel`, {
                method: 'PUT',
                server: false,
            })
            this.loading['cancelPaypalOrder'] = false
            if (data.value) {
                return data.value
            }
            return false
        },

        async getOrderHistories() {
            this.loading['getOrderHistories'] = true

            const { data }: { data: Ref<OrderHistoriesResponse> } = await useAPI(`orders`, {
                method: 'GET',
                server: false,
                query: this.fetchOptions,
            })
            this.loading['getOrderHistories'] = false

            if (data.value) {
                // this.paymentHistories = data.value.result.map((item) => {
                //     return {
                //         ...item,
                //         isSuccess: item.status === PaymentStatus.COMPLETED,
                //     }
                // })
                this.paymentHistories = data.value.result
                this.paymentHistoriesTotal = data.value.total
                return data.value
            }
            return false
        },

        async createSubscription(payload: object) {
            this.loading['createSubscription'] = true

            const { data, error }: { data: Ref<PaypalCreateSubscriptionResponse>; error: any } = await useAPI(
                'subscription/paypal/create',
                {
                    method: 'POST',
                    server: false,
                    body: payload,
                }
            )
            this.loading['createSubscription'] = false
            if (data.value?.success) {
                this.orderUUID = data.value?.subscription_id
                return data.value?.subscription_id
            }

            if (error.value && error.value.data) {
                this.error['createSubscription'] = error.value.data
                return false
            }

            return false
        },

        async cancelSubscription(payload: object) {
            this.loading['cancelSubscription'] = true
            this.error = {}
            const { data, error }: { data: Ref<PaypalCreateSubscriptionResponse>; error: any } = await useAPI(
                `subscription/paypal/${payload.subscription_id}/cancel`,
                {
                    method: 'PUT',
                    server: false,
                    body: payload,
                }
            )
            this.loading['cancelSubscription'] = false
            if (data.value?.success) {
                return true
            }

            if (error.value && error.value.data) {
                this.error['cancelSubscription'] = error.value.data
                return false
            }

            return false
        },

        async getAllProductsAndPlans() {
            // // Skip if products are already fetched
            // if (this.products.length > 0) {
            //     return true
            // }
            this.loading['getAllProductsAndPlans'] = true
            this.error = {}
            const { data, error }: { data: Ref<PaypalCreateSubscriptionResponse>; error: any } = await useAPI(
                `products`,
                {
                    method: 'GET',
                    server: false,
                }
            )
            this.loading['getAllProductsAndPlans'] = false
            if (data.value?.success) {
                this.products = data.value?.result || []

                const buyCreditProduct = this.products.find((item) => item.id === TOKEN_PROD_ID) as Product
                this.tokenUnitPrice = buyCreditProduct?.price_divide_100 || 0
                this.quickTopUpList = [1, 2, 5, 7, 10, 12, 15, 17, 20].map((num) => ({
                    tokens: num * buyCreditProduct?.base_credit,
                    price: num * this.tokenUnitPrice,
                    quantity: num,
                }))
                this.topUpTokens = buyCreditProduct?.base_credit
                this.tokenUnit = buyCreditProduct?.base_credit
                return true
            }

            if (error.value && error.value.data) {
                this.error['getAllProductsAndPlans'] = error.value.data
                return false
            }

            return false
        },

        async redeemGiftCard(gift_card_code: string, token?: string) {
            this.loading['redeemGiftCard'] = true
            this.error = {}
            const { data }: { data: Ref<string> } = await useAPI(`user/redeem-gift-card`, {
                method: 'POST',
                server: false,
                body: { gift_card_code, token },
            })
            this.loading['redeemGiftCard'] = false
            if (data.value) {
                return data.value
            }
            return false
        },

        async createOrderCryptomus(product_id: string, quantity: number) {
            this.loading['createOrderCryptomus'] = true
            this.error = {}
            const { data }: { data: Ref<string> } = await useAPI(`/order/cryptomus/create`, {
                method: 'POST',
                server: false,
                body: { product_id, quantity },
            })
            this.loading['createOrderCryptomus'] = false
            if (data.value) {
                return data.value
            }
            return false
        },

        async createOrderStripe(product_id: string, quantity: number) {
            this.loading['createOrderStripe'] = true
            this.error = {}
            const { data }: { data: Ref<string> } = await useAPI(`/order/stripe/create`, {
                method: 'POST',
                server: false,
                body: { product_id, quantity },
            })
            this.loading['createOrderStripe'] = false
            if (data.value) {
                return data.value
            }
            return false
        },

        async getDiscountConfigs(type: 'FREE_TRIAL_PLAN' | 'BUY_CREDITS') {
            this.loading['getDiscountConfigs'] = true
            this.error = {}
            const { data, error }: { data: Ref<DiscountConfigsResponse>; error: any } = await useAPI(
                'discount-configs',
                {
                    method: 'GET',
                    server: false,
                    query: { type },
                }
            )
            this.loading['getDiscountConfigs'] = false
            console.log('🚀 ~ getDiscountConfigs ~ data.value:', Object.values(data.value))
            if (data.value) {
                this.discountConfigs =  Object.values(data.value) || []
                return data.value
            }

            if (error.value && error.value.data) {
                this.error['getDiscountConfigs'] = error.value.data
                return false
            }

            return false
        },
    },
})

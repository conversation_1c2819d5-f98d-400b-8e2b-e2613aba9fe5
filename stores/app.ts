export const useAppStore = defineStore('appStore', {
    state: () => ({
        showTermsOfServiceModal: false,
        acceptedTermsOfService: false,
        darkMode: localStorage.getItem('nuxt-color-mode') === 'dark' ? true : false,
        showAppDrawer: false,
        showUserDrawer: false,
        showNotificationsDropdown: false,
        contactEmail: '<EMAIL>',
        // chatGPTVersion: localStorage.getItem('chatGPTVersion') || "3.5",
        chatGPTVersion: 'gpt-3.5',
        appVersion: '0.0.1',
        dropdownLanguage: null,
        showNotificationPopup: false,
        loading: false,

        showPopupReferral: false,

        isNotificationsSlideoverOpen: false,

        showPopupMobileApp: false,

        appleStoreLink: 'https://apps.apple.com/app/text-to-speech-ai-voices/id6736361290',
        googlePlayLink: 'https://play.google.com/store/apps/details?id=com.tts.text2speech.aivoices&pcampaignid=web_share',
    }),

    getters: {},

    actions: {
        setShowNotificationPopup(value: boolean) {
            this.showNotificationPopup = value || false
            localStorage.setItem('seenNotificationPopup', value.toString())
        },
        setShowTermsOfServiceModal(value: boolean) {
            this.showTermsOfServiceModal = value || false
        },
        toggleDarkMode() {
            const newDarkMode = !this.darkMode
            this.darkMode = newDarkMode || false
            localStorage.setItem('nuxt-color-mode', newDarkMode.toString())
        },
        setShowAppDrawer(value: boolean) {
            this.showAppDrawer = value || false
        },
        setShowUserDrawer(value: boolean) {
            this.showUserDrawer = value || false
        },
        setShowNotificationsDropdown(value: boolean) {
            this.showNotificationsDropdown = value || false
        },
        changeChatGPTVersion(value: string) {
            this.chatGPTVersion = value || 'gpt-3.5'
            localStorage.setItem('chatGPTVersion', value)
        },
    },
})

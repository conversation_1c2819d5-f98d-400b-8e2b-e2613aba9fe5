export const useSpeechStudioStore = defineStore('speechStudioStore', {
    state: () => ({
        input: '',
        currentCursor: 0,
        currentBlock: 0,
        currentBreak: -1,
        selectedBreaks: [] as number[],
        inputBlocks: [
            {
                text: '',
                type: 'text',
            },
        ] as any[],
        openContextMenu: false,
        customBreakValue: '',
        maxTextLength: 3000,
    }),

    getters: {
        currentBreakValue(): string {
            return this.inputBlocks[this.currentBreak]?.value
        },
        input() {
            let input = ''
            this.inputBlocks.forEach((block) => {
                switch (block.type) {
                    case 'text':
                        input += block.text
                        break
                    case 'break':
                        let breakTag = ''
                        if (block.value === 'custom') {
                            breakTag = `<break time="${block.time}ms" />`
                        } else {
                            breakTag = `<break strength="${block.value}" />`
                        }
                        input += `!@#${btoa(breakTag)}!@#`
                        break
                    default:
                        break
                }
            })
            // base64 encode the input
            return input
        },
        isSpeechStudioInputValid(): boolean {
            return this.inputBlocks.length > 0 && (this.inputBlocks[0].text.length > 0 || this.inputBlocks.length > 1)
        },
        textLength(): number {
            return this.inputBlocks.reduce((acc, block) => {
                if (block.type === 'text') {
                    return acc + block.text.length
                }
                return acc
            }, 0)
        },
        isContenteditable(): boolean {
            return this.textLength < this.maxTextLength
        },
        isEmpty(): boolean {
            return this.textLength === 0 && this.inputBlocks.length === 1
        }
    },

    actions: {
        clearInputBlocks() {
            this.inputBlocks = [
                {
                    text: '',
                    type: 'text',
                },
            ]
            this.currentCursor = 0
            this.currentBlock = 0
            this.currentBreak = -1
        },
        addBreakAfterCurrentCursor(value: string, time?: number) {
            // if current cursor is at the start of the text and there is break before it, do nothing
            if (
                this.currentCursor === 0 &&
                this.currentBlock > 0 &&
                this.inputBlocks[this.currentBlock - 1].type === 'break'
            ) {
                return
            }
            const block = this.inputBlocks[this.currentBlock]
            const text = block.text
            const before = text.slice(0, this.currentCursor)
            const after = text.slice(this.currentCursor)
            const newBlock = {
                text: after,
                type: 'text',
            }
            block.text = before
            this.inputBlocks.splice(this.currentBlock + 1, 0, {
                type: 'break',
                value: value,
                time: time,
            })
            this.inputBlocks.splice(this.currentBlock + 2, 0, newBlock)
            this.currentCursor = 0
            this.currentBlock += 2

            nextTick(() => {
                const element = document.getElementById('block_' + this.currentBlock)
                console.log('🚀 ~ nextTick ~ element:', element)
                element?.focus()
            })
        },

        removeBlock(index: number) {
            // if blocks length is 1, do nothing
            if (this.inputBlocks.length === 1) {
                return
            }
            this.inputBlocks.splice(index, 1)
        },
        addNewLineAfterCurrentCursor() {
            const block = this.inputBlocks[this.currentBlock]
            const text = block.text
            const before = text.slice(0, this.currentCursor)
            const after = text.slice(this.currentCursor)
            const newBlock = {
                text: after,
                type: 'text',
            }
            block.text = before
            this.inputBlocks.splice(this.currentBlock + 1, 0, {
                type: 'newline',
            })
            this.inputBlocks.splice(this.currentBlock + 2, 0, newBlock)
            this.currentCursor = 0
            this.currentBlock += 2

            nextTick(() => {
                const element = document.getElementById('block_' + this.currentBlock)
                console.log('🚀 ~ nextTick ~ element:', element)
                element?.focus()
            })
        },
    },
})

export const useAdBlockStore = defineStore('adBlockStore', {
    state: () => ({
        detectedAdBlock: false,
        showAdBlockModal: false,
        needCheckAdBlock: false,
    }),
    getters: {
        isAdBlocked: (state) => state.detectedAdBlock,
        isNeedCheckAdBlock: (state) => state.needCheckAdBlock,
    },
    actions: {
        setDetectedAdBlock(isAdBlocked: boolean) {
            this.detectedAdBlock = isAdBlocked
        },
        setShowAdBlockModal(show: boolean) {
            this.showAdBlockModal = show
        },
        async detectAdBlock() {
            const config = useRuntimeConfig()
            // 広告ブロックチェックをするかどうか
            if (!config.public.NUXT_DETECT_ADS_BLOCK) {
                return
            }
            const authStore = useAuthStore()
            this.clear()
            const googleAdUrl = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js'
            try {
                const res = await fetch(new Request(googleAdUrl), {
                    redirect: 'follow',
                })
                if (res.redirected) {
                    this.setDetectedAdBlock(true)
                    if (authStore.needDetectAdBlock) {
                        this.setShowAdBlockModal(true)
                        this.needCheckAdBlock = true
                    }
                } else {
                    this.setDetectedAdBlock(false)
                    this.setShowAdBlockModal(false)
                    this.needCheckAdBlock = false
                }
            } catch (e) {
                this.setDetectedAdBlock(true)
                if (authStore.needDetectAdBlock) {
                    this.setShowAdBlockModal(true)
                    this.needCheckAdBlock = true
                }
            } finally {
                console.log(`AdBlock Enabled: ${this.detectedAdBlock}`)
            }
        },
        // 広告ブロックモーダルを表示するか確認する
        async continueActionAfterCheckAdBlock() {
            //await this.detectAdBlock()
            return !this.needCheckAdBlock
        },

        clear() {
            this.detectedAdBlock = false
            this.showAdBlockModal = false
            this.needCheckAdBlock = false
        },
    },
})

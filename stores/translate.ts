import _ from 'lodash'
import { useAPI, useAxios } from '~/composables/useAPI'
import { useAppStore } from './app'
import axios from 'axios'
import { AccessEnum } from '~/types'
interface TranslateResultResponse {
    success: string
    result: string
    lang: string
    translation_uuid?: string
    warning?: string[]
}

interface TranslateOption {
    translateWritingStyle?: string
    translateDomain?: string
    translateTone?: string
    speed?: number
}

export const useTranslateStore = defineStore('translateStore', {
    persist: [
        {
            paths: ['voiceIdSelected'],
            storage: window.localStorage,
        },
    ],
    state: () => ({
        transFromLanguagesList: [] as string[],
        transToLanguagesList: [] as string[],
        selectedTransFromLang: 'Detect language',
        selectedTransToLang: 'English',
        mode: 'text',
        result: '',
        documentResult: '',
        resultUuid: '',
        inputText: '',
        inputFile: null as File | null,
        resultFileLink: '',
        isTranslating: false,
        isUploading: false,
        openSelectLanguage: false,
        selectLanguageFor: 'from',
        detectedLanguage: '',
        translateOptions: {
            translateDomain: '',
            translateTone: '',
            translateWritingStyle: '',
            speed: 1,
        } as TranslateOption,
        translateError: '',
        uploadProgress: 0,
        showLimitedWarning: true,
        showAccountVerifyWarning: false,
        customPrompt: '',
        showCustomPrompt: false,
        liked: false,
        disLiked: false,
        invalidFileExtension: [],
        isInvalidFile: false,
        filePassword: '',
        isFileHasPassword: false,
        translateStream: null,
        translatingSesionId: 0,
        translateTextResult: {} as { [key: number]: string },

        voiceOptions: [
            { value: 'alloy', text: 'alloy', description: 'Neutral, professional, and clear' },
            { value: 'echo', text: 'echo', description: 'Warm, friendly, and engaging' },
            { value: 'fable', text: 'fable', description: 'Energetic, expressive, and engaging' },
            { value: 'onyx', text: 'onyx', description: 'Older, mature, and experienced' },
            { value: 'nova', text: 'nova', description: 'Young, energetic, and engaging' },
            { value: 'shimmer', text: 'shimmer', description: 'Lively, vibrant, and dynamic' },
        ],
        voiceSelected: 'alloy',
        voiceIdSelected: 'OA001',
        voiceObject: {
            value: 'alloy',
            speaker_name: 'Alloy',
            description: 'Neutral, professional, and clear',
            avatar: {
                src: '/assets/images/avatars/alloy.svg',
            },
        } as any,
        modelOptions: [
            {
                value: 'tts-1',
                label: 'High Quality',
                icon: 'i-material-symbols-high-quality',
            },
            {
                value: 'tts-2',
                label: 'High Quality Plus',
                icon: 'i-material-symbols-high-quality-rounded',
            },
            {
                value: 'tts-1-hd',
                label: 'HD Quality',
                icon: 'i-icon-park-twotone-voice-one',
            },
        ],
        model: 'tts-1',

        outputFormatOptions: [
            { value: 'mp3', label: 'MP3', icon: 'i-carbon-mp3' },
            { value: 'wav', label: 'WAV', icon: 'i-bi-filetype-wav' },
            // { value: 'flac', label: 'FLAC', icon: 'i-bi-filetype-wav' },
        ],
        outputFormat: 'mp3',

        outputChannelOptions: [
            { value: 'mono', label: 'Mono', icon: 'i-material-symbols-speaker-outline' },
            { value: 'stereo', label: 'Stereo', icon: 'i-material-symbols-speaker-group-outline' },
        ],
        outputChannel: 'mono',

        speedOptions: [
            { value: 0.25, label: '0.25x' },
            { value: 0.5, label: '0.5x' },
            { value: 0.75, label: '0.75x' },
            { value: 1, label: 'Normal' },
            { value: 1.25, label: '1.25x' },
            { value: 1.5, label: '1.5x' },
            { value: 1.75, label: '1.75x' },
            { value: 2, label: '2x' },
            { value: 2.25, label: '2.25x' },
            { value: 2.5, label: '2.5x' },
            { value: 2.75, label: '2.75x' },
            { value: 3, label: '3x' },
            { value: 3.25, label: '3.25x' },
            { value: 3.5, label: '3.5x' },
            { value: 3.75, label: '3.75x' },
            { value: 4, label: '4x' },
        ],
        speed: 1,
        accent: '',

        showAudioPlayer: false,
        audioResult: {
            speed: 1,
            link: undefined as string | undefined,
            voice: 'alloy',
            model: 'tts-1',
        },

        loadings: {} as { [key: string]: boolean },

        isPlaying: false,
        supportFiles: [
            'docx',
            'xlsx',
            'pptx',
            'pdf',
            'epub',
            'mobi',
            'txt',
            'html',
            'odt',
            'ods',
            'odp',
            'azw',
            'azw3',
        ],

        ttsResult: {
            uuid: '',
            mode: '' as 'text' | 'documents' | 'story-maker' | '',
        } as any,

        turnstileToken: '',
        turnstileRef: null,
        isOpenProcess: false,
        processSuccess: false,
        processError: false,
        resultListener: null,

        s3_file_path: '',
        file_name_origin: '',
        file_size: 0,
    }),

    getters: {
        setShowAccountVerifyWarning: (state) => (value: boolean) => {
            state.showAccountVerifyWarning = value
        },
        isHasLimited: (state) => {
            const authStore = useAuthStore()
            const sizeFile = (state.inputFile as File)?.size / (1000 * 1000)
            return !authStore.isLoggedIn && (state.inputText?.length > 500 || sizeFile >= 0)
        },
        isHasLimitedPlan: (state) => {
            return ['PLAN_MAX_FILE_SIZE_EXCEED', 'PLAN_TOTAL_FILE_EXCEED'].includes(state.translateError)
        },
        isDetectLanguage: (state) => state.selectedTransFromLang === 'Detect language',
        showWarningDocument: (state) => {
            const authStore = useAuthStore()
            return state.inputFile && !authStore.isLoggedIn
        },
        isDisableTranslateButton: (state) => (routeName: string) => {
            return (
                (state.isTranslating && routeName == 'documents') ||
                (state.isInvalidFile && routeName == 'documents') ||
                (state.inputText?.trim()?.length === 0 && routeName == 'index') ||
                (state.inputFile === null && routeName == 'documents')
            )
        },
        isFileProtected: (state) => {
            return ['FILE_IS_PASSWORD_PROTECTED', 'FILE_PASSWORD_NOT_CORRECT'].includes(state.translateError)
        },
        isFilePasswordIncorrect: (state) => {
            return state.translateError === 'FILE_PASSWORD_NOT_CORRECT'
        },
        getVoice: (state) => (voice: string) => {
            return state.voiceOptions.find((item) => item.value === voice)
        },

        audioResultVoice: (state) => {
            return state.voiceOptions.find((item) => item.value === state.audioResult.voice)
        },

        voiceSelectedObj: (state) => {
            return state.voiceOptions.find((item) => item.value === state.voiceSelected)
        },

        modelSelectedObj: (state) => {
            return state.modelOptions.find((item) => item.value === state.model)
        },

        canCreateSpeech: (state) => {
            const route = useRoute()
            console.log('🚀 ~ route.name:', route.name)

            return (
                (state.inputText?.trim().length > 0 && route.name === 'index') ||
                (state.inputFile && route.name === 'documents' && state.inputFile?.size <= 100 * 1024 * 1024)
            )
        },
        isFileSizeExceed: (state) => {
            return state.inputFile?.size && state.inputFile?.size > 100 * 1024 * 1024
        },
        inputFileType: (state) => {
            if (!state.inputFile) {
                return ''
            }
            const fileExtension = state.inputFile.name.split('.').pop()
            return fileExtension
        },
        canShowAudioPlayerOnCurrentPage: (state) => {
            const route = useRoute()
            return route.name === 'documents' || route.name === 'story-maker' ? false : true
        },
    },

    actions: {
        setIsTranslating(value: boolean) {
            this.isTranslating = value
        },
        clearTranslateError() {
            console.log('🚀 ~ clearTranslateError')
            this.translateError = ''
        },
        setMode(mode: string) {
            this.mode = mode
        },
        setInputFile(file: File) {
            // check file type is docx, pptx, xlsx or pdf
            const fileExtension = file.name.split('.').pop()
            if (!this.supportFiles.includes(fileExtension as string)) {
                return
            }

            this.isInvalidFile = false
            this.inputFile = file
        },
        clearInputFile() {
            this.inputFile = null
            this.resultFileLink = ''
            this.documentResult = ''
            this.translateError = ''
            this.filePassword = ''
            this.isFileHasPassword = false
            this.uploadProgress = 0
            this.s3_file_path = ''
            this.file_name_origin = ''
            this.file_size = 0
        },
        clearTranslateText() {
            this.inputText = ''
            this.result = ''
            this.translateTextResult = {}
            this.translateOptions = {
                translateDomain: '',
                translateTone: '',
                translateWritingStyle: '',
            }
            this.translateError = ''
            this.detectedLanguage = ''
            this.customPrompt = ''
        },

        capitalizeFirstLetter(inputString: string) {
            return inputString.charAt(0).toUpperCase() + inputString.slice(1)
        },
        remove_origin_language_marks(text: string) {
            const match = text.match(/\{.*?\}/)
            let textWithoutSubstring = text
            if (match) {
                const substringToRemove = match[0]
                textWithoutSubstring = text.replace(substringToRemove, '')
            }

            return textWithoutSubstring
        },

        setInputText(text: string) {
            this.inputText = text
        },
        clearInputAndResultText() {
            this.result = ''
            this.translateTextResult = {}
            this.setInputText('')
            this.showLimitedWarning = true
            this.detectedLanguage = ''
            this.selectedTransFromLang = 'Detect language'
        },
        clearResultText() {
            this.result = ''
            this.translateTextResult = {}
        },
        setShowLimitedWarning(value: boolean) {
            this.showLimitedWarning = value
        },
        setRating(rating: string) {
            if (!rating) {
                this.liked = false
                this.disLiked = false
                return
            }
            this.liked = rating === 'thumbs_up'
            this.disLiked = rating === 'thumbs_down'
        },

        async getUploadFileLink(file_name: string, file_size: number) {
            try {
                const { data, error } = await useAPI('get-upload-file-url', {
                    method: 'GET',
                    // lazy: true,
                    server: false,
                    params: {
                        file_name,
                        file_size,
                    },
                })

                if (data.value) {
                    return data.value
                }

                if (error.value?.data) {
                    throw error.value?.data
                }

                return false
            } catch (error) {
                throw error
            }
        },

        async createSpeech(t: any, token?: string) {
            const route = useRoute()
            const authStore = useAuthStore()
            let mode = 'text'
            switch (route.name) {
                case 'documents':
                    mode = 'documents'
                    break
                case 'index':
                    mode = 'text'
                    break
                case 'speech-studio':
                    mode = 'speech-studio'
                    break
                default:
                    mode = 'text'
            }
            const { user, canAccess } = authStore
            const notificationsStore = useNotificationsStore()
            var _history_uuid = ''
            try {
                this.loadings['createSpeech'] = true
                this.processSuccess = false
                this.processError = false
                notificationsStore.needSyncData = false
                const toast = useToast()
                const router = useRouter()
                // sleep 2s
                if (mode === 'text') {
                    const apiUrl = user ? 'text-to-speech-stream' : 'public/text-to-speech-stream'
                    // const apiUrl = user ? 'text-to-speech' : 'public/text-to-speech'

                    if (user) {
                        const { data, error }: { data: Ref<any>; error: Ref<any> } = await useAPI(apiUrl, {
                            method: 'POST',
                            body: {
                                model: this.model,
                                // voice: this.voiceSelected,
                                speed: +this.speed,
                                input: this.inputText,
                                voice_id: this.voiceIdSelected,
                                accent: this.accent || undefined,
                                output_format: this.outputFormat,
                                output_channel: this.outputChannel,
                                token,
                            },
                            // responseType: 'blob',
                        })
                        // audio/mpeg
                        if (error.value) {
                            if (error.value?.statusCode === 402) {
                                this.translateError = this.showAccountVerifyWarning
                                    ? 'NOT_VERIFY_ACCOUNT'
                                    : error.value?.data?.detail?.error_code
                                this.loadings['createSpeech'] = false
                                this.processError = true
                                return
                            }

                            // check if error detail error_code[0] is timeout-or-duplicate, reset turnstile token and try again
                            // if (error.value?.data?.detail?.error_code === 'timeout-or-duplicate') {
                            //     this.turnstileRef?.reset()
                            //     this.loadings['createSpeech'] = false
                            //     return
                            // }
                            throw error.value
                        }

                        this.translateError = ''

                        this.ttsResult = data.value
                        this.ttsResult.mode = 'text'
                        // check is voice is system voice
                        const voiceLibraryStore = useVoiceLibraryStore()
                        const voiceObject = voiceLibraryStore.arrayVoiceLibraries.find(
                            (voice) => voice.id === this.voiceIdSelected
                        )
                        toast.add({
                            id: 'create-speech',
                            color: 'primary',
                            title: t('Success'),
                            description: t('Vocalization created successfully, you can check the result in history.'),
                            actions: [
                                {
                                    label: t('Go to history'),
                                    variant: 'solid',
                                    color: 'primary',
                                    click: () => {
                                        router.push({ name: 'history', query: { id: data.value?.uuid } })
                                        toast.remove('create-speech')
                                    },
                                },
                            ],
                            timeout: 30000,
                            icon: 'i-ooui:success',
                        })
                        this.loadings['createSpeech'] = false
                        this.processSuccess = true
                        _history_uuid = data.value?.uuid
                        return { uuid: data.value?.uuid }
                    } else {
                        const { data, error }: { data: Ref<any>; error: Ref<any> } = await useAPI(apiUrl, {
                            method: 'POST',
                            body: {
                                model: this.model,
                                // voice: this.voiceSelected,
                                speed: +this.speed,
                                input: this.inputText,
                                voice_id: this.voiceIdSelected,
                                accent: this.accent || undefined,
                                output_format: this.outputFormat,
                                output_channel: this.outputChannel,
                                token,
                            },
                            responseType: 'blob',
                        })
                        // audio/mpeg
                        if (error.value) {
                            if (error.value?.statusCode === 402) {
                                this.translateError = 'NOT_ENOUGH_CREDIT'
                                this.loadings['createSpeech'] = false
                                this.processError = true
                                return
                            }
                            throw error.value
                        }
                        this.translateError = ''
                        const url = URL.createObjectURL(data.value as Blob)

                        this.audioResult = {
                            speed: this.speed,
                            link: url,
                            voice: this.voiceSelected,
                            model: this.model,
                        }
                        this.showAudioPlayer = true
                        this.loadings['createSpeech'] = false
                        this.processSuccess = true
                        _history_uuid = data.value?.uuid
                        return { uuid: data.value?.uuid }
                    }

                    // parse to url
                    // const url = URL.createObjectURL(data.value as Blob)

                    // this.audioResult = {
                    //     speed: this.speed,
                    //     link: url,
                    //     voice: this.voiceSelected,
                    //     model: this.model,
                    // }
                    // this.showAudioPlayer = true
                    // /api/v1/history/ff025876-dfa5-11ee-a7ae-ba5da73d4d92
                } else if (mode === 'documents') {
                    if (!authStore.user) {
                        navigateTo('/signin')
                        this.loadings['createSpeech'] = false
                        return
                    }
                    if (!canAccess(AccessEnum.IS_UP_FILE_TO_CONVERT)) {
                        navigateTo('/pricing')
                        this.loadings['createSpeech'] = false
                        return
                    }
                    if (this.showWarningDocument || authStore.isUnverified) {
                        this.loadings['createSpeech'] = false

                        return
                    } else {
                        this.isUploading = true
                        const authStore = useAuthStore()
                        let uploadFileLink: any

                        if (!this.s3_file_path && !this.file_name_origin) {
                            try {
                                uploadFileLink = await this.getUploadFileLink(
                                    this.inputFile?.name || '',
                                    this.inputFile?.size || 0
                                )
                            } catch (error: any) {
                                this.loadings['createSpeech'] = false
                                this.translateError = error.detail?.error_code || 'SYSTEM_ERROR'
                                this.documentResult = 'ERROR'
                                this.processError = true
                                return false
                            }

                            const { http_method, url, s3_file_path, file_name_origin } = uploadFileLink
                            if (!uploadFileLink) {
                                this.loadings['createSpeech'] = false
                                this.translateError = 'SYSTEM_ERROR'
                                this.documentResult = 'ERROR'
                                this.processError = true
                                return
                            } else {
                                try {
                                    const response = await axios.put(url, this.inputFile, {
                                        headers: {
                                            'Content-Type': this.inputFile?.type || 'application/octet-stream',
                                        },
                                        onUploadProgress: (progressEvent: any) => {
                                            this.uploadProgress = Math.round(
                                                (progressEvent.loaded * 100) / progressEvent.total
                                            )
                                            console.log(this.uploadProgress)
                                        },
                                    })
                                } catch (error: any) {
                                    console.log(error)
                                    this.loadings['createSpeech'] = false
                                    this.translateError = error.response.data?.detail?.error_code || 'SYSTEM_ERROR'
                                    console.log('🚀 ~ createSpeech ~ error.response:', error.response)
                                    this.documentResult = 'ERROR'
                                    this.processError = true
                                    return false
                                }
                            }

                            this.s3_file_path = s3_file_path
                            this.file_name_origin = file_name_origin
                        }
                        try {
                            const { data } = await useAPI('tts-document-s3', {
                                method: 'POST',
                                body: {
                                    s3_file_path: this.s3_file_path || '',
                                    file_name_origin: this.file_name_origin || '',
                                    model: this.model,
                                    // voice: this.voiceSelected,
                                    speed: this.speed + '',
                                    file_password: this.filePassword,
                                    voice_id: this.voiceIdSelected,
                                    accent: this.accent || undefined,
                                    output_format: this.outputFormat,
                                    output_channel: this.outputChannel,
                                },
                            })

                            if (data.value) {
                                console.log(data.value)
                                this.loadings['createSpeech'] = false
                                this.uploadProgress = 0
                                this.documentResult = 'STARTED'
                                const { history_uuid } = data.value as { history_uuid: string }
                                this.setTtsResult(history_uuid, 'documents')

                                toast.add({
                                    id: 'create-speech',
                                    color: 'primary',
                                    title: t('Success'),
                                    description: t(
                                        'Vocalization created successfully, you can check the result in history.'
                                    ),
                                    actions: [
                                        {
                                            label: t('Go to history'),
                                            variant: 'solid',
                                            color: 'primary',
                                            click: () => {
                                                router.push({
                                                    name: 'history',
                                                    query: { id: data.value?.history_uuid },
                                                })
                                                toast.remove('create-speech')
                                            },
                                        },
                                    ],
                                    timeout: 30000,
                                    icon: 'i-ooui:success',
                                })
                                this.processSuccess = true
                                _history_uuid = history_uuid
                                return { uuid: history_uuid }
                            }
                        } catch (error: any) {
                            console.log(error)
                            this.loadings['createSpeech'] = false
                            this.translateError = error.response.data?.detail?.error_code || 'SYSTEM_ERROR'
                            this.documentResult = 'ERROR'
                            this.processError = true
                            return false
                        } finally {
                            this.isUploading = false
                            this.loadings['createSpeech'] = false
                        }
                    }
                } else if (mode === 'speech-studio') {
                    const apiUrl = 'text-to-speech-ssml'
                    const speechStudioStore = useSpeechStudioStore()
                    if (user) {
                        const { data, error }: { data: Ref<any>; error: Ref<any> } = await useAPI(apiUrl, {
                            method: 'POST',
                            body: {
                                model: this.model,
                                speed: +this.speed,
                                input: speechStudioStore.input,
                                voice_id: this.voiceIdSelected,
                                accent: this.accent || undefined,
                                output_format: this.outputFormat,
                                output_channel: this.outputChannel,
                                token,
                            },
                        })
                        // audio/mpeg
                        if (error.value) {
                            if (error.value?.statusCode === 402) {
                                this.translateError = this.showAccountVerifyWarning
                                    ? 'NOT_VERIFY_ACCOUNT'
                                    : error.value?.data?.detail?.error_code
                                this.loadings['createSpeech'] = false
                                this.processError = true
                                return
                            }

                            // check if error detail error_code[0] is timeout-or-duplicate, reset turnstile token and try again
                            // if (error.value?.data?.detail?.error_code === 'timeout-or-duplicate') {
                            //     this.turnstileRef?.reset()
                            //     this.loadings['createSpeech'] = false
                            //     return
                            // }
                            throw error.value
                        }

                        this.translateError = ''

                        this.ttsResult = data.value
                        this.ttsResult.mode = 'text'
                        toast.add({
                            id: 'create-speech',
                            color: 'primary',
                            title: t('Success'),
                            description: t('Vocalization created successfully, you can check the result in history.'),
                            actions: [
                                {
                                    label: t('Go to history'),
                                    variant: 'solid',
                                    color: 'primary',
                                    click: () => {
                                        router.push({ name: 'history', query: { id: data.value?.uuid } })
                                        toast.remove('create-speech')
                                    },
                                },
                            ],
                            timeout: 30000,
                            icon: 'i-ooui:success',
                        })
                        this.loadings['createSpeech'] = false
                        this.processSuccess = true
                        _history_uuid = data.value?.uuid
                        return { uuid: data.value?.uuid }
                        _history_uuid = data.value?.uuid
                        return { uuid: data.value?.uuid }
                    }
                }
            } catch (error: any) {
                console.log('🚀 ~ createSpeech ~ error:', error)
                this.loadings['createSpeech'] = false

                // check if error is not 403
                if (error?.statusCode !== 403) {
                    this.translateError = error.response?.data?.detail?.error_code || 'SYSTEM_ERROR'
                    const toast = useToast()
                    toast.add({
                        title: 'Error',
                        description: JSON.stringify(error.response?.data) || error.message,
                    })
                    this.processError = true
                } else {
                }
            } finally {
                this.isUploading = false
                const authStore = useAuthStore()
                authStore.syncUserTokenInfo()

                // clear result listener first
                if (this.resultListener) {
                    clearInterval(this.resultListener)
                }

                this.resultListener = setInterval(() => {
                    if (_history_uuid) {
                        const historyStore = useHistoryStore()
                        historyStore.fetchHistoryById(_history_uuid, false)
                    }

                    if (this.ttsResult?.uuid) {
                        this.fetchTtsResult()
                    }
                }, 30000)
            }
        },

        async fetchTtsResult() {
            try {
                const translation_uuid = this.ttsResult?.uuid
                this.loadings['createSpeech'] = true
                const { data }: { data: Ref<any> } = await useAPI('history/' + translation_uuid, {
                    method: 'GET',
                    // lazy: true,
                    server: false,
                })
                if (this.ttsResult.mode === 'text') {
                    this.audioResult = {
                        speed: this.speed,
                        link: data.value?.media_url,
                        voice: this.voiceSelected,
                        model: this.model,
                    }
                    if (data.value?.media_url) {
                        this.showAudioPlayer = true
                    }
                    clearInterval(this.resultListener)
                }
            } catch (error: any) {
            } finally {
                this.loadings['createSpeech'] = false
                this.processSuccess = true
            }
        },

        setTtsResult(uuid: string, mode: 'text' | 'documents' | 'story-maker') {
            if (this.ttsResult) {
                this.ttsResult.uuid = uuid
                this.ttsResult.mode = mode
            }
        },

        clearAudioResult() {
            this.audioResult = {
                speed: 1,
                link: undefined,
                voice: 'alloy',
                model: 'tts-1',
            }
            this.ttsResult = {
                uuid: '',
                mode: '',
            }
        },

        resetTurnstileRef() {
            this.turnstileRef?.reset()
        },

        resetWhenLogout() {
            this.clearInputAndResultText()
            this.clearInputFile()
            this.showAudioPlayer = false
            this.audioResult = {
                speed: 1,
                link: undefined,
                voice: 'alloy',
                model: 'tts-1',
            }
            this.model = 'tts-1'
        },
    },
})

import axios, { type AxiosProgressEvent } from 'axios'
import { MyVoiceType } from '~/types'
import type { MyVoice, MyVoiceUploadResponse } from '~/types'

export const useMyVoiceStore = defineStore('myVoiceStore', {
    state: () => ({
        showSelectVoiceTypeModal: false,
        showNewSettingVoiceModal: false,
        showPolicyModal: false,
        isAgreed: false,
        uploadProgress: 0,
        loadings: {
            btnGenerateMyVoice: false,
            uploadFile: false,
        },
        errorMessages: {
            uploadFile: '',
            generateMyVoice: '',
        },
        newMyVoice: {
            training_type: MyVoiceType.INSTANT_VOICE_CLONING,
            speaker_name: '' as string,
            gender: '' as string,
            age: '' as string,
            accent: '' as string,
            description: '' as string,
            accentStrength: 0 as number,
            inputFile: null as File | null,
            audio_path: '',
            file_duration: 0 as number,
        } as MyVoice,
    }),
    getters: {
        modalShowing: (state) =>
            state.showSelectVoiceTypeModal || state.showNewSettingVoiceModal || state.showPolicyModal,
        isInstantVoiceCloning: (state) => state.newMyVoice.training_type === MyVoiceType.INSTANT_VOICE_CLONING,
    },
    actions: {
        setVoiceTypeForNewMyVoice(voiceType: MyVoiceType) {
            this.clearErrorMessages()
            this.resetNewMyVoice()
            this.newMyVoice.training_type = voiceType
            this.showSelectVoiceTypeModal = false
            this.showNewSettingVoiceModal = true
        },
        async setInputFileForNewMyVoice(inputFile: File | null) {
            this.clearInputFileForNewMyVoice()
            this.newMyVoice.inputFile = inputFile
        },
        clearInputFileForNewMyVoice() {
            this.newMyVoice.inputFile = null
            this.newMyVoice.audio_path = ''
        },
        resetNewMyVoice() {
            this.newMyVoice = {
                training_type: MyVoiceType.INSTANT_VOICE_CLONING,
                speaker_name: '',
                gender: '',
                age: '',
                accent: '',
                description: '',
                inputFile: null,
                audio_path: '',
            }
            this.uploadProgress = 0
            this.isAgreed = false
            this.loadings.btnGenerateMyVoice = false
            this.loadings.uploadFile = false
        },
        setMyVoice(myVoice: MyVoice) {
            const { training_type, speaker_name, gender, age, accent, description } = myVoice
            this.newMyVoice = {
                training_type: this.newMyVoice.training_type,
                speaker_name,
                gender,
                age,
                accent,
                description,
                inputFile: this.newMyVoice.inputFile,
                audio_path: '',
            }
        },
        hideAllModals() {
            this.resetNewMyVoice()
            this.clearErrorMessages()
            this.clearInputFileForNewMyVoice()
            this.showNewSettingVoiceModal = false
            this.showSelectVoiceTypeModal = false
        },
        async uploadFile(file_name: string, file_size: number): Promise<MyVoiceUploadResponse> {
            const { data, error } = await useAPI('get-upload-voice-pro-url', {
                method: 'GET',
                // lazy: true,
                server: false,
                params: {
                    file_name,
                    file_size,
                },
            })
            if (error.value?.data) {
                const { detail } = error.value?.data
                const { error_message, error_code } = detail as { error_message: string; error_code: string }
                switch (error_code) {
                    case 'MAXIMUM_FILE_SIZE_EXCEED':
                        this.errorMessages.uploadFile = error_code
                        break
                    default:
                        this.errorMessages.uploadFile = error_message || 'Upload file failed'
                        break
                }
                this.loadings.uploadFile = false
                this.loadings.btnGenerateMyVoice = false
                throw error.value?.data
            }
            const { http_method, url, s3_file_path, file_name_origin } = data.value as MyVoiceUploadResponse
            try {
                const response = await axios.put(url, this.newMyVoice.inputFile, {
                    headers: {
                        'Content-Type': this.newMyVoice.inputFile?.type || 'application/octet-stream',
                    },
                    onUploadProgress: (progressEvent: AxiosProgressEvent) => {
                        if (progressEvent.total) {
                            this.uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                            console.log(this.uploadProgress)
                        }
                    },
                })
            } catch (error) {
                this.errorMessages.uploadFile = 'Upload file failed'
                this.loadings.uploadFile = false
                this.loadings.btnGenerateMyVoice = false
                throw error
            }
            return { http_method, url, s3_file_path, file_name_origin }
        },
        clearErrorMessages() {
            this.errorMessages.uploadFile = ''
            this.errorMessages.generateMyVoice = ''
        },

        async generateMyVoice(): Promise<boolean> {
            this.clearErrorMessages()
            this.loadings.btnGenerateMyVoice = true
            // Upload file
            this.loadings.uploadFile = true
            const res = await this.uploadFile(
                this.newMyVoice.inputFile?.name || '',
                this.newMyVoice.inputFile?.size || 0
            )
            const { http_method, url, s3_file_path, file_name_origin } = res as MyVoiceUploadResponse
            this.newMyVoice.audio_path = s3_file_path
            const { training_type, speaker_name, gender, age, accent, description, audio_path } = this.newMyVoice
            const body = {
                training_type,
                speaker_name,
                gender,
                age,
                accent,
                description,
                audio_path,
            }
            const { data, error } = await useAPI('voice-library/custom-voice/create', {
                method: 'POST',
                // lazy: true,
                server: false,
                body: constructBody(body),
            })
            if (error.value?.data) {
                const detail = error.value?.data?.detail
                const { error_code, error_message } = detail as { error_code: string; error_message: string }
                switch (error_code) {
                    case 'NOT_ENOUGH_CREDIT':
                    case 'NOT_ENOUGH_AND_LOCK_CREDIT':
                    case 'MAXIMUM_FILE_SIZE_EXCEED':
                        this.errorMessages.generateMyVoice = error_code
                        break
                    default:
                        this.errorMessages.generateMyVoice = 'Generate my voice failed'
                        break
                }
                this.loadings.uploadFile = false
                this.loadings.btnGenerateMyVoice = false
                throw error.value?.data
            }
            this.hideAllModals()
            const authStore = useAuthStore()
            authStore.syncUserTokenInfo()
            return true
        },
    },
})

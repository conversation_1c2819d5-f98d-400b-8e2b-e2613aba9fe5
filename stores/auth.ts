import type { Ref } from 'vue'
import type { ServerResponseError } from '~/interface/common-interface'
import { useNotificationsStore } from './notifications'
import { GoogleAuthProvider, signInWithPopup } from 'firebase/auth'
import dayjs from 'dayjs'
import { AccessEnum } from '~/types'
import axios from 'axios'

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))
interface RefreshTokenResponse {
    access_token: string
    refresh_token?: string
}
interface User {
    email: string
    is_active: boolean
    is_superuser: boolean
    full_name: string
    id: number
    uuid?: string
    status?: number
    user_credit?: UserToken
    user_plan?: UserPlan | null
    tokens: number
    current_plan: string
    isGuest?: boolean
    subscription_id?: string
    avatar?: string
    provider?: string
    user_feature?: UserFeature | {}
    referral_link?: string
    user_settings?: {
        email_ads_flag: 'on' | 'off'
    }
}

interface LoginInfo {
    username: string
    password: string
    remember_me: boolean
}
interface ForgotPasswordForm {
    email: string
}

interface SignUpInfo {
    full_name: string
    email: string
    password: string
    invitation_code?: string
    origin?: string
    ps_partner_key?: string
    ps_xid?: string
    gsxid?: string
    gspk?: string
}

interface LoginResponse {
    access_token: string
    type: string
    refresh_token?: string
}

interface UserPlan {
    id: number
    plan: {
        id: string
        name: string
    }
    product: {
        id: string
        name: string
        type: number
        features?: string[]
        usable_models?: string[]
    }
    is_subscribe: boolean
    expire_at: string | null
    subscription_id?: string
}

interface UserToken {
    plan_credit: number
    purchased_credit: number
    locked_credit: number
    available_credit: number
    expire_at: string | null
}

interface UserInfoResponse {
    email: string
    is_active: boolean
    is_superuser: boolean
    full_name: string
    id: number
    uuid: string
    status?: number
    user_credit?: UserToken
    user_plan?: UserPlan | null
    user_feature: UserFeature
}

interface UserFeature {
    id: number
    // 使用できるモデル
    is_use_hd: boolean
    // History ダウンロード可能か
    is_download: boolean
    // ファイルアップロード可能か
    is_up_file_to_convert: boolean
    // 使用できるモデル
    // usable_models: 'tts-1' | 'tts-1-hd'
    usable_models: string[]
    // 音声作成可能か
    is_create_voice: boolean
    // System voice可能か
    is_access_voice_lib: boolean
    // ストーリー作成可能か
    is_create_story: boolean
    // 有効期限
    expire_at: string | null
    // 広告表示可能か
    adsd: string
    // 広告表示可能か
    no_advertisement_expire_at: string
    is_no_advertisement: boolean
}

interface ForgotPasswordResponse {
    success?: boolean
    detail?: {
        error_code: string
        error_message: string
    }
}

interface ResetPasswordResponse {
    success?: boolean
    email?: string
    detail?: {
        error_code: string
        error_message: string
    }
}

export const useAuthStore = defineStore('authStore', {
    persist: [
        {
            paths: ['user'],
            storage: sessionStorage,
        },
        {
            paths: ['modals', 'referralInfo'],
            storage: localStorage,
        },
    ],
    state: () => ({
        isOpen: false,
        user: undefined as User | undefined,
        isLoggingIn: false,
        isSigningUp: false,
        isError: false,
        signInError: null as ServerResponseError | null,
        registerError: null as ServerResponseError | null,
        forgotPasswordError: null as ServerResponseError | null,
        resetPasswordError: null as ServerResponseError | null,
        resetPasswordEmail: '' as string,
        activateError: null as ServerResponseError | null,
        isResendingActivation: false,
        isExpired: false,
        showBetaNotification: dayjs().isAfter(dayjs(localStorage.getItem('beta_notification') || '2024-01-01')),
        showNoAdsModal: dayjs().isAfter(dayjs(localStorage.getItem('no_ads_notification') || '2024-01-01')),
        modals: {
            manual: true,
        },
        isSyncingCredit: false,
        refreshTokenInterval: null as any,

        loadings: {} as Record<string, boolean>,
        errors: {} as Record<string, any>,

        referralInfo: {} as any,
    }),

    getters: {
        isLoggedIn: (state) => state.user,
        isNotEnoughTokens: (state) => (state.user ? !state.user?.tokens || state.user?.tokens <= 0 : false),
        isUnverified: (state) => !state.user?.is_active,
        // isUnverified: (state) => true,
        currentSubscriptionPlan: (state) =>
            state.user?.user_plan?.subscription_id ? state.user?.user_plan?.product?.id : null,

        isUsingFreePlan: (state) => state.user?.current_plan === 'FP0001',
        // canDownloadAudio: (state) => state.user?.user_plan?.product?.features?.includes('is_download'),
        usableModels: (state) => {
            return state.user?.user_feature?.usable_models || ['tts-2', 'tts-1', 'tts-1-hd']
        },
        maxLengthText: (state) => {
            const runtimeConfig = useRuntimeConfig()
            const limited = runtimeConfig.public.limited
            // フリープランの場合は1000文字まで
            if (state.user?.current_plan === 'PP0001') return limited?.maxLengthTextPremium
            return state.user ? limited?.maxLengthTextFree : limited?.maxLengthEmotionTextNonLogin
        },
        maxLengthTextPro: (state) => {
            const runtimeConfig = useRuntimeConfig()
            const limited = runtimeConfig.public.limited

            // フリープランの場合は1000文字まで
            if (state.user?.current_plan === 'PP0001') return limited?.maxLengthEmotionTextPremium
            return state.user ? limited?.maxLengthEmotionTextFree : limited?.maxLengthEmotionTextNonLogin
        },
        isSuperUser: (state) => state.user?.is_superuser,
        isUsingPremiumPlan: (state) => state.user?.current_plan === 'PP0001',
        showAds: (state) => {
            const runtimeConfig = useRuntimeConfig()
            if (!runtimeConfig.public.enableAds) {
                return false
            }
            // return state.user?.current_plan !== 'PP0001'
            if (!state.user?.user_feature) {
                return true
            }
            const userFeature = state.user.user_feature as UserFeature
            return userFeature[AccessEnum.ADS_DISPLAY] !== 'o'
        },
        canUseStripe: (state) => {
            const runtimeConfig = useRuntimeConfig()
            // return if user id is greater than 500.000
            return state.user?.id > runtimeConfig.public.NUXT_STRIPE_USER_ID_GREATER_THAN || state.user?.is_superuser
        },
        // canUseStoryFeature: (state) => {
        //     return state.user?.current_plan === 'PP0001'
        // },
        // canUseDocumentFeature: (state) => {
        //     return state.user?.current_plan === 'PP0001'
        // },
        canAccess:
            (state) =>
            (access: AccessEnum): boolean => {
                if (!state.user?.user_feature) {
                    return false
                } else {
                    const userFeature = state.user.user_feature as UserFeature
                    switch (access) {
                        case AccessEnum.ADS_DISPLAY:
                            return userFeature[AccessEnum.ADS_DISPLAY] === 'o'
                        default:
                            return userFeature[access]
                    }
                }
            },
    },

    actions: {
        async login(loginInfo: LoginInfo) {
            this.isLoggingIn = true
            this.isError = false
            this.signInError = null

            const { data, error }: { data: Ref<LoginResponse>; error: any } = await useAPI('login-v2', {
                method: 'POST',
                body: loginInfo,
            })
            this.isLoggingIn = false

            if (data.value?.access_token) {
                localStorage.setItem('access_token', data.value.access_token)
                if (data.value?.refresh_token) {
                    localStorage.setItem('refresh_token', data.value.refresh_token)
                }

                return true
            }

            if (error.value && error.value.data) {
                console.log(error.value.data)
                this.signInError = error.value.data as ServerResponseError
                return false
            }
            //
            // this.isError = true
            // return false
        },
        async signup(signUpInfo: SignUpInfo) {
            this.isSigningUp = true
            this.isError = false
            this.registerError = null
            if (!signUpInfo.invitation_code) {
                delete signUpInfo.invitation_code
            }
            const { data, error }: { data: Ref<LoginResponse>; error: any } = await useAPI('signup', {
                method: 'POST',
                body: signUpInfo,
            })
            this.isSigningUp = false

            if (data.value?.access_token) {
                localStorage.setItem('access_token', data.value.access_token)
                // reset referralInfo
                this.referralInfo = {}
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.registerError = error.value.data as ServerResponseError
                return false
            }
            //
            // if (error.value && error.value.data?.detail) {
            //     this.registerError = error.value.data as ServerResponseError
            //     return false
            // }
            //
            // if (data.value) {
            //     return true
            // }
            //
            // this.isError = true
            // return false
        },
        async forgotPassword(payload: ForgotPasswordForm) {
            this.isError = false

            const { data, error }: { data: Ref<ForgotPasswordResponse>; error: any } = await useAPI(
                'password-recovery',
                {
                    method: 'POST',
                    body: payload,
                    // lazy: true,
                    server: false,
                }
            )

            if (data.value && data.value.success) {
                this.forgotPasswordError = null
                console.log('this.forgotPasswordError', this.forgotPasswordError)
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.forgotPasswordError = error.value.data as ServerResponseError
                return false
            }
        },
        async resetPassword(payload: object) {
            console.log('🚀 ~ resetPassword ~ payload:', payload)
            this.isError = false

            const { data, error }: { data: Ref<ResetPasswordResponse>; error: any } = await useAPI('password-reset', {
                method: 'PUT',
                body: payload,
                server: false,
            })

            if (data.value && data.value.success) {
                this.resetPasswordEmail = data.value.email || ''
                return true
            }

            if (error.value && error.value.data?.detail) {
                console.log(error.value.data?.detail)
                this.resetPasswordError = error.value.data as ServerResponseError
                return false
            }
        },
        logout() {
            const translateStore = useTranslateStore()
            const appStore = useAppStore()
            const voiceLibraryStore = useVoiceLibraryStore()
            const notificationsStore = useNotificationsStore()
            const inputTextProStore = useInputTextProStore()

            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')
            localStorage.removeItem('seenNotificationPopup')
            appStore.setShowNotificationPopup(false)
            translateStore.clearTranslateText()
            translateStore.clearInputFile()
            translateStore.setShowAccountVerifyWarning(false)
            translateStore.resetWhenLogout()
            voiceLibraryStore.resetWhenLogout()
            appStore.changeChatGPTVersion('gpt-3.5')
            if (inputTextProStore.vibe?.type === 'user_vibe') {
                inputTextProStore.vibe = null
            }
            const voiceTypes = ['openai_voice', 'system_voice', 'user_voice', 'favorite_voice']
            voiceTypes.forEach((type) => {
                voiceLibraryStore.fetchVoiceLibraryByType(type as string, true)
            })
            voiceLibraryStore.filterVoiceLibraries()
            this.user = undefined
            notificationsStore.alreadyGotNotifications = false
        },
        async fetchUserInfo() {
            try {
                // const notificationsStore = useNotificationsStore()

                if (this.user?.uuid) {
                    // if (!notificationsStore.alreadyGotNotifications) {
                    //     notificationsStore.getNotifications(this.user?.uuid || '')
                    // }
                    if (!this.user?.is_active) {
                        const translateStore = useTranslateStore()
                        translateStore.setShowAccountVerifyWarning(true)
                    } else {
                        return
                    }

                    // return
                }
                this.isLoggingIn = true
                const { data, error }: { data: Ref<any>; error: any } = await useAPI('me', {
                    method: 'GET',
                    // lazy: true,
                    server: false,
                })

                // if(error?.value?.statusCode === 403) {
                //     this.logout()
                //     window.location.href = "/"
                // }
                if (data.value) {
                    this.user = {
                        ...data.value,
                        current_plan: data.value.user_plan?.product?.id || data.value.user_plan?.plan?.id || '',
                        tokens: data.value.user_credit?.available_credit || 0,
                    }
                    console.log('🚀 ~ fetchUserInfo ~ user:', this.user)
                    const translateStore = useTranslateStore()

                    if (!data.value.is_active) {
                        translateStore.setShowAccountVerifyWarning(true)
                    } else {
                        translateStore.setShowAccountVerifyWarning(false)
                    }

                    this.showNoAdsModal = dayjs().isAfter(
                        dayjs(localStorage.getItem('no_ads_notification') || '2024-01-01')
                    )
                    localStorage.setItem('no_ads_notification', dayjs().add(1, 'day').toISOString())

                    const voiceTypes = ['openai_voice', 'system_voice', 'user_voice', 'favorite_voice']
                    const voiceLibraryStore = useVoiceLibraryStore()
                    voiceTypes.forEach((type) => {
                        voiceLibraryStore.fetchVoiceLibraryByType(type as string)
                    })
                    voiceLibraryStore.filterVoiceLibraries()
                }
            } catch (error) {
                console.log('🚀 ~ fetchUserInfo ~ error:', error)
            } finally {
                this.isLoggingIn = false
            }
        },

        async activateAccount(token: string) {
            this.isLoggingIn = true
            const { data, error }: { data: Ref<LoginResponse>; error: any } = await useAPI('activate-account', {
                method: 'PUT',
                body: { token },
                server: false,
            })
            this.isLoggingIn = false

            if (data.value?.access_token) {
                localStorage.setItem('access_token', data.value.access_token)
                return data.value.access_token
            }
            if (error.value && error.value.data?.detail) {
                this.activateError = error.value.data as ServerResponseError
                return false
            }
            this.isError = true
        },

        async syncUserTokenInfo() {
            this.isSyncingCredit = true
            const { data, error }: { data: Ref<UserInfoResponse>; error: any } = await useAPI('me', {
                method: 'GET',
                // lazy: true,
                server: false,
            })

            if (data.value) {
                this.user = {
                    ...data.value,
                    current_plan: data.value.user_plan?.product?.id || data.value.user_plan?.plan?.id || '',
                    tokens: data.value.user_credit?.available_credit || 0,
                }
            }
            this.isSyncingCredit = false
        },

        async resendVerifyEmail() {
            this.isResendingActivation = true
            const { data, error }: { data: Ref<string>; error: any } = await useAPI('resend-activation', {
                method: 'POST',
                // lazy: true,
                server: false,
                body: {
                    email: this.user?.email,
                },
            })
            this.isResendingActivation = false
        },

        setIsExpired(value: boolean) {
            this.isExpired = value
        },

        loginAsGuest() {
            this.user = {
                email: '<EMAIL>',
                user_credit: {
                    plan_token: 99999999,
                    purchased_token: 0,
                    locked_token: 0,
                    available_token: 99999999,
                },
                user_plan: {
                    id: 8,
                    plan: { id: 'PP0002', name: 'Professional plan', type: 1 },
                    is_subscribe: false,
                    expire_at: '2023-11-18T15:26:20',
                },
                is_active: true,
                is_superuser: false,
                full_name: 'Guest',
                status: 2,
                id: 8,
                uuid: '4217733a-218d-11ee-b112-0a58a9feac02',
                current_plan: 'PP0002',
                tokens: 99999999,
                isGuest: true,
            }
        },

        logoutGuest() {
            if (this.user?.isGuest) {
                this.user = undefined
            }
        },

        async processGoogleRedirectResult(
            result: any,
            token: string | undefined,
            invitation_code?: string | undefined
        ) {
            this.isLoggingIn = true
            this.isSigningUp = true
            this.isError = false
            this.signInError = null
            const appStore = useAppStore()
            appStore.loading = true

            try {
                console.log('Processing Google redirect result')
                const { data, error }: { data: Ref<any>; error: any } = await useAPI('google-login-v2', {
                    method: 'POST',
                    server: false,
                    body: {
                        google_access_token: token,
                        invitation_code: invitation_code,
                        user: result.user,
                    },
                })

                if (data.value?.access_token) {
                    localStorage.setItem('access_token', data.value.access_token)
                    if (data.value?.refresh_token) {
                        localStorage.setItem('refresh_token', data.value.refresh_token)
                    }

                    // Fetch complete user info from API
                    await this.fetchUserInfo()

                    // Navigate to home page
                    navigateTo('/')
                    return true
                }

                if (error.value && error.value.data) {
                    console.log(error.value.data)
                    this.signInError = error.value.data as ServerResponseError
                    return false
                }

                return false
            } catch (error: any) {
                // Handle Errors here.
                const errorCode = error.code
                const errorMessage = error.message

                this.signInError = {
                    detail: {
                        error_code: errorCode,
                        error_message: errorMessage,
                    },
                }
                return false
            } finally {
                this.isLoggingIn = false
                this.isSigningUp = false
                appStore.loading = false
            }
        },

        async signInWithGoogle(invitation_code?: string | undefined) {
            const appStore = useAppStore()
            this.isLoggingIn = true
            this.isSigningUp = true
            this.isError = false
            this.signInError = null
            appStore.loading = true
            const provider = new GoogleAuthProvider()
            const { auth } = useFirebase()
            try {
                const result = await signInWithPopup(auth, provider)
                // This gives you a Google Access Token. You can use it to access the Google API.
                const credential = GoogleAuthProvider.credentialFromResult(result)
                const token = credential.accessToken
                const { data, error }: { data: Ref<any>; error: any } = await useAPI('google-login-v2', {
                    method: 'POST',
                    // lazy: true,
                    server: false,
                    body: {
                        google_access_token: token,
                        invitation_code: invitation_code,
                        user: result.user,
                    },
                })

                if (data.value?.access_token) {
                    localStorage.setItem('access_token', data.value.access_token)
                    if (data.value?.refresh_token) {
                        localStorage.setItem('refresh_token', data.value.refresh_token)
                    }
                    // The signed-in user info.
                    const user = result.user
                    // fake user
                    this.user = {
                        email: user.email || '',
                        full_name: user.displayName || '',
                        is_active: true,
                        is_superuser: false,
                        id: 0,
                        tokens: 0,
                        current_plan: '',
                        avatar: user.photoURL || '',
                    }
                    return true
                }

                if (error.value && error.value.data) {
                    console.log(error.value.data)
                    this.signInError = error.value.data as ServerResponseError
                    return false
                }

                return false
            } catch (error: any) {
                // Handle Errors here.
                const errorCode = error.code
                const errorMessage = error.message

                this.signInError = {
                    detail: {
                        error_code: errorCode,
                        error_message: errorMessage,
                    },
                }
                return false
            } finally {
                this.isLoggingIn = false
                this.isSigningUp = false
                appStore.loading = false
            }
        },

        async signInWithApple(invitation_code?: string | undefined) {
            const appStore = useAppStore()
            this.isLoggingIn = true
            this.isSigningUp = true
            this.isError = false
            this.signInError = null
            appStore.loading = true

            try {
                const result = await window?.AppleID?.auth?.signIn()
                console.log('🚀 ~ signInWithApple ~ result:', result)
                // This gives you a Google Access Token. You can use it to access the Google API.
                const { data, error }: { data: Ref<any>; error: any } = await useAPI('apple-login', {
                    method: 'POST',
                    // lazy: true,
                    server: false,
                    body: {
                        apple_access_token: result?.authorization?.id_token,
                        full_name: result?.user?.name?.firstName
                            ? result?.user?.name?.firstName + ' ' + result?.user?.name?.lastName
                            : undefined,
                        invitation_code: invitation_code,
                    },
                })

                if (data.value?.access_token) {
                    localStorage.setItem('access_token', data.value.access_token)
                    if (data.value?.refresh_token) {
                        localStorage.setItem('refresh_token', data.value.refresh_token)
                    }
                    // fake user
                    this.user = {
                        email: 'apple',
                        full_name: 'apple',
                        is_active: true,
                        is_superuser: false,
                        id: 0,
                        tokens: 0,
                        current_plan: '',
                        avatar: '',
                    }
                    return true
                }

                if (error.value && error.value.data) {
                    console.log(error.value.data)
                    this.signInError = error.value.data as ServerResponseError
                    return false
                }

                return false
            } catch (error: any) {
                // Handle Errors here.
                const errorCode = error.code
                const errorMessage = error.message

                this.signInError = {
                    detail: {
                        error_code: errorCode,
                        error_message: errorMessage,
                    },
                }
                return false
            } finally {
                this.isLoggingIn = false
                this.isSigningUp = false
                appStore.loading = false
            }
        },

        checkAccessBeforeAction(access: AccessEnum, action: () => void) {
            if (this.canAccess(access)) {
                action()
                return true
            } else {
                // プランをアップグレードする
                navigateTo('/pricing')
            }
        },

        async refreshToken() {
            const refresh_token = localStorage.getItem('refresh_token')
            if (!refresh_token) {
                return
            }
            const { data }: { data: Ref<RefreshTokenResponse>; error: any } = await useAPI('/refresh-token', {
                method: 'POST',
                body: {
                    refresh_token,
                },
            })
            if (data.value) {
                localStorage.setItem('access_token', data.value.access_token)
                localStorage.setItem('refresh_token', data.value.refresh_token || '')
                this.fetchUserInfo()
                // set the timer to refresh token after 25 minutes
                // setTimeout(() => {
                //     this.refreshToken()
                // }, 25 * 60 * 1000)
            } else {
                this.setIsExpired(true)
                this.logout()
            }
        },

        async getReferralLink() {
            const toast = useToast()

            try {
                this.loadings.getReferralLink = true
                this.errors.getReferralLink = null

                const res = await axios.post(
                    'https://api.partnerstack.com/v1/partnerships/anonymous/mptmultilingualtranslationcompanylimited/' +
                        this.user?.email
                )
                if (res.data?.status === 200 && this.user) {
                    const rdata = JSON.parse(res.data?.rdata)
                    this.user.referral_link = rdata?.link

                    await useAPI('user/update-info', {
                        method: 'PUT',
                        body: {
                            referral_link: rdata?.link,
                        },
                        // lazy: true,
                        server: false,
                    })
                    return rdata?.link
                }
            } catch (error: any) {
                this.errors.getReferralLink = error
                toast.add({
                    title: 'Error',
                    description: error?.response?.data?.message || 'Failed to get referral link',
                    color: 'red',
                })
                return false
            } finally {
                this.loadings.getReferralLink = false
            }
        },
    },
})

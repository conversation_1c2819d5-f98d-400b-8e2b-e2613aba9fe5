interface LoadingObject {
    [key: string]: boolean
}

interface APIResponse {
    success: boolean
}

type WebhookResponse = Pick<APIResponse, "success"> & {
    result: Webhook
};

interface Webhook {
    id: number
    user_id: number
    webhook_url: string
    created_at: string
    updated_at: string
}

interface APIKey {
    id: number
    api_key: string
    name: string
    created_at: string
    updated_at: string
}

type APIKeyResponse = Pick<APIResponse, "success"> & {
    result: APIKey[]
}

type CreateAPIKeyResponse = Pick<APIResponse, "success"> & {
    api_key: string
}

export const useIntegrationStore = defineStore('integrationStore', {
    state: () => ({
        loading: {} as LoadingObject,
        apiKeys: [] as any[],
        webhook: {} as Webhook,
        apiKeyName: '' as string,
        webhookUrl: '' as string,

        triggerWebhookResponse: null as any,
    }),

    getters: {},

    actions: {
        async getAPIKeys() {
            const appStore = useAppStore()
            try {
                this.loading['getAPIKeys'] = true
                appStore.loading = true
                const { data }: { data: Ref<APIKeyResponse> } = await useAPI('user/api-key', {
                    method: 'GET',
                    server: false,
                })
                if (data.value?.success) {
                    const result = data.value.result.map(apiKey => {
                        //TODO: If name is null, set it to 'Secret Key'
                        return {
                            ...apiKey,
                            name: apiKey.name === null ? 'Secret Key' : apiKey.name
                        };
                    });
                    this.apiKeys = result
                    return true
                }
            } catch (error) {
                return false
            } finally {
                this.loading['getAPIKeys'] = false
                appStore.loading = false
            }
        },
        async createAPIKey(name?: string) {
            const appStore = useAppStore()
            try {
                this.loading['createAPIKey'] = true
                appStore.loading = true
                const { data }: { data: Ref<CreateAPIKeyResponse> } = await useAPI('user/api-key', {
                    method: 'POST',
                    body: {
                        name: name,
                    },
                })
                if (data.value?.success) {
                    this.apiKeyName = '';
                    return data.value.api_key
                }
                return false
            } catch (error) {
                return false
            } finally {
                this.loading['createAPIKey'] = false
                appStore.loading = false
            }
        },
        async deleteAPIKey(id: number) {
            const appStore = useAppStore()
            try {
                this.loading['deleteAPIKey'] = true
                appStore.loading = true
                const { data }: { data: Ref<APIResponse> } = await useAPI('user/api-key', {
                    method: 'DELETE',
                    body: {
                        id: id,
                    },
                })
                console.log("Delete", data)
                if (data.value?.success) {
                    return true
                }
                
                return false
            } catch (error) {
                return false
            } finally {
                this.loading['deleteAPIKey'] = false
                appStore.loading = false
            }
        },
        async getWebhook() {
            const appStore = useAppStore()
            try {
                this.loading['getWebhook'] = true
                appStore.loading = true
                const { data }: { data: Ref<WebhookResponse> } = await useAPI('user/webhook', {
                    method: 'GET',
                    server: false,
                })
                if (data.value?.success) {
                    this.webhook = data.value.result
                    this.webhookUrl = this.webhook.webhook_url
                    return true
                }
            } catch (error) {
                return false
            } finally {
                this.loading['getWebhook'] = false
                appStore.loading = false
            }
        },
        async saveWebhook(url?: string) {
            const appStore = useAppStore()
            try {
                this.loading['createWebhook'] = true
                appStore.loading = true
                const { data }: { data: Ref<APIResponse> } = await useAPI('user/webhook', {
                    method: 'POST',
                    body: {
                        webhook_url: url,
                    },
                })
                console.log("create webhook", data.value)
                if (data.value?.success) {
                    this.apiKeyName = '';
                    return true
                }
                return false
            } catch (error) {
                return false
            } finally {
                this.loading['createWebhook'] = false
                appStore.loading = false
            }
        },
        async triggerWebhook(event_name: string) {
            const appStore = useAppStore()
            try {
                this.loading['triggerWebhook'] = true
                // appStore.loading = true
                const { data } = await useAPI(
                    'webhook_events/test/' + event_name,
                    {
                        method: 'GET',
                        server: false,
                    }
                )

                this.triggerWebhookResponse = data.value
                return (data.value as { success: boolean })?.success || false
            } catch (error) {
                this.triggerWebhookResponse = {
                    success: false,
                }
                return false
            } finally {
                this.loading['triggerWebhook'] = false
                // appStore.loading = false
            }
        },
    },
})

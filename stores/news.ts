import axios, { type AxiosProgressEvent } from 'axios'

export const useNewsStore = defineStore('newsStore', {
    state: () => ({
        news: [
            {
                id: "1",
                title: 'Announcement on Service Charges at TTSOpenAI',
                description:
                    'We would like to inform you that, starting from 2:00 3/11/2024 UTC, TTSOpenAI.com will officially implement a fee-based model for our text-to-speech service...',
                content: `new-0-content`,
                image: '/assets/images/service-charge.jpg',
            },
            {
                id: "2",
                title: 'TTSOpenAI Mobile App Launching on iOS and Android This November',
                description:
                    'Due to high demand from our valued users, TTSOpenAI is excited to announce the upcoming launch of our official mobile app for iOS and Android in November 2024...',
                content: `new-1-content`,
                image: '/assets/images/mobile-app.jpg',
            },
            {
                id: "3",
                title: 'TTSOpenAI API Launching at the End of November',
                description:
                    'We’re thrilled to announce that, by the end of November 2024, TTSOpenAI will officially release its API. This new offering responds to the growing interest from developers and businesses looking to integrate TTSOpenAI’s advanced text-to-speech capabilities directly into their own platforms, applications, or workflows...',
                content: `new-2-content`,
                image: '/assets/images/api.jpg',
            },
        ],
    }),
    getters: {},
    actions: {},
})

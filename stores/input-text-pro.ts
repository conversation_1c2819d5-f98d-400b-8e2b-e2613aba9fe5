export const useInputTextProStore = defineStore('inputTextProStore', {
    persist: [
        {
            paths: [
                'emotion',
                'model',
                'vibe',
                'recentEmotions',
                'recentVibes',
                'userCustomPrompts',
                'custom_prompt',
                'selectedPrompt',
            ],
            storage: window.localStorage,
        },
    ],
    state: () => ({
        emotion: '' as any,
        model: 'audio_stable',
        input: '',
        custom_prompt: '',
        vibe: null as any,
        loadings: {} as Record<string, boolean>,
        errors: {
            createSpeech: null as string | null,
            addNewPrompt: null as string | null,
            updatePrompt: null as string | null,
        } as Record<string, any>,

        textareaRef: null as HTMLTextAreaElement | null,
        recentEmotions: [] as any[],
        recentVibes: [] as any[],

        userCustomPrompts: [] as any[],
        selectedPrompt: null as any,

        emotions: [] as any[],
        vibes: [] as any[],
        customVibes: [] as any[],
        customVibe: {
            vibe: '',
            prompt: '',
        } as any,
    }),

    getters: {
        textareaWidth(): number {
            return this.textareaRef?.offsetWidth || 0
        },
        userCustomPromptsPayload(): any[] {
            return this.userCustomPrompts.map((e) => ({
                label: e.label,
                value: e.value,
            }))
        },
    },

    actions: {
        async createSpeech(t: any) {
            const translateStore = useTranslateStore()
            const toast = useToast()
            const router = useRouter()
            try {
                translateStore.loadings['createSpeech'] = true
                const { data, error } = await useAPI('/text-to-speech-advanced', {
                    method: 'POST',
                    body: {
                        emotion: this.emotion?.id,
                        model: this.model,
                        input: this.input,
                        custom_prompt: this.custom_prompt?.trim() || undefined,
                        voice_id: translateStore.voiceIdSelected,
                        speed: translateStore.speed,
                        output_format: translateStore.outputFormat,
                        output_channel: translateStore.outputChannel,
                        vibe_id: this.vibe?.id,
                    },
                })
                if (data?.value) {
                    toast.add({
                        id: 'create-speech',
                        color: 'primary',
                        title: t('Success'),
                        description: t('Vocalization created successfully, you can check the result in history.'),
                        actions: [
                            {
                                label: t('Go to history'),
                                variant: 'solid',
                                color: 'primary',
                                click: () => {
                                    router.push({ name: 'history', query: { id: data.value?.uuid } })
                                    toast.remove('create-speech')
                                },
                            },
                        ],
                        timeout: 30000,
                        icon: 'i-ooui:success',
                    })
                }

                if (error.value) {
                    if (error.value?.statusCode === 402) {
                        this.errors['createSpeech'] = translateStore.showAccountVerifyWarning
                            ? 'NOT_VERIFY_ACCOUNT'
                            : error.value?.data?.detail?.error_code
                        translateStore.loadings['createSpeech'] = false
                        return
                    }

                    // check if error detail error_code[0] is timeout-or-duplicate, reset turnstile token and try again
                    // if (error.value?.data?.detail?.error_code === 'timeout-or-duplicate') {
                    //     this.turnstileRef?.reset()
                    //     this.loadings['createSpeech'] = false
                    //     return
                    // }
                    throw error.value
                }
            } catch (error: any) {
                console.log('🚀 ~ createSpeech ~ error:', error)
                toast.add({
                    id: 'create-speech',
                    title: t('Error'),
                    description: error.message,
                    icon: 'i-ooui:error',
                    color: 'red',
                })

                this.errors['createSpeech'] = error
            } finally {
                translateStore.loadings['createSpeech'] = false
            }
        },

        updateRecentEmotions(emotionId: any) {
            if (this.recentEmotions.length === 0) {
                this.recentEmotions.push(emotionId)
            } else {
                const index = this.recentEmotions.findIndex((e) => e === emotionId)
                if (index !== -1) {
                    this.recentEmotions.splice(index, 1)
                    this.recentEmotions.unshift(emotionId)
                } else {
                    this.recentEmotions.unshift(emotionId)
                    if (this.recentEmotions.length > 5) {
                        this.recentEmotions.pop()
                    }
                }
            }
        },

        updateRecentVibes(vibe_id: any) {
            if (this.recentVibes.length === 0) {
                this.recentVibes.push(vibe_id)
            } else {
                const index = this.recentVibes.findIndex((e) => e === vibe_id)
                if (index !== -1) {
                    this.recentVibes.splice(index, 1)
                    this.recentVibes.unshift(vibe_id)
                } else {
                    this.recentVibes.unshift(vibe_id)
                    if (this.recentVibes.length > 5) {
                        this.recentVibes.pop()
                    }
                }
            }
        },

        async updatePrompts(payload: any): boolean {
            const toast = useToast()
            try {
                this.loadings['updatePrompts'] = true
                const { data } = await useAPI('/user/update-info', {
                    method: 'PUT',
                    body: {
                        custom_prompt: payload,
                    },
                })
                if (data?.value) {
                    return true
                } else {
                    throw new Error("Can't create speech")
                }
            } catch (error: any) {
                console.log('🚀 ~ updatePrompts ~ error:', error)
                return false
            } finally {
                this.loadings['updatePrompts'] = false
            }
        },

        async addNewPrompt(payload: any) {
            try {
                this.loadings['addNewPrompt'] = true

                const { data, error } = await useAPI('/custom-prompts', {
                    method: 'POST',
                    body: {
                        name: payload?.label,
                        prompt: payload?.value,
                    },
                })

                if (data?.value) {
                    const newPrompt = {
                        ...payload,
                        id: data.value?.data?.id,
                    }
                    this.userCustomPrompts.unshift(newPrompt)
                    this.selectedPrompt = newPrompt
                    return data.value
                } else if (error.value) {
                    throw error.value
                }
            } catch (error: any) {
                console.log('🚀 ~ addNewPrompt ~ error:', error)
                this.errors['addNewPrompt'] = error
                return false
            } finally {
                this.loadings['addNewPrompt'] = false
            }
        },

        async removePrompt(prompt: any) {
            try {
                this.loadings['removePrompt'] = true
                const { data } = await useAPI('/custom-prompts/' + prompt?.id, {
                    method: 'DELETE',
                })
                if (data?.value) {
                    const index = this.userCustomPrompts.findIndex((e) => e.id === prompt.id)
                    this.userCustomPrompts.splice(index, 1)
                }

                return data.value
            } catch (error: any) {
                console.log('🚀 ~ removePrompt ~ error:', error)
                return false
            } finally {
                this.loadings['removePrompt'] = false
            }
        },

        async updatePrompt(prompt: any) {
            try {
                this.loadings['updatePrompt'] = true
                const { data } = await useAPI('/custom-prompts/' + prompt?.id, {
                    method: 'PUT',
                    body: {
                        name: prompt?.label,
                        prompt: prompt?.value,
                    },
                })
                if (data?.value) {
                    this.userCustomPrompts = this.userCustomPrompts.map((e, i) => {
                        if (e.id === prompt.id) {
                            return prompt
                        }
                        return e
                    })
                }
                return data.value
            } catch (error: any) {
                console.log('🚀 ~ updatePrompt ~ error:', error)
                return false
            } finally {
                this.loadings['updatePrompt'] = false
            }
        },

        async fetchEmotions() {
            const toast = useToast()
            if (this.emotions.length > 0) return
            try {
                this.loadings['fetchEmotions'] = true
                const { data }: { data: any } = await useAPI('/emotions', {
                    method: 'GET',
                })
                if (data?.value) {
                    this.emotions = data.value?.result?.filter((emotion: any) => emotion.is_enabled)
                    console.log('🚀 ~ fetchEmotions ~ data.value?.result:', data.value?.result)
                    return true
                } else {
                    throw new Error("Can't fetch emotions")
                }
            } catch (error: any) {
                console.log('🚀 ~ fetchEmotions ~ error:', error)
                this.errors['fetchEmotions'] = error
                return false
            } finally {
                this.loadings['fetchEmotions'] = false
            }
        },

        async fetchVibes() {
            const toast = useToast()
            if (this.vibes.length > 0) return
            try {
                this.loadings['fetchVibes'] = true
                const { data }: { data: any } = await useAPI('/master-vibes', {
                    method: 'GET',
                })
                if (data?.value) {
                    // parse data.value to array of object
                    const arrayData = Object.keys(data.value).map((key) => {
                        return {
                            ...data.value[key],
                        }
                    })
                    this.vibes = arrayData
                    console.log("🚀 ~ fetchVibes ~ this.vibe:", this.vibe)

                    if(this.vibe?.id) {
                        const index = this.vibes.findIndex((e) => e.id === this.vibe?.id)
                        console.log("🚀 ~ fetchVibes ~ index:", index)
                        if (index !== -1) {
                            this.vibe = this.vibes[index]
                        }
                    }
                    return true
                } else {
                    throw new Error("Can't fetch vibes")
                }
            } catch (error: any) {
                console.log('🚀 ~ fetchVibes ~ error:', error)
                this.errors['fetchVibes'] = error
                return false
            } finally {
                this.loadings['fetchVibes'] = false
            }
        },

        async fetchCustomVibes() {
            const toast = useToast()
            if (this.vibes.length > 0) return
            try {
                this.loadings['fetchCustomVibes'] = true
                const { data }: { data: any } = await useAPI('/custom-vibes', {
                    method: 'GET',
                })
                if (data?.value) {
                    // parse data.value to array of object
                    const arrayData = Object.keys(data.value).map((key) => {
                        return {
                            ...data.value[key],
                        }
                    })
                    this.customVibes = arrayData
                    if(this.vibe?.id) {
                        const index = this.customVibes.findIndex((e) => e.id === this.vibe?.id)
                        if (index !== -1) {
                            this.vibe = this.customVibes[index]
                        }
                    }
                    return true
                } else {
                    throw new Error("Can't fetch vibes")
                }
            } catch (error: any) {
                console.log('🚀 ~ fetchCustomVibes ~ error:', error)
                this.errors['fetchCustomVibes'] = error
                return false
            } finally {
                this.loadings['fetchCustomVibes'] = false
            }
        },

        async createCustomVibe(payload: any) {
            const toast = useToast()
            try {
                this.loadings['createCustomVibe'] = true
                const { data }: { data: any } = await useAPI('/custom-vibes', {
                    method: 'POST',
                    body: {
                        vibe: payload?.name,
                        prompt: payload?.prompt,
                    },
                })
                if (data?.value) {
                    this.customVibes.unshift(data.value?.data)
                    this.vibe = data.value?.data
                }
                return data.value
            } catch (error: any) {
                console.log('🚀 ~ createCustomVibe ~ error:', error)
                this.errors['createCustomVibe'] = error
                toast.add({
                    id: 'create-custom-vibe',
                    title: 'Error',
                    description: error.message,
                    icon: 'i-ooui:error',
                    color: 'red',
                })
                return false
            } finally {
                this.loadings['createCustomVibe'] = false
            }
        },

        async deleteCustomVibe(payload: any) {
            const toast = useToast()
            try {
                this.loadings['deleteCustomVibe'] = true
                const { data }: { data: any } = await useAPI('/custom-vibes/' + payload?.id, {
                    method: 'DELETE',
                })
                if (data?.value) {
                    this.customVibes = this.customVibes.filter((e) => e.id !== payload?.id)
                    if (this.vibe?.id === payload?.id) {
                        this.vibe = null
                    }
                }
                return data.value
            } catch (error: any) {
                console.log('🚀 ~ deleteCustomVibe ~ error:', error)
                this.errors['deleteCustomVibe'] = error
                toast.add({
                    id: 'create-custom-vibe',
                    title: 'Error',
                    description: error.message,
                    icon: 'i-ooui:error',
                    color: 'red',
                })
                return false
            } finally {
                this.loadings['deleteCustomVibe'] = false
            }
        },

        async updateCustomVibe(payload: any) {
            const toast = useToast()
            try {
                this.loadings['updateCustomVibe'] = true
                const { data }: { data: any } = await useAPI('/custom-vibes/' + payload?.id, {
                    method: 'PUT',
                    body: {
                        vibe: payload?.name,
                        prompt: payload?.prompt,
                    },
                })
                if (data?.value) {
                    this.customVibes = this.customVibes.map((e) => {
                        if (e.id === payload.id) {
                            return {
                                ...e,
                                vibe: payload.name,
                                prompt: payload.prompt,
                            }
                        }
                        return e
                    })
                }
                return data.value
            } catch (error: any) {
                console.log('🚀 ~ updateCustomVibe ~ error:', error)
                this.errors['updateCustomVibe'] = error
                toast.add({
                    id: 'update-custom-vibe',
                    title: 'Error',
                    description: error.message,
                    icon: 'i-ooui:error',
                    color: 'red',
                })
                return false
            } finally {
                this.loadings['updateCustomVibe'] = false
            }
        },

        clearInput() {
            this.input = ''
        },

        async fetchPrompts() {
            // if (this.userCustomPrompts.length > 0) return
            try {
                this.loadings['fetchPrompts'] = true
                const { data }: { data: any } = await useAPI('/custom-prompts', {
                    method: 'GET',
                })
                console.log('🚀 ~ fetchPrompts ~ data:', data.value)

                if (data?.value) {
                    // convert to array of object { 0 : { label: 'prompt', value: 'prompt' } } (data.value)
                    const arrayData = Object.keys(data.value).map((key) => {
                        return {
                            ...data.value[key],
                        }
                    })
                    this.userCustomPrompts = arrayData

                    return true
                } else {
                    throw new Error("Can't fetch prompts")
                }
            } catch (error: any) {
                console.log('🚀 ~ fetchPrompts ~ error:', error)
                this.errors['fetchPrompts'] = error
                return false
            } finally {
                this.loadings['fetchPrompts'] = false
            }
        },
    },
})

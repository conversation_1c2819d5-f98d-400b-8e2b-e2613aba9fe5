import { cloneDeep } from 'lodash'
import { useTranslateStore } from './translate'
import type { BlockItem } from '~/types'

interface History {
    id: number
    type: string
    origin_lang: string
    target_lang: string
    domain: string
    tone: string
    writing_style: string
    trans_input: string
    trans_result: string
    used_token: number
    created_at: string
    uuid: string
    media_url?: string
}

interface HistoryResponse {
    success: boolean
    total: number
    result: History[]
}

const FAKE_HISTORIES: History[] = [
    {
        id: 99999999,
        uuid: '6c924fd0-5f9b-11ee-0000-0a58a9feac02',
        origin_lang: 'english',
        target_lang: 'vietnamese',
        domain: 'Art & Design',
        tone: 'Authoritative',
        writing_style: 'Academic',
        trans_input:
            'This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box. This is a very long text. You can not see the end of it if you do not expand the box.',
        trans_result:
            'Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp. Đây là một đoạn văn rất dài. Bạn không thể nhìn thấy phần cuối nếu bạn không mở rộng hộp.',
        type: 'translate-text',
        used_token: 122,
        used_model: 'gpt-3.5-turbo',
        status: 2,
        status_desc: '',
        status_percentage: 1,
        error_message: '',
        rating: '',
        rating_content: '',
        custom_prompt: '',
        created_at: '2023-09-30T14:12:44',
        updated_at: '2023-09-30T14:12:45',
        file_size: 0,
        file_password: '',
    },
]

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))
export const useHistoryStore = defineStore('history', {
    state: () => ({
        filterBy: 'all',
        isLoading: false,
        isError: false,
        defaultItemPerPage: 10,
        totalPage: 1,
        currentPage: 1,
        // histories: [] as History[],
        filteredHistories: [] as History[],
        historyUUIDTobeDeleted: '',
        fakeHistories: FAKE_HISTORIES,

        loadings: {
            fetchStoryDetail: {} as any,
        } as any,
        errors: {
            onRetryBlock: null,
        },
        isShowRetryBlockModal: false,
        selectedBlock: {} as BlockItem,
        isRetryInputPro: false,
        loadingBtnRetry: false,
        selectedStoryUUID: '',
        loadingBtnDownload: false,
    }),

    getters: {
        hasProcessingData(): boolean {
            return this.filteredHistories.some((history: any) => history?.status === 1)
        },
        hasRetryingData(): boolean {
            return this.filteredHistories.some((history: any) =>
                history?.details?.blocks?.some((block: any) => block?.status === 4 || block?.status === 1)
            )
        },
    },

    actions: {
        setFilterBy(filterBy: string) {
            this.filterBy = filterBy
        },

        async filterHistories(loading = true) {
            this.isLoading = loading
            this.isError = false
            if (loading) {
                this.filteredHistories = []
                this.totalPage = 1
            }
            let query = {
                filter_by: this.filterBy,
                items_per_page: 10,
                page: this.currentPage,
            }

            const { data }: { data: Ref<HistoryResponse> } = await useAPI('histories', {
                method: 'GET',
                query: query,
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                this.filteredHistories = data.value.result
                this.totalPage = Math.ceil(data.value.total / this.defaultItemPerPage)
                return true
            }

            this.isError = true
            return false
        },

        updateHistoryProgressByUUID(uuid: string, payload: object) {
            this.filteredHistories = this.filteredHistories.map((history) => {
                if (history.uuid === uuid) {
                    return { ...history, ...payload }
                }
                return history
            })
        },

        async fetchHistoryById(translation_uuid: string, loading = true) {
            this.isLoading = loading
            this.isError = false
            if (loading) {
                this.filteredHistories = []
                this.totalPage = 1
            }
            const { data }: { data: Ref<History> } = await useAPI('history/' + translation_uuid, {
                method: 'GET',
                // lazy: true,
                server: false,
            })
            this.isLoading = false
            console.log('🚀 ~ fetchHistoryById ~ data.value:', data.value)

            if (data.value) {
                // this.filteredHistories = cloneDeep([data.value])
                // this.totalPage = 1
                if (this.filteredHistories.length > 0) {
                    // replace the history with the same uuid
                    this.filteredHistories = this.filteredHistories.map((history) => {
                        if (history.uuid === translation_uuid) {
                            return data.value
                        }
                        return history
                    })
                } else {
                    this.filteredHistories = [data.value]
                    this.totalPage = 1
                }
                // if status = 2 or 3, clear resultListener
                if (data.value.status === 2 || data.value.status === 3) {
                    const translateStore = useTranslateStore()
                    clearInterval(translateStore.resultListener)
                }
                return true
            }

            this.isError = true
            return false
        },

        async deleteHistory(id: number, uuid: string) {
            this.isLoading = true
            this.isError = false

            const { data }: { data: Ref<HistoryResponse> } = await useAPI('history/' + id, {
                method: 'DELETE',
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                const translateStore = useTranslateStore()
                const { resultUuid } = translateStore

                if (resultUuid === uuid) {
                    translateStore.clearTranslateText()
                }
                return true
            }

            this.isError = true
            return false
        },

        async stopProcessingHistory(id: number, uuid: string) {
            this.isLoading = true
            this.isError = false

            const { data }: { data: Ref<HistoryResponse> } = await useAPI('history/' + id + '/stop', {
                method: 'PUT',
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                // update status of history to 4
                this.filteredHistories = this.filteredHistories.map((history) => {
                    if (history.uuid === uuid) {
                        return { ...history, status: 4 }
                    }
                    return history
                })
                return true
            }

            this.isError = true
            return false
        },

        async deleteAllHistory() {
            this.isLoading = true
            this.isError = false
            const deleteAllUrl = this.filterBy === 'all' ? 'histories/all' : 'histories/all/' + this.filterBy
            const { data }: { data: Ref<HistoryResponse> } = await useAPI(deleteAllUrl, {
                method: 'DELETE',
                // lazy: true,
                server: false,
            })
            this.isLoading = false

            if (data.value?.success) {
                return true
            }

            this.isError = true
            return false
        },
        async rateHistory(history_uuid: string, payload: object) {
            let publicPrefix = ''
            if (!localStorage.getItem('access_token')) {
                // User is not logging in then use public API (limited translate)
                publicPrefix = 'public/'
            }

            const { data }: { data: Ref<HistoryResponse> } = await useAPI(
                publicPrefix + 'history-feedback/' + history_uuid,
                {
                    method: 'PUT',
                    // lazy: true,
                    body: payload,
                    server: false,
                }
            )

            return !!data.value?.success
        },

        async fetchStoryDetail(uuid: string): Promise<any> {
            // const appStore = useAppStore()
            try {
                this.loadings['fetchStoryDetail'][uuid] = true
                const { data } = await useAPI(`story/detail/${uuid}`, {
                    method: 'GET',
                    server: false,
                })
                this.filteredHistories = this.filteredHistories.map((history: any) => {
                    if (history.uuid === uuid) {
                        history.details = data.value
                    }
                    return history
                })
                return true
            } catch (error) {
                return false
            } finally {
                this.loadings['fetchStoryDetail'][uuid] = false
            }
        },
        // Show retry block modal
        showRetryBlockModal(block: BlockItem, uuid: string, isInputPro = false) {
            if (block && uuid) {
                this.selectedBlock = block
                this.selectedStoryUUID = uuid
                this.isShowRetryBlockModal = true
                this.isRetryInputPro = isInputPro
            } else {
                console.error('Block or UUID is required')
            }
        },
        async onRetryBlock(block: BlockItem): Promise<boolean> {
            this.loadingBtnRetry = true
            this.errors.onRetryBlock = null
            const { name, input, voice_id, speed, accent } = block
            const payload = {
                name,
                input,
                voice_id,
                speed,
                accent
            }
            const authStore = useAuthStore()
            try {
                const { data, error } = await useAPI('story/block/' + block.uuid + '/retry-speech', {
                    method: 'PUT',
                    body: payload,
                })
                if (data.value) {
                    // Refresh story detail

                    await this.fetchStoryDetail(this.selectedStoryUUID)
                    authStore.syncUserTokenInfo()
                    this.filterHistories(false)
                    return true
                }

                if (error.value) {
                    if (error.value?.statusCode == 402 || error.value?.statusCode == 422) {
                        this.errors.onRetryBlock = error.value?.data?.detail?.error_code

                        return false
                    }
                    throw error.value
                }
                return false
            } catch (error) {
                console.error('error', error)
                return false
            } finally {
                this.loadingBtnRetry = false
            }
        },

        async onDownloadAll(blocks: BlockItem[], uuid: string, isReplaceAudio: boolean): Promise<boolean> {
            this.loadingBtnDownload = true
            try {
                const { data } = await useAPI('story/' + uuid + '/merge-blocks', {
                    method: 'POST',
                    body: {
                        blocks: blocks.map((block) => block.id),
                        replace: isReplaceAudio ? 1 : 0,
                    },
                })
                return true
            } catch (error) {
                console.error(error)
                return false
            } finally {
                this.loadingBtnDownload = false
            }
        },

        cloneHistory(history: any) {
            const translateStore = useTranslateStore()
            const storyStore = useStoryStore()
            const voiceLibraryStore = useVoiceLibraryStore()
            const inputTextProStore = useInputTextProStore()
            const router = useRouter()
            translateStore.model = history.model_name || 'tts-1'
            translateStore.speed = history.speed
            translateStore.voiceIdSelected = history.voice_id
            translateStore.outputFormat = history.output_format || 'mp3'
            translateStore.outputChannel = history.output_channel || 'mono'
            voiceLibraryStore.filter.voiceTypes = voiceLibraryStore.getVoiceTypeByVoiceId(history.voice_id) || ''
            switch (history.type) {
                case 'tts-text':
                    translateStore.inputText = history.tts_input
                    translateStore.accent = history.accent
                    router.push('/')
                    break
                case 'tts-text-emotion':
                    inputTextProStore.input = history.tts_input
                    inputTextProStore.model = history.model_name
                    inputTextProStore.emotion = {
                        id: history.emotion,
                        emojis: history.emotion ? history.emotion + '.svg' : null,
                    }
                    inputTextProStore.vibe = {
                        id: history.vibe_id,
                    }
                    inputTextProStore.custom_prompt = history.custom_prompt
                    inputTextProStore.selectedPrompt = null
                    router.push('/emotion-text')
                    break
                case 'tts-story':
                    let stories = history.details?.blocks || []
                    stories = stories.map((block: any) => {
                        const voice = voiceLibraryStore.arrayVoiceLibraries.find((v: any) => v.id === block?.voice_id)
                        return {
                            ...block,
                            voice: voice,
                        }
                    })
                    storyStore.stories = stories
                    storyStore.storyOptions.format = history.output_format || 'mp3'
                    storyStore.storyOptions.output_channel = history.output_channel || 'stereo'
                    router.push('/story-maker')
                    break
                case 'tts-document':
                    translateStore.inputFile = new File([''], history.tts_input)
                    translateStore.filePassword = history.file_password
                    translateStore.s3_file_path = history.input_file_path
                    translateStore.file_name_origin = history.tts_input
                    translateStore.file_size = history.file_size
                    router.push('/documents')
                    break
                default:
                    break
            }
        },
    },
})

import { getDatabase, ref, onValue, update, query, orderByChild, startAt, onChildChanged } from 'firebase/database'
import _ from 'lodash'
import { saveAs } from 'file-saver'
interface DownloadFileResponse {
    url: string
}

interface Notification {
    created_at: string
    document_name: string
    message: string
    seen: boolean
    translation_history_id: number
    uuid: string
    success?: boolean
    status_percentage?: number
}

export const useNotificationsStore = defineStore('notifications', {
    state: () => ({
        notifications: [],
        isLoading: false,
        userUUID: '',
        alreadyGotNotifications: false,
        needSyncData: false,
        loadings: {} as Record<string, boolean>,
    }),

    getters: {
        hasUnreadNotifications: (state) => {
            return Object.values(state.notifications).some((notification: any) => !notification.seen)
        },
        sortedNotifications: (state) => {
            const temp = [] as Notification[]
            Object.entries(state.notifications).forEach(([key, value]) => {
                if ((value as Notification).translation_history_id) {
                    temp.push({ ...(value as Notification), uuid: key })
                }
            })
            // only get notifications in the last 30 days
            return _.orderBy(temp, ['created_at'], ['desc']).filter((notification) => {
                const today = new Date()
                const notificationDate = new Date(notification.created_at)
                const diffTime = Math.abs(today.getTime() - notificationDate.getTime())
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                return diffDays <= 30 && notification.status_percentage === 100
            })
        },
    },

    actions: {
        async getNotifications(userUUID: string) {
            console.log('notification: ', userUUID)
            this.alreadyGotNotifications = true
            this.isLoading = true
            this.userUUID = userUUID
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            // Calculate the date 30 days ago
            const currentDate = new Date()
            const thirtyDaysAgo = new Date()
            thirtyDaysAgo.setDate(currentDate.getDate() - 30)
            const formattedThirtyDaysAgo = thirtyDaysAgo.toISOString().slice(0, 19).replace('T', ' ')
            //startAt and endAt are used to get the last 30 days of notifications
            const notificationsRef = query(
                ref(db, 'notification/' + userUUID),
                orderByChild('created_at'),
                startAt(formattedThirtyDaysAgo)
            )

            onValue(notificationsRef, (snapshot) => {
                const data = snapshot.val()
                this.notifications = { ...data }
                this.isLoading = false

                const translateStore = useTranslateStore()
                const ttsResult = translateStore.ttsResult
                if (data && data[ttsResult?.uuid]) {
                    const result = data[ttsResult?.uuid]
                    if ((result?.status_percentage === 100 && result?.status === 2) || result?.status === 3) {
                        translateStore.fetchTtsResult()
                        const historyStore = useHistoryStore()
                        historyStore.fetchHistoryById(ttsResult?.uuid, false)
                    }
                }
            })
        },

        async listenResult(userUUID: string) {
            this.needSyncData = false
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            const notificationsRef = query(ref(db, 'notification/' + userUUID))
            onChildChanged(notificationsRef, (snapshot) => {
                const data = snapshot.val()
                console.log('🚀 ~ onChildChanged ~ data:', data)
                if ((data?.status_percentage === 100 && data?.status === 2) || data?.status === 3) {
                    this.needSyncData = true
                }

                // if (data?.status_percentage) {
                //     // check if nuxt is already loaded
                //     if (process.client) {
                //         console.log('Nuxt client side is already loaded')
                //         const historyStore = useHistoryStore()
                //         historyStore.updateHistoryProgressByUUID(data.translation_history_id, {
                //             status_percentage: data.status_percentage,
                //         })
                //     }
                // }
            })
        },

        async markAsRead(notificationUUID: string) {
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            const notificationRef = ref(db, 'notification/' + this.userUUID + '/' + notificationUUID)
            await update(notificationRef, { seen: true })
        },

        async markAllAsRead() {
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            for (const notification of this.sortedNotifications) {
                const notificationRef = ref(db, 'notification/' + this.userUUID + '/' + notification.uuid)
                await update(notificationRef, { seen: true })
            }
        },

        async tryDownloadFile(file_download_url: string): Promise<any> {
            try {
                const token = localStorage.getItem('access_token')
                const response = await fetch(file_download_url, {
                    method: 'GET',
                    headers: {
                        Authorization: `Bearer ${token}`,
                    },
                })
                return response
            } catch (error: any) {
                console.log("🚀 ~ tryDownloadFile ~ error:", error)
                // check if error is 403
                if (error?.statusCode === 403 || error?.statusCode === 401) {
                    const authStore = useAuthStore()
                    await authStore.refreshToken()
                    return await this.tryDownloadFile(file_download_url)
                }
                throw error
            }
        },

        async downloadFile(id: string, url: string, row: any) {
            try {
                this.loadings[id] = true
                // const res: any = await useAPI(row?.file_download_url, {
                //     method: 'GET',
                //     // lazy: true,
                //     server: false,
                //     responseType: 'stream',
                // })
                // // attachment; filename=test_hahaha (1).mp3
                // const header = res.data?.headers?.['content-disposition']
                // console.log("Header: ", header)
                // const filename = res.data?.headers?.['content-disposition']?.split('filename=')?.[1]
                // console.log("Filename: ", filename)
                // const fileName = filename?.split(';')[0]?.replace(/"/g, '') || `${id}.mp3`
                // console.log("FileName: ", fileName)
                // const fileType = res.data?.headers?.['content-type'] || 'application/octet-stream'
                // const blob = new Blob([res.data], { type: fileType })
                // saveAs(blob, fileName)
                const response = await this.tryDownloadFile(row?.file_download_url)

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`)
                }

                // Convert response to Blob
                const blob = await response.blob()
                const filename = row?.file_download_url?.split('/').pop() // Extract filename from URL

                // Create a download link
                const link = document.createElement('a')
                link.href = URL.createObjectURL(blob)
                link.download = filename
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            } catch (error) {
                console.log('🚀 ~ downloadFile ~ error:', error)
            } finally {
                this.loadings[id] = false
            }
        },

        addFakeNotification() {
            const notification = {
                created_at: new Date().toISOString(),
                document_name: 'another_file.pdf',
                message: 'Your document has been translated',
                seen: false,
                translation_history_id: 1,
                uuid: 'fake-notification-uuid',
                success: true,
            }
            this.notifications = { ...this.notifications, [notification.uuid]: notification }
        },

        removeFakeNotification() {
            const notifications = { ...this.notifications }
            delete notifications['fake-notification-uuid']
            this.notifications = notifications
        },
    },
})

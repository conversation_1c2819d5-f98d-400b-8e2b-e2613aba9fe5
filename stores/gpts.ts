interface LoadingObject {
    [key: string]: boolean
}
import { getDatabase, ref, onValue, update, query, remove } from 'firebase/database'
export const useGptsStore = defineStore('gptsStore', {
    state: () => ({
        loading: {} as LoadingObject,
        gptsResult: {} as any,
        isPlaying: false,
    }),

    getters: {
        hasProcessingData: (state) => {
            return state.gptsResult?.url === null || state.gptsResult?.url === undefined
        },
    },

    actions: {
        async fetchGptsResult(uuid: string, loading = true) {
            const appStore = useAppStore()
            try {
                this.loading['fetchGptsResult'] = loading
                appStore.loading = loading
                const { data } = await useAPI(`public/history/${uuid}`, {
                    method: 'GET',
                    server: false,
                })
                this.gptsResult = data.value
                return this.gptsResult
            } catch (error) {
                return false
            } finally {
                this.loading['fetchGptsResult'] = false
                appStore.loading = false
            }
        },

        async listenNotification(uuid: string) {
            const { firebaseApp } = useFirebase()
            const db = getDatabase(firebaseApp)
            const publicRef = query(
                ref(db, 'public/' + uuid),
            )
            onValue(publicRef, (snapshot) => {
                const data = snapshot.val()
                // check if status is 2, then fetch the result and remove this data
                if (data?.status === 2 || data?.status === 3) {
                    this.fetchGptsResult(uuid)
                    // remove the data
                    remove(ref(db, 'public/' + uuid))
                }
            })
        },
    },
})

// import type { User, UserPayload } from '~/types'
// import { useAuthService } from '~/composables/useAuthService'

export const useDialogsStore = defineStore('dialogs', {
  state: () => {
    return {
      confirmDialog: {
        open: false,
        title: '',
        message: '',
        confirmButtonText: '',
        cancelButtonText: '',
        onConfirm: () => {},
        onCancel: null as (() => void) | null
      }
    }
  },
  getters: {},
  actions: {
    onCloseConfirmDialog: function () {
      this.confirmDialog.open = false
    },

    showConfirmDialog: function ({
      title,
      message,
      confirmButtonText,
      cancelButtonText,
      onConfirm,
      onCancel
    }: {
      title: string
      message: string
      confirmButtonText: string
      cancelButtonText: string
      onConfirm: () => void
      onCancel?: () => void
    }) {
      this.confirmDialog.open = true
      this.confirmDialog.title = title
      this.confirmDialog.message = message
      this.confirmDialog.confirmButtonText = confirmButtonText
      this.confirmDialog.cancelButtonText = cancelButtonText
      this.confirmDialog.onConfirm = onConfirm
      this.confirmDialog.onCancel = () => {
        onCancel && onCancel()
        this.onCloseConfirmDialog()
      }
    }
  }
})

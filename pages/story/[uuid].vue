<script setup lang="ts">
definePageMeta({
  layout: false,
});
const storyStore = useStoryStore();
const { storyDetail } = storeToRefs(storyStore);

const route = useRoute();
onMounted(() => {
  const { uuid } = route.params;
  storyStore.fetchStoryDetail(uuid as string);
});
</script>

<template>
  <div>
    <NuxtLayout name="non-translate">
      <ULandingSection class="sm:pt-20">
        <div v-for="block in storyDetail?.blocks" class="flex flex-col gap-1">
          <div>
            {{ block?.input }}
          </div>
          <audio controls>
            <source :src="block?.media_url" type="audio/mpeg" controlsList/>
            Your browser does not support the audio element.
          </audio>
        </div>
      </ULandingSection>
    </NuxtLayout>
  </div>
</template>

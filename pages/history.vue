<script setup lang="ts">
const authStore = useAuthStore();
const { isLoggedIn, showAds } = storeToRefs(authStore);
const historyStore = useHistoryStore();
const { filteredHistories } = storeToRefs(historyStore);
const toast = useToast();
const inputTextProStore = useInputTextProStore()
definePageMeta({
  layout: false,
});

onMounted(() => {
  toast.remove("create-speech");
  // const script = document.createElement("script");
  // // (adsbygoogle = window.adsbygoogle || []).push({});
  // script.innerHTML = `(adsbygoogle = window.adsbygoogle || []).push({});`;
  // // append script tag to body
  // document.body.appendChild(script);

  // inputTextProStore.fetchEmotions()
});
</script>

<template>
  <div>
    <NuxtLayout name="non-translate">
      <ULandingSection id="history" class="scroll-mt-[var(--header-height)] !pt-20">
        <div class="flex flex-row gap-2">
          <div v-if="showAds" class="w-[130px] hidden md:block">
            <div class="fixed w-[130px] top-30">
              <AdsenseHistorySide :adSlot="6853457344" />
            </div>
          </div>
          <div class="flex-1 min-h-[calc(100vh-130px)] pb-24 space-y-4 w-full">
            <div class="z-20 pb-3 sticky top-20">
              <HistoryMenus id="translation-history-menu" />
            </div>
            <div v-if="!isLoggedIn" class="max-w-lg mx-auto p-4 md:p-0">
              <SignInSignUpRequire
                class="p-14"
                :title="$t('Want to see your history of speech creation?')"
                :message="
                  $t(
                    'You can see your history of speech creation after you sign in. It is free and easy to sign up.'
                  )
                "
              />
            </div>
            <template v-else>
              <div
                class="md:max-w-screen-md mx-auto flex justify-between flex-inline items-center"
              >
                <div
                  class="pl-4 md:pl-8 font-thin text-base text-gray-600 dark:text-gray-300"
                >
                  {{ $t("All history after 30 days will be deleted automatically.") }}
                </div>
                <div v-if="filteredHistories.length > 0">
                  <HistoryDeleteButton />
                </div>
              </div>
              <HistoryList class="md:max-w-screen-lg mx-auto md:px-0 pl-0 pr-0" />
            </template>
            <AdsenseHistorySingle v-if="showAds" />
          </div>
          <div v-if="showAds" class="w-[130px] hidden md:block">
            <div class="fixed w-[130px] top-30">
              <AdsenseHistorySide :adSlot="9765419505" />
            </div>
          </div>
        </div>
      </ULandingSection>
      <RetryBlockModal />
    </NuxtLayout>
  </div>
</template>

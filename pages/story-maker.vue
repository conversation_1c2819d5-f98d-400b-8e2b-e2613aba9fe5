<script setup lang="ts">
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
definePageMeta({
    layout: false,
})

const { t } = useI18n()
const storyStore = useStoryStore()
const {
    stories,
    storiesElm,
    activeStory,
    showVoiceLibrariesModal,
    isFirstBlockHasSilenceMoreThan2Minutes,
    isHasMoreThan100Blocks,
} = storeToRefs(storyStore)
const authStore = useAuthStore()
const { showAds } = storeToRefs(authStore)
const drag = ref(false)
const dragOptions = computed(() => ({
    animation: 200,
    group: 'description',
    disabled: false,
    ghostClass: 'ghost',
    dragClass: 'no-move',
}))

const onStartDrag = () => {
    drag.value = true
}

const onEndDrag = () => {
    drag.value = false
}

const onClickOutside = () => {
    // activeStory.value = null;
}

const onSelectVoice = (voice_id: string) => {
    storyStore.setVoiceToActiveStory(voice_id)
    showVoiceLibrariesModal.value = false
    // activeStory.value = null;
}

watch(
    () => activeStory.value,
    (active) => {
        if (active) {
            nextTick(() => {
                const index = stories.value.findIndex((story) => story.id === active.id)
                storiesElm.value.scrollToItem(index)

                // if (storyElm) {
                //   storyElm.scrollIntoView({
                //     behavior: "smooth",
                //     block: "center",
                //     inline: "center",
                //   });
                // }
            })
        }
    },
    { immediate: true, deep: true }
)

const showWarning = ref(false)
const warningTexts = computed(() => {
    const warnings = []
    if (isFirstBlockHasSilenceMoreThan2Minutes?.value) {
        warnings.push(t('Be careful, the first block has more than 2 minutes of silence'))
    }
    if (isHasMoreThan100Blocks?.value) {
        warnings.push(t('Processing more than 100 blocks is prone to errors, we recommend splitting the story'))
    }
    return warnings
})

// watch isHasMoreThan100Blocks and isFirstBlockHasSilenceMoreThan2Minutes
watch(
    [isFirstBlockHasSilenceMoreThan2Minutes, isHasMoreThan100Blocks],
    () => {
        showWarning.value = isFirstBlockHasSilenceMoreThan2Minutes?.value || isHasMoreThan100Blocks?.value
    },
    { immediate: true }
)
</script>
<template>
    <div>
        <NuxtLayout name="story">
            <div v-if="stories.length > 0" class="relative flex flex-col w-full h-full">
                <DynamicScroller
                    ref="storiesElm"
                    :items="stories"
                    :min-item-size="65"
                    class="w-full md:h-[calc(100vh-268px)] pb-4 overflow-auto scrollbar-thin"
                >
                    <template v-slot="{ item, index, active }">
                        <DynamicScrollerItem
                            :item="item"
                            :active="active"
                            :size-dependencies="[item.input]"
                            :data-index="index"
                            :data-id="item.id"
                            class="overflow-visible"
                        >
                            <div
                                class="relative overflow-visible flex flex-row items-center justify-between space-x-2 pl-3 pr-2 py-2.5 dark:bg-gray-900 dark:border-gray-800 border-b border-gray-200 cursor-pointer"
                                :class="{
                                    'hover:bg-gray-50': activeStory?.id !== item.id,
                                }"
                            >
                                <div class="h-full flex flex-row items-center w-full">
                                    <StoryItem
                                        class="flex-1"
                                        :active="activeStory?.id === item.id"
                                        v-model="stories[index]"
                                        @delete="storyStore.removeStory"
                                        @edit="activeStory = item"
                                        @duplicate="storyStore.duplicateStory"
                                        @select="storyStore.selectStory"
                                        @deleteAll="storyStore.removeAllStories"
                                        :index="index"
                                        :key="item.id"
                                    />
                                </div>
                            </div>
                        </DynamicScrollerItem>
                    </template>
                </DynamicScroller>
                <div
                    v-if="showWarning"
                    class="flex flex-row justify-between items-end w-full absolute bottom-0 left-0 right-0 text-xs text-yellow-500 dark:text-yellow-400 px-4 py-1 border-b border-gray-200 dark:border-gray-800 bg-yellow-50 dark:bg-yellow-900 p-2"
                >
                    <div>
                        <div>{{ $t('Warning') }}:</div>
                        <ul class="list-disc pl-4">
                            <li v-for="text in warningTexts" :key="text">{{ text }}</li>
                        </ul>
                    </div>
                    <div>
                        <UButton
                            size="xs"
                            color="yellow"
                            variant="solid"
                            :label="$t('Got it')"
                            @click="showWarning = false"
                        />
                    </div>
                </div>
            </div>
            <div class="">
                <div v-if="!stories.length" class="flex flex-col items-center justify-center h-full">
                    <div class="pt-14 flex flex-col items-center justify-center mb-2">
                        <div class="h-32 w-32 rounded-full flex items-center justify-center bg-gray-100 mb-5">
                            <UIcon name="i-material-symbols:voice-chat-rounded" class="text-6xl text-gray-400" />
                        </div>
                        <div class="text-gray-500 text-lg">
                            {{ $t('Create your first story') }}
                        </div>
                        <div class="text-gray-500 text-sm px-6">
                            {{ $t('You can create manually on UI or import from a file') }}
                        </div>
                    </div>
                    <div class="flex flex-col gap-3 py-2">
                        <UButton
                            icon="i-heroicons-plus"
                            size="sm"
                            color="primary"
                            variant="outline"
                            :label="$t('Add Conversation')"
                            :trailing="false"
                            @click="storyStore.addStory"
                            class="justify-center"
                        />
                        <div class="flex flex-row gap-2">
                            <StoryImportSrt />
                            <StoryImportCsv />
                            <StoryImportHistory />
                        </div>

                        <UTooltip :text="$t('Click to download template file')">
                            <a
                                @click="storyStore.downloadTemplate"
                                class="mx-auto text-xs text-center cursor-pointer underline"
                                >{{ $t('csv template') }}</a
                            >
                        </UTooltip>
                    </div>
                </div>
            </div>

            <StoryMakerMenu v-if="stories.length" />
            <VoiceLibraryModal @select="onSelectVoice" />
            <StoryBatchUpdateModal />
        </NuxtLayout>
    </div>
    <SelectDurationModal />
</template>

<template>
    <div>Download the mobile app</div>
</template>

<script setup lang="ts">
const appStore = useAppStore()
const { showPopupMobileApp, appleStoreLink, googlePlayLink } = storeToRefs(appStore)

const { isDesktop, isApple } = useDevice()

onMounted(() => {
    if (isDesktop) {
        navigateTo('/')
        showPopupMobileApp.value = true
    } else {
        if (isApple) {
            navigateTo(appleStoreLink.value, { external: true })
        } else {
            navigateTo(googlePlayLink.value, { external: true })
        }
        navigateTo('/')
        showPopupMobileApp.value = true
    }
})
</script>

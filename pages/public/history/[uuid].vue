<template>
    <div class="px-3 pt-14">
        <BaseAudioPlayer class="mt-10 z-50 max-w-4xl" :url="gptsResult?.url" :data="gptsResult?.history">
            <template #description>
                <div v-if="gptsResult?.url" class="text-sm text-yellow-500 my-3">
                    <i18n-t v-if="user" keypath="This audio will be deleted after 7 days." tag="p"></i18n-t>
                    <i18n-t
                        v-else
                        keypath="This audio will be deleted after 7 days. Let's {0} to keep more audios for free."
                        tag="p"
                    >
                        <a
                            class="font-semibold underline cursor-pointer text-primary-500"
                            href="/signup"
                            target="_blank"
                            >{{ $t('Sign up') }}</a
                        >
                    </i18n-t>
                </div>
            </template>
        </BaseAudioPlayer>
    </div>
</template>

<script setup lang="ts">
const route = useRoute()
const gptsStore = useGptsStore()
const { gptsResult, hasProcessingData } = storeToRefs(gptsStore)
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
let interval: any = null
onMounted(async () => {
    const uuid = route.params.uuid
    const result = await gptsStore.fetchGptsResult(uuid as string)

    if (!result?.url) {
        // interval = setInterval(() => {
        //     const uuid = route.params.uuid
        //     if (gptsResult.value.url) {
        //         clearInterval(interval)
        //         authStore.syncUserTokenInfo()
        //     } else {
        //         gptsStore.fetchGptsResult(uuid as string, false)
        //     }
        // }, 5000)

        gptsStore.listenNotification(uuid as string)
    }
})

watch(
    () => hasProcessingData.value,
    (val) => {
        console.log('🚀 ~ val:', val)
        if (val) {
            // interval = setInterval(() => {
            //     const uuid = route.params.uuid
            //     if (gptsResult.value.url) {
            //         clearInterval(interval)
            //         authStore.syncUserTokenInfo()
            //     } else {
            //         gptsStore.fetchGptsResult(uuid as string, false)
            //     }
            // }, 5000)
        } else {
            clearInterval(interval)
            authStore.syncUserTokenInfo()
        }
    }
)
</script>

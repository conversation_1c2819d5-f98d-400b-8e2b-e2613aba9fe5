<script setup lang="ts">
const authStore = useAuthStore()
const { isLoggedIn, isLoggingIn } = storeToRefs(authStore)

definePageMeta({
    layout: false,
    layoutTransition: true,
})
</script>

<template>
    <div>
        <NuxtLayout name="non-translate">
            <div class="max-w-lg mx-auto p-4 md:p-0 mt-20">
                <!-- <SignInSignUpRequire
                    class="p-14"
                    :title="$t('Want to see your translation history?')"
                    :message="$t('You can see your translation history here. Sign in or sign up to see your history.')"
                /> -->
                <img src="~/assets/images/soon.png" class="w-full" />
            </div>
        </NuxtLayout>
    </div>
</template>

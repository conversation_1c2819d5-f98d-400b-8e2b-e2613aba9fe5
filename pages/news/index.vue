<script setup lang="ts">
definePageMeta({
  layout: false,
  layoutTransition: true,
});

const newsStore = useNewsStore();
const { news } = storeToRefs(newsStore);
</script>

<template>
  <div>
    <ULandingSection :title="$t('News')">
      <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div class="lg:col-span-5">
          <ULandingCard
            icon="i-stash-payment-link"
            :title="$t(news[0].title)"
            :description="$t(news[0].description)"
            color="primary"
            orientation="vertical"
            :to="`/news/${news[0].id}`"
            :ui="{
              title: 'break-words text-ellipsis	',
            }"
          >
            <img :src="news[0].image" class="w-full rounded-md" />
          </ULandingCard>
        </div>
        <div class="lg:col-span-7 flex flex-col gap-6">
          <ULandingCard
            icon="i-noto-mobile-phone"
            :title="$t(news[1].title)"
            :description="$t(news[1].description)"
            color="primary"
            orientation="horizontal"
            :to="`/news/${news[1].id}`"
            :ui="{
              title: 'break-words text-ellipsis	',
            }"
          >
            <img :src="news[1].image" class="w-full rounded-md" />
          </ULandingCard>
          <ULandingCard
            icon="i-eos-icons-api-outlined"
            :title="$t(news[2].title)"
            :description="$t(news[2].description)"
            color="primary"
            orientation="horizontal"
            :to="`/news/${news[2].id}`"
            :ui="{
              title: 'break-words text-ellipsis	',
            }"
          >
            <img :src="news[2].image" class="w-full rounded-md" />
          </ULandingCard>
        </div>
      </div>
    </ULandingSection>
  </div>
</template>

<style>
ol {
  list-style-type: decimal;
  padding-left: 20px;
}
</style>

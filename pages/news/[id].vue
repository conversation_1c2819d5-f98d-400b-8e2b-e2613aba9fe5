<script setup lang="ts">
definePageMeta({
  layout: false,
  layoutTransition: true,
});

const newsStore = useNewsStore();
const { news } = storeToRefs(newsStore);
const route = useRoute();

const newDetail = computed(() => {
  return news.value.find((item) => item.id === route.params.id);
});
</script>

<template>
  <div class="max-w-4xl mx-auto px-6 mt-10">
    <UPageHero
      :title="$t(newDetail?.title || 'News Detail')"
      align="center"
      :ui="{
        title: 'lg:text-3xl',
        icon: {
          wrapper: 'w-full',
        },
      }"
    >
      <template #icon>
        <div class="w-full flex flex-1 mb-4">
          <UButton
            icon="i-ep-back"
            size="md"
            color="white"
            variant="solid"
            :label="$t('Back to news')"
            :trailing="false"
            @click="() => navigateTo('/news')"
          />
        </div>
      </template>
      <div v-html="$t(newDetail?.content || '')" />

      <img
        :src="newDetail?.image"
        class="w-full rounded-md shadow-xl ring-1 ring-gray-300 dark:ring-gray-700"
      />
    </UPageHero>
  </div>
</template>

<style>
ol {
  list-style-type: decimal;
  padding-left: 20px;
}
</style>

<script setup lang="ts">
const { t } = useI18n();
definePageMeta({
    layout: false,
})

const voiceLibraryStore = useVoiceLibraryStore();

onMounted(() => {
    voiceLibraryStore.fetchVoiceLibraryByType('user_voice' as string);
})
</script>

<template>
    <div>
        <NuxtLayout name="non-translate">
            <ULandingSection class="sm:pt-20">
                <div>
                    <div class="text-2xl font-semibold">{{t('My Voices')}}</div>
                    <div>
                        {{t("Your creative AI toolkit. Design entirely new synthetic voices from scratch. Clone your own voice or a voice you have a permission and rights to. Only you have access to the voices you create.")}}
                    </div>

                    <div class="py-4 grid grid-cols-1 md:grid-cols-3 sm:grid-cols-2 xs:grid-cols-2 gap-4 w-full">
                        <MyVoiceCard />
                    </div>
                </div>
            </ULandingSection>
        </NuxtLayout>
    </div>
</template>


<script setup lang="ts">
import type {
    iContent,
    iContentDubbingStudioBlock,
    iContentAiTextToSpeechBlock,
    iContentChangeVoiceSpeechToSpeechBlock,
    iContentLongFormVoiceGenerationBlock,
    iContentPoweredCutResearchBlock,
    iContentReviewBlock,
    iContentAiVoiceGeneratorLanguageBlock,
    iContentAiVoiceLabBlock,
    iContentFaqBlock,
} from '~/types'

definePageMeta({
    layout: false,
})

const { pending, data: blocks } = useFetch('/api/cms/contents/home/<USER>', {
    lazy: true,
})

const findBlockBySection = (section: string) => {
    return blocks.value?.find((block: iContent) => block.section === section) as iContent
}

const introdutionCommonBlock = computed(() => findBlockBySection('natural_and_ai_text_to_speech'))
const aiTextToSpeechBlock = computed(() => findBlockBySection('ai_text_to_speech'))
const dubbingStudioBlock = computed(() => findBlockBySection('dubbing_studio'))
const changeVoiceSpeechToSpeechBlock = computed(() => findBlockBySection('change_voice_speech_to_speech'))
const longFormVoiceGenerationBlock = computed(() => findBlockBySection('long_form_voice_generation'))
const poweredCutResearchBlock = computed(() => findBlockBySection('powered_cut_research'))
const reviewBlock = computed(() => findBlockBySection('review'))
const aiVoiceGeneratorLanguageBlock = computed(() => findBlockBySection('ai_voice_generator_language'))
const aiVoiceLabBlock = computed(() => findBlockBySection('ai_voice_lab'))
const faqBlock = computed(() => findBlockBySection('faq'))
</script>

<template>
    <div>
        <NuxtLayout name="non-translate">
            <ULandingSection class="sm:pt-25">
                <div class="flex flex-col gap-10">
                    <SeoHomeIntrodutionCommonBlock v-bind="introdutionCommonBlock" />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeAiTextToSpeechBlock v-bind="aiTextToSpeechBlock as iContentAiTextToSpeechBlock" />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeAiVoiceGeneratorLanguageBlock
                        v-bind="aiVoiceGeneratorLanguageBlock as iContentAiVoiceGeneratorLanguageBlock"
                    />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeAiVoiceLabBlock v-bind="aiVoiceLabBlock as iContentAiVoiceLabBlock" />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeDubbingStudioBlock v-bind="dubbingStudioBlock as iContentDubbingStudioBlock" />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeVoiceSpeechToSpeechBlock
                        v-bind="changeVoiceSpeechToSpeechBlock as iContentChangeVoiceSpeechToSpeechBlock"
                    />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeLongFormVoiceGeneraBlock
                        v-bind="longFormVoiceGenerationBlock as iContentLongFormVoiceGenerationBlock"
                    />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomePoweredCutResearchBlock
                        v-bind="poweredCutResearchBlock as iContentPoweredCutResearchBlock"
                    />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeReviewBlock v-bind="reviewBlock as iContentReviewBlock" />
                </div>
            </ULandingSection>
            <ULandingSection class="sm:pt-3">
                <div class="flex flex-col gap-10">
                    <SeoHomeFaqBlock v-bind="faqBlock as iContentFaqBlock" />
                </div>
            </ULandingSection>
        </NuxtLayout>
    </div>
</template>

<script setup lang="ts">
import * as validators from '@vuelidate/validators'
import { useVuelidate } from '@vuelidate/core'
const i18n = useI18n()
const { t, locale } = useI18n()
const authStore = useAuthStore()
const appStore = useAppStore()
const route = useRoute()
const { createI18nMessage } = validators
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const { isLoggingIn, signInError, isSuperUser } = storeToRefs(authStore)
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)
const minLength = (v: any) => withI18nMessage(validators.minLength(v))
const runtimeConfig = useRuntimeConfig()
definePageMeta({
    layout: 'auth',
    layoutTransition: true,
})

useSeoMeta({
    title: 'Login',
})

const fields = computed(
    () =>
        [
            {
                type: 'text',
                name: 'username',
                label: t('Email'),
                placeholder: t('Enter your email'),
            },
            {
                type: 'password',
                name: 'password',
                label: t('Password'),
                placeholder: t('Enter your password'),
            },
        ] as any[]
)

const formLoginRules = computed(() => ({
    username: { required, email },
    password: { required, minLength: minLength(6) },
}))

const validate = async (state: any) => {
    const v$ = useVuelidate(formLoginRules, state)
    const result = await v$.value.$validate()
    const errors = [] as any[]
    v$.value.$errors.forEach((error: any) => {
        errors.push({ path: error.$propertyPath, message: error.$message })
    })
    return errors
}

const providers = computed(() => {
    return [
        {
            label: t('Continue with Google'),
            icon: 'i-devicon-google',
            color: 'white' as const,
            click: async () => {
                const { invitation_code } = route.query
                const result = await authStore.signInWithGoogle(invitation_code as string)
                if (result) {
                    navigateTo('/')
                }
            },
            show: false,
        },
        {
            label: t('Continue with Apple'),
            icon: 'i-devicon-apple',
            color: 'white' as const,
            click: async () => {
                const { invitation_code } = route.query
                const result = await authStore.signInWithApple(invitation_code as string)
                if (result) {
                    navigateTo('/')
                }
            },
            show: runtimeConfig.public.features.appleLogin || isSuperUser.value,
        },
    ].filter((provider) => provider.show)
})

async function onSubmit(data: any) {
    const success = await authStore.login({
        username: data.username,
        password: data.password,
        remember_me: true,
    })
    if (success) {
        navigateTo('/')
    }
}

onMounted(() => {
    if (runtimeConfig.public.features.appleLogin || isSuperUser.value) {
        window?.AppleID?.auth?.init({
            clientId: runtimeConfig.public.NUXT_APPLE_LOGIN_CLIENT_ID,
            scope: 'name email',
            redirectURI: runtimeConfig.public.NUXT_APPLE_LOGIN_REDIRECT_URI,
            state: (Math.random() + 1).toString(36).substring(7),
            nonce: (Math.random() + 1).toString(36).substring(7),
            usePopup: true,
        })
    }
})

import { useTokenClient, type AuthCodeFlowSuccessResponse, type AuthCodeFlowErrorResponse } from 'vue3-google-signin'

const handleOnSuccess = (response: AuthCodeFlowSuccessResponse) => {
    authStore.processGoogleRedirectResult(response, response.access_token).then((success) => {
        if (success) {
            // Force reload user info
            authStore.fetchUserInfo()
        }
    })
}

const handleOnError = (errorResponse: AuthCodeFlowErrorResponse) => {
    console.log('Error: ', errorResponse)
}

const { isReady, login } = useTokenClient({
    onSuccess: handleOnSuccess,
    onError: handleOnError,
    // other options
})
</script>

<!-- eslint-disable vue/multiline-html-element-content-newline -->
<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
    <UCard class="mx-auto max-w-sm w-full bg-white/75 dark:bg-gray-900/80 backdrop-blur">
        <UAuthForm
            :key="locale"
            :fields="fields"
            :validate="validate"
            :providers="providers"
            :title="$t('Welcome back')"
            align="top"
            icon="i-heroicons-lock-closed"
            :ui="{ base: 'text-center', footer: 'text-center' }"
            :submit-button="{
                trailingIcon: 'i-heroicons-arrow-right-20-solid',
                label: $t('Continue'),
            }"
            :loading="isLoggingIn"
            @submit="onSubmit"
            :divider="$t('or')"
        >
            <template #description>
                {{ $t("Don't have an account?") }}
                <NuxtLink to="/signup" class="text-primary font-medium"> {{ $t('Sign up') }} </NuxtLink>.
                <div class="text-left mt-4">
                    <UAlert
                        v-if="signInError"
                        :description="$t(signInError.detail.error_code)"
                        :title="$t('Login failed')"
                        color="yellow"
                        variant="subtle"
                    />
                </div>
            </template>

            <template #password-hint>
                <NuxtLink to="/account-recovery" class="text-primary font-medium" tabindex="-1">
                    {{ $t('Forgot password?') }}
                </NuxtLink>
            </template>

            <template #footer>
                <UButton
                    icon="flat-color-icons:google"
                    size="sm"
                    color="gray"
                    variant="solid"
                    :label="$t('Continue with Google')"
                    :trailing="false"
                    :disabled="!isReady"
                    @click="() => login()"
                    block
                    :loading="isLoggingIn"
                    class="mb-4"
                />
                <label for="terms" class="font-light text-gray-500 dark:text-gray-300">
                    <i18n-t keypath="By {0}, you agree to our {1}." tag="span">
                        <span>
                            {{ $t('signing in') }}
                        </span>
                        <a
                            @click="appStore.setShowTermsOfServiceModal(true)"
                            class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                        >
                            {{ $t('Terms of Service') }}
                        </a>
                    </i18n-t>
                </label>
            </template>
        </UAuthForm>
    </UCard>
</template>

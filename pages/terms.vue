<script setup lang="ts">
definePageMeta({
    layout: false,
    layoutTransition: true,
})
const terms = computed(() => {
    return useTerms()
})
</script>

<template>
    <div>
        <NuxtLayout name="non-translate">
            <ULandingSection
                :title="$t('Terms of Service')"
                :headline="$t('Text To Speech OpenAI')"
                :description="
                    $t(
                        'This Terms of Service is meant to help you understand what information we collect, why we collect it, and how you can update, manage, export, and delete your information.'
                    )
                "
            >
                <ul class="p-6 space-y-3 text-base leading-relaxed text-gray-500 dark:text-gray-400">
                    <li v-for="term in terms">
                        <strong>{{ term.title }}: </strong>
                        <span>
                            {{ term.content }}
                        </span>
                    </li>
                </ul>
            </ULandingSection>
        </NuxtLayout>
    </div>
</template>

<style>
ol {
    list-style-type: decimal;
    padding-left: 20px;
}
</style>

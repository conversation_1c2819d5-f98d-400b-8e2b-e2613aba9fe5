<script setup lang="ts">
definePageMeta({
    layout: false,
    layoutTransition: true,
})
const policies = computed(() => {
    return usePolicy()
})

const wrapTag = (content: string) => {
    return `<p>${content}</p>`
}
</script>

<template>
    <div>
        <NuxtLayout name="non-translate">
            <ULandingSection :title="$t('Privacy Policy')" :headline="$t('Text To Speech OpenAI')">
                <ol class="p-6 list-decimal space-y-3 text-base leading-relaxed text-gray-500 dark:text-gray-400">
                    <li v-for="policy in policies">
                        <strong>{{ policy.title }}: </strong>
                        <div v-html="wrapTag(policy.content)"/>
                    </li>
                </ol>
            </ULandingSection>
        </NuxtLayout>
    </div>
</template>

<style>
ol {
    list-style-type: decimal;
    padding-left: 20px;
}
</style>

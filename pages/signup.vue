<script setup lang="ts">
import * as validators from '@vuelidate/validators'
import { useVuelidate } from '@vuelidate/core'
const i18n = useI18n()
const { t, locale } = useI18n()
const authStore = useAuthStore()
const appStore = useAppStore()
const route = useRoute()
const { createI18nMessage } = validators
const withI18nMessage = createI18nMessage({ t: i18n.t.bind(i18n) })
const { isSigningUp, registerError, referralInfo, isSuperUser } = storeToRefs(authStore)
const required = withI18nMessage(validators.required)
const email = withI18nMessage(validators.email)
const minLength = (v: any) => withI18nMessage(validators.minLength(v))
const sameAs = (v: any) => withI18nMessage(validators.sameAs(v))
const runtimeConfig = useRuntimeConfig()
definePageMeta({
    layout: 'auth',
    layoutTransition: true,
})

const fields = computed(
    () =>
        [
            {
                type: 'text',
                name: 'full_name',
                label: t('Your name'),
                placeholder: t('<PERSON>'),
            },
            {
                type: 'text',
                name: 'email',
                label: t('Your email'),
                placeholder: t('<EMAIL>'),
            },
            {
                type: 'password',
                name: 'password',
                label: t('Password'),
                placeholder: t('••••••••'),
            },
            {
                type: 'password',
                name: 'confirm_password',
                label: t('Confirm password'),
                placeholder: t('••••••••'),
            },
            {
                type: 'text',
                name: 'invitation_code',
                label: t('Invitation code (Optional)'),
                placeholder: t('abcdef1234567890'),
            },
        ] as any[]
)

const formSignUpState = computed(() => ({
    full_name: { required },
    email: { required, email },
    password: { required, minLength: minLength(6) },
    confirm_password: { required, minLength: minLength(6) },
    invitation_code: { minLength: minLength(11) },
}))

const validate = async (state: any) => {
    const v$ = useVuelidate(formSignUpState, state)
    const result = await v$.value.$validate()
    const errors = [] as any[]
    v$.value.$errors.forEach((error: any) => {
        errors.push({ path: error.$propertyPath, message: error.$message })
    })

    if (state.password !== state.confirm_password) {
        errors.push({ path: 'confirm_password', message: t('Passwords do not match') })
    }
    return errors
}
const providers = computed(() => {
    return [
        {
            label: t('Continue with Google'),
            icon: 'i-devicon-google',
            color: 'white' as const,
            click: async () => {
                const { invitation_code } = route.query
                const result = await authStore.signInWithGoogle(invitation_code as string)
                if (result) {
                    navigateTo('/')
                }
            },
            show: false,
        },
        {
            label: t('Continue with Apple'),
            icon: 'i-devicon-apple',
            color: 'white' as const,
            click: async () => {
                const { invitation_code } = route.query
                const result = await authStore.signInWithApple(invitation_code as string)
                if (result) {
                    navigateTo('/')
                }
            },
            show: runtimeConfig.public.features.appleLogin || isSuperUser.value,
        },
    ].filter((provider) => provider.show)
})

const signedUp = ref(false)
const countDown = ref(5)
async function onSubmit(data: any) {
    console.log('🚀 ~ onSubmit ~ data:', data)
    const success = await authStore.signup({
        full_name: data.full_name,
        email: data.email,
        password: data.password,
        invitation_code: route.query?.invitation_code || data.invitation_code,
        origin: (route.query?.origin as string) || undefined,
        ...referralInfo.value,
    })
    if (success) {
        signedUp.value = true
        setInterval(() => {
            countDown.value--
        }, 1000)

        setTimeout(() => {
            navigateTo('/')
        }, 5000)
    }
}

onMounted(() => {
    // https://ttsopenai.com/signup?pscd=affiliate.ttsopenai.com&ps_partner_key=ZTE4YzhmYzU0YWU3&ps_xid=c8mKKCWtiIKlEQ&gsxid=c8mKKCWtiIKlEQ&gspk=ZTE4YzhmYzU0YWU3
    const { ps_partner_key, ps_xid, gsxid, gspk } = route.query
    if (ps_partner_key || ps_xid || gsxid || gspk) {
        referralInfo.value = {
            ps_partner_key,
            ps_xid,
            gsxid,
            gspk,
        }
    }
})

import { useTokenClient, type AuthCodeFlowSuccessResponse, type AuthCodeFlowErrorResponse } from 'vue3-google-signin'

const handleOnSuccess = (response: AuthCodeFlowSuccessResponse) => {
    authStore.processGoogleRedirectResult(response, response.access_token).then((success) => {
        if (success) {
            // Force reload user info
            authStore.fetchUserInfo()
        }
    })
}

const handleOnError = (errorResponse: AuthCodeFlowErrorResponse) => {
    console.log('Error: ', errorResponse)
}

const { isReady, login } = useTokenClient({
    onSuccess: handleOnSuccess,
    onError: handleOnError,
    // other options
})
</script>

<!-- eslint-disable vue/multiline-html-element-content-newline -->
<!-- eslint-disable vue/singleline-html-element-content-newline -->
<template>
    <UCard class="mx-auto max-w-sm w-full bg-white/75 dark:bg-gray-900/80 backdrop-blur">
        <UAuthForm
            v-if="signedUp"
            :title="$t('Success!')"
            align="top"
            icon="i-mdi-account-check"
            :ui="{ base: 'text-center', footer: 'text-center', form: 'space-y-2' }"
        >
            <template #description>
                <div class="mt-2 mb-4 text-sm">
                    {{ $t('Your account has been created, ') }}
                    <span class="text-yellow-800 dark:text-yellow-300">{{
                        $t('please check your mailbox to activate your account.')
                    }}</span>
                </div>
                <div class="mt-2 mb-4 text-sm">
                    {{
                        $t('After few seconds, you will be redirected to the home page.', {
                            value: +countDown,
                        })
                    }}
                </div>
                <div>
                    <button
                        @click="navigateTo('/')"
                        type="button"
                        class="text-white bg-green-800 hover:bg-green-900 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                    >
                        <UIcon name="material-symbols:home" class="mr-2" />
                        {{ $t('Redirect now') }}
                    </button>
                </div>
            </template>
        </UAuthForm>
        <BaseAuthForm
            v-else
            :key="locale"
            :fields="fields"
            :validate="validate"
            :providers="providers"
            :divider="$t('or')"
            :title="$t('Create an account')"
            align="top"
            icon="i-ic-twotone-account-circle"
            :ui="{ base: 'text-center', footer: 'text-center', form: 'space-y-3' }"
            :submit-button="{ label: $t('Create an account') }"
            :loading="isSigningUp"
            @submit="onSubmit"
            :invitationCode="route.query?.invitation_code || ''"
        >
            <template #description>
                {{ $t('Already have an account?') }}
                <NuxtLink to="/signin" class="text-primary font-medium"> {{ $t('Sign in') }} </NuxtLink>.
                <div class="text-left mt-4">
                    <UAlert
                        v-if="registerError"
                        :description="$t(registerError.detail.error_code)"
                        :title="$t('Signup failed')"
                        color="yellow"
                        variant="subtle"
                    />
                </div>
            </template>

            <template #footer>
                <UButton
                    icon="flat-color-icons:google"
                    size="sm"
                    color="gray"
                    variant="solid"
                    :label="$t('Continue with Google')"
                    :trailing="false"
                    :disabled="!isReady"
                    @click="() => login()"
                    block
                    :loading="isLoggingIn"
                    class="mb-4"
                />
                <label for="terms" class="font-light text-gray-500 dark:text-gray-300">
                    <i18n-t keypath="By {0}, you agree to our {1}." tag="span">
                        <span>
                            {{ $t('signing up') }}
                        </span>
                        <a
                            @click="appStore.setShowTermsOfServiceModal(true)"
                            class="font-medium text-primary-600 hover:underline dark:text-primary-500 cursor-pointer"
                        >
                            {{ $t('Terms of Service') }}
                        </a>
                    </i18n-t>
                </label>
            </template>
        </BaseAuthForm>
    </UCard>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
definePageMeta({
  layout: false,
});

const storyStore = useStoryStore();
const { stories, dragArea, activeStory, showVoiceLibrariesModal } = storeToRefs(
  storyStore
);

const drag = ref(false);
const dragOptions = computed(() => ({
  animation: 200,
  group: "description",
  disabled: false,
  ghostClass: "ghost",
  dragClass: "no-move",
}));

const onStartDrag = () => {
  drag.value = true;
};

const onEndDrag = () => {
  drag.value = false;
};

const onClickOutside = () => {
  // activeStory.value = null;
};

const onSelectVoice = (voice_id: string) => {
  storyStore.setVoiceToActiveStory(voice_id);
  showVoiceLibrariesModal.value = false;
  // activeStory.value = null;
};
</script>
<template>
  <div>
    <NuxtLayout name="story">
      <draggable
        v-if="stories.length > 0"
        class="dragArea list-group w-full md:h-[calc(100vh-268px)] pb-4 overflow-auto scrollbar-thin"
        item-key="_id"
        ref="dragArea"
        handle=".handle"
        :component-data="{
          tag: 'div',
          type: 'transition-group',
          name: !drag ? 'flip-list' : null,
        }"
        :list="stories"
        v-bind="dragOptions"
        @start="onStartDrag"
        @end="onEndDrag"
        :key="stories.length"
        direction="vertical"
        v-click-outside="onClickOutside"
      >
        <template #item="{ element, index }">
          <div class="group" :key="index" :id="element?.id">
            <div
              class="flex flex-row items-center justify-between space-x-2 pr-2 py-2.5 dark:bg-gray-900 dark:border-gray-800 border-b border-gray-200 cursor-pointer"
              :class="{
                'hover:bg-gray-50': activeStory?.id !== element.id,
              }"
            >
              <div class="h-full flex flex-row items-center w-full">
                <div class="h-full w-5">
                  <div class="h-full hidden group-hover:block">
                    <UIcon
                      name="i-akar-icons-drag-vertical"
                      class="text-gray-500 text-lg handle"
                    />
                  </div>
                </div>

                <StoryItem
                  class="flex-1"
                  :active="activeStory?.id === element.id"
                  v-model="stories[index]"
                  @delete="storyStore.removeStory"
                  @edit="activeStory = element"
                  @duplicate="storyStore.duplicateStory"
                  @select="storyStore.selectStory"
                  :index="index"
                  :key="element.id"
                />
              </div>
            </div>
          </div>
        </template>
        <template #footer>
          <div class="pl-4 pr-2 pt-4">
            <div
              @click="storyStore.addStory"
              class="flex flex-row items-center justify-center bg-gray-00 dark:bg-gray-900 hover:bg-gray-50 p-4 border rounded-md border-gray-300 dark:border-gray-800 border-dashed dark:hover:bg-gray-700 cursor-pointer"
            >
              <UIcon name="i-heroicons-plus" class="text-gray-500 text-3xl" />
            </div>
          </div>
        </template>
      </draggable>

      <div class="">
        <div
          v-if="!stories.length"
          class="flex flex-col items-center justify-center h-full"
        >
          <div class="pt-14 flex flex-col items-center justify-center mb-2">
            <div
              class="h-32 w-32 rounded-full flex items-center justify-center bg-gray-100 mb-5"
            >
              <UIcon
                name="i-material-symbols:voice-chat-rounded"
                class="text-6xl text-gray-400"
              />
            </div>
            <div class="text-gray-500 text-lg">
              {{ $t("Create your first story") }}
            </div>
            <div class="text-gray-500 text-sm px-6">
              {{ $t("You can create manually on UI or import from a file") }}
            </div>
          </div>
          <div class="flex flex-col gap-3 py-2">
            <UButton
              icon="i-heroicons-plus"
              size="sm"
              color="primary"
              variant="outline"
              label="Add Conversation"
              :trailing="false"
              @click="storyStore.addStory"
              class="justify-center"
            />
            <div class="flex flex-row gap-2">
              <StoryImportCsv />
              <StoryImportSrt />
            </div>
            <UTooltip :text="$t('Click to download template file')">
              <a
                @click="storyStore.downloadTemplate"
                class="mx-auto text-xs text-center cursor-pointer underline"
                >{{ $t("csv template") }}</a
              >
            </UTooltip>
          </div>
        </div>
      </div>
      <StoryMakerMenu v-if="stories.length" />
      <VoiceLibraryModal @select="onSelectVoice" />
      <StoryBatchUpdateModal />
    </NuxtLayout>
  </div>
</template>

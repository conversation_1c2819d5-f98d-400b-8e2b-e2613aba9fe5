<template>
    <div class="px-3">
        <BaseAudioPlayer v-if="gptsResult?.url" class="mt-10 z-50" :url="gptsResult?.url" :data="gptsResult?.history">
            <template #description>
                <div class="text-sm text-yellow-500 my-3">
                    {{ $t('This audio will be deleted after 7 days.') }}
                </div>
            </template>
        </BaseAudioPlayer>
    </div>
</template>

<script setup lang="ts">
const route = useRoute()
const gptsStore = useGptsStore()
const { gptsResult } = storeToRefs(gptsStore)
onMounted(() => {
    const uuid = route.params.uuid
    gptsStore.fetchGptsResult(uuid as string)
})
</script>

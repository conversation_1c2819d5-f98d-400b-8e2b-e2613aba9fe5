<script setup lang="ts">
definePageMeta({
    layout: false,
    layoutTransition: true,
})

import ProfileCard from '~/components/ProfileCard.vue'

const route = useRoute()
const hideLeftMenu = computed(() => route.path === 'pricing')
</script>

<template>
    <div>
        <NuxtLayout name="non-translate">
            <div class="flex-1 min-h-[calc(100vh-130px)] pt-2 md:pt-6 w-full max-w-screen-xl mx-auto">
                <div class="grid grid-cols-12 h-full pt-14">
                    <div v-if="!hideLeftMenu" class="hidden col-span-4 lg:block pt-10">
                        <ProfileCard class="md:sticky top-10 lg:px-6 md:px-2" :canCancelSubscription="true" />
                    </div>
                    <div
                        class="col-span-12 lg:col-span-8 md:border dark:border-gray-600 bg-white dark:bg-gray-800 dark:text-gray-300"
                        :class="{
                            'lg:col-span-3': hideLeftMenu,
                        }"
                    >
                        <NuxtPage />
                    </div>
                </div>
            </div>
        </NuxtLayout>
    </div>
</template>

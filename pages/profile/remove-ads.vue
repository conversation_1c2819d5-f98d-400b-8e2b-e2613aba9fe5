<template>
    <div v-if="user" class="space-y-3 p-0 md:p-4">
        <div>
            <div class="p-6">
                <div class="">
                    <h4 class="font-semibold text-lg">
                        {{ $t('Remove Ads!') }}
                    </h4>
                    <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
                    <UCarousel
                        :items="months"
                        :ui="{
                            container: 'gap-6 py-4 px-2',
                        }"
                        arrows
                    >
                        <template #default="{ item }">
                            <RemoveAdsCard v-bind="item" @buy="buyNoAds" />
                        </template>

                        <template #prev="{ onClick, disabled }">
                            <button :disabled="disabled" @click="onClick">
                                {{ $t('Prev') }}
                            </button>
                        </template>

                        <template #next="{ onClick, disabled }">
                            <button :disabled="disabled" @click="onClick">
                                {{ $t('Next') }}
                            </button>
                        </template>
                    </UCarousel>
               
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)

const months = [
    {
        month: 1,
    },
    {
        month: 2,
    },
    {
        month: 3,
    },
    {
        month: 6,
    },
    {
        month: 9,
    },
    {
        month: 12,
    },
]

const paymentsStore = usePaymentsStore()
const { paymentInfo, isOpen, noAdsProduct } = storeToRefs(paymentsStore)

const buyNoAds = (item: any) => {
    paymentInfo.value = {
        price: noAdsProduct.value.price_divide_100 * item.month,
        quantity: item.month,
        id: noAdsProduct.value?.id,
        type: 'no-ads',
    }
    isOpen.value = true
}
</script>

<script setup lang="ts"></script>

<template>
    <section class="bg-white dark:bg-gray-800 p-0 md:p-4">
        <div class="px-4 sm:px-0 mx-auto max-w-screen-xl lg:px-0">
            <div class="mx-auto max-w-screen-md text-center mb-8 lg:mb-12 pt-6">
                <h2 class="mb-4 text-3xl tracking-tight font-extrabold text-gray-900 dark:text-white">
                    {{ $t('Change your plan') }}
                </h2>
                <div>
                    asdasd
                </div>
            </div>
  
            <div class="space-y-8 lg:grid lg:grid-cols-3 lg:space-y-0">
                <!-- Pricing Card -->
                <div
                    v-for="plan in plans"
                    class="flex flex-col p-0 text-center text-gray-900 border border-white dark:border-gray-800 bg-white rounded-lg p-4 pt-6 dark:bg-gray-800 dark:text-white hover:shadow-lg !hover:border-primary-600"
                    :class="{
                        '!border-primary-600': user?.current_plan === plan.id,
                    }"
                >
                    <h3 class="mb-0 text-2xl font-semibold">{{ plan.title }}</h3>
                    <div class="flex justify-center items-baseline mt-8">
                        <span class="mr-2 text-4xl font-extrabold">${{ plan.price }}</span>
                        <span class="text-gray-500 dark:text-gray-400">/{{ $t('month') }}</span>
                    </div>
                    <div class="flex justify-center items-baseline mb-8 mt-2">
                        <span class="mr-1 text-xl font-extrabold text-primary-500">{{ plan.token }}</span>
                        <span class="text-gray-500 dark:text-gray-400">{{ $t('token') }}</span>
                    </div>
                    <!-- List -->
                    <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                        <li v-for="feature in plan.features" class="flex items-center space-x-2">
                            <!-- Icon -->
                            <svg
                                class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span>{{ feature }}</span>
                        </li>
                    </ul>
                    <a
                        @click="changePlan(plan)"
                        class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                        :class="{
                            '!bg-primary-200 !text-primary-600 cursor-not-allowed': user?.current_plan === plan.id,
                        }"
                    >
                        {{ user?.current_plan === plan.id ? $t('Current plan') : $t('Get started') }}
                    </a>
                </div>
            </div>
        </div>
    </section>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useAuthStore } from '~~/stores/auth'

import { loadScript } from '@paypal/paypal-js'
import PaymentModal from '~/base-components/PaymentModal.vue'
import { ModalsContainer, useModal } from 'vue-final-modal'

const authStore = useAuthStore()
const { isLoggingIn, user, isLoggedIn } = storeToRefs(authStore)
const { t } = useI18n({ useScope: 'global' })

const { open, close, options } = useModal({
    component: PaymentModal,
    attrs: {
        onClose() {
            close()
        },
        onSubmit() {
            close()
        },
    },
})

const plans = [
    {
        id: 'Starter',
        product_id: 'PP001',
        title: t('Starter'),
        price: 6,
        token: '1,000,000',
        features: [
            t('GPT-3.5'),
            t('GPT-4'),
            t('documents/month', { num: 20 }),
            t('Up to size per document', { size: '30MB' }),
            t('Bonus when you recharge:', { bonus: '50%' }),
        ],
    },
    {
        id: 'Professional',
        product_id: 'PP002',
        title: t('Professional'),
        price: 30,
        token: '5,000,000',
        features: [
            t('GPT-3.5'),
            t('GPT-4'),
            t('documents/month', { num: 100 }),
            t('Up to size per document', { size: '60MB' }),
            t('Bonus when you recharge:', { bonus: '70%' }),
        ],
    },
    {
        id: 'Premium',
        product_id: 'PP003',
        title: t('Premium'),
        price: 100,
        token: '17,000,000',
        features: [
            t('GPT-3.5'),
            t('GPT-4'),
            t('Unlimited document translation'),
            t('Up to size per document', { size: '100MB' }),
            t('Bonus when you recharge:', { bonus: '100%' }),
        ],
    },
]

const changePlan = (plan) => {
    if (user.value.current_plan === plan.id) {
        return
    }
    options.attrs.type = 'change-plan'
    options.attrs.plan = plan
    options.attrs.id = plan.product_id
    options.attrs.quantity = 1
    open()
}
</script>

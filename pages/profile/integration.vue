<template>
    <div class="space-y-3 p-0 md:p-4">
        <div>
            <div class="p-4">
                <div class="">
                    <UHorizontalNavigation :links="links" class="border-b border-gray-200 dark:border-gray-800" />
                    <div class="pt-4 px-2">
                        <NuxtPage />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const route = useRoute()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()
const links = computed(() => {
    return [
        [
            {
                label: t('API keys'),
                icon: 'i-fluent:lock-closed-key-24-regular',
                to: '/profile/integration/api-keys',
            },
            {
                label: t('Webhook'),
                icon: 'i-mdi:webhook',
                to: '/profile/integration/webhook',
            },
            {
                label: t('Webhook history'),
                icon: 'i-material-symbols:history',
                to: '/profile/integration/webhook-history',
            },
        ],
        [
            {
                label: t('API reference'),
                icon: 'i-material-symbols:api',
                to: runtimeConfig.public.NUXT_DOCS_URL + '/getting-started',
                target: '_blank',
                external: true,
            },
        ],
    ]
})
</script>

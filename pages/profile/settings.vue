<script setup lang="ts">
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const userStore = useUserStore()

const { loadings } = storeToRefs(userStore)

const state = reactive<{ [key: string]: boolean }>({
    email_ads_flag: user.value?.user_settings?.email_ads_flag === 'on',
})

const { t } = useI18n()
const sections = computed(() => {
    return [
        // {
        //     title: t('Notification channels'),
        //     description: t('Where can we notify you?'),
        //     fields: [
        //         {
        //             name: 'email',
        //             label: t('Email'),
        //             description: t('Receive vocalize results in your email.'),
        //         },
        //         {
        //             name: 'web',
        //             label: t('Web notifications'),
        //             description: t('Receive notifications in your browser.'),
        //         },
        //         {
        //             name: 'mobile',
        //             label: t('Mobile notifications'),
        //             description: t("Receive notifications on your mobile's app."),
        //         }
        //     ],
        // },
        {
            title: t('Notification settings'),
            description: t('What kind of notifications do you want to receive?'),
            fields: [
                {
                    name: 'email_ads_flag',
                    label: t('Email notifications'),
                    description: t('Receive emails about new features, updates, etc.'),
                },
                // {
                //     name: 'important_updates',
                //     label: 'Important updates',
                //     description: 'Receive emails about important updates like security fixes, maintenance, etc.',
                // },
            ],
        },
    ]
})

async function onChange() {
    // Do something with data
    Object.keys(state).forEach((key) => {
        userStore.updateUserSettings(key, state[key] ? 'on' : 'off')
        user.value.user_settings[key] = state[key] ? 'on' : 'off'
    })
}
</script>

<template>
    <div v-if="user" class="space-y-3 p-0 md:p-4">
        <div class="p-6">
            <div class="">
                <h4 class="font-semibold text-lg">
                    {{ $t('Settings') }}
                </h4>
                <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
                <UDashboardPanelContent class="p-0 pb-24 divide-y divide-gray-200 dark:divide-gray-800">
                    <UDashboardSection
                        v-for="(section, index) in sections"
                        :key="index"
                        :title="section.title"
                        :description="section.description"
                        orientation="horizontal"
                        class="px-1 py-6"
                    >
                        <UCard
                            :ui="{
                                body: { base: 'divide-y divide-gray-200 dark:divide-gray-800 gap-4 flex flex-col' },
                            }"
                        >
                            <UFormGroup
                                v-for="field in section.fields"
                                :key="field.name"
                                :name="field.name"
                                :label="field.label"
                                :description="field.description"
                                class="flex items-center justify-between pt-4 first:pt-0 gap-2"
                                :ui="{ container: 'flex' }"
                            >
                                <UToggle :loading="loadings.updateUserSettings[field.name]" v-model="state[field.name]" size="md" @update:model-value="onChange" />
                            </UFormGroup>
                        </UCard>
                    </UDashboardSection>
                </UDashboardPanelContent>
            </div>
        </div>
    </div>
</template>

<template>
    <div class="text-sm">
        <div class="flex flex-col gap-2">
            <p>
                {{
                    $t(
                        `We'll send a POST request to the URL you provide below every time a vocalization is completed. This allows you to keep your system in sync with our vocalization service.`
                    )
                }}
            </p>
            <div class="flex flex-row gap-4">
                <UInput
                    class="flex-1"
                    placeholder="URL"
                    icon="i-mdi:webhook"
                    autocomplete="off"
                    size="lg"
                    v-model="webhookUrlState"
                    :ui="{ icon: { trailing: { pointer: '' } }, rounded: 'rounded-lg' }"
                >
                    <template #trailing>
                        <UButton
                            color="gray"
                            variant="link"
                            icon="i-heroicons-x-mark-20-solid"
                            :padded="false"
                            @click="onClearWebhookUrl"
                        />
                    </template>
                </UInput>
                <UButton
                    icon="i-material-symbols:save-outline"
                    color="primary"
                    variant="solid"
                    :label="$t('Save')"
                    :disabled="!webhookUrlState"
                    :trailing="false"
                    :ui="{
                        rounded: 'rounded-lg',
                    }"
                    @click="onSubmit"
                />
            </div>
            <div class="grid grid-cols-4 gap-4 mt-5">
                <UButton
                    icon="i-material-symbols-translate"
                    color="green"
                    variant="soft"
                    :disabled="!canTriggerWebhook"
                    :label="$t('TTS_TEXT_SUCCESS')"
                    :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_TEXT_SUCCESS')"
                />
                <UButton
                    icon="i-mdi-file-document-outline"
                    color="green"
                    variant="soft"
                    :disabled="!canTriggerWebhook"
                    :label="$t('TTS_DOCUMENT_SUCCESS')"
                    :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_DOCUMENT_SUCCESS')"
                />
                <!-- <UButton icon="i-fluent:people-chat-24-regular" color="green" variant="soft"
                    :label="$t('TTS_STORY_SUCCESS')" :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_STORY_SUCCESS')" />
                <UButton icon="i-mingcute-voice-line" color="green" variant="soft"
                    :label="$t('TTS_VOICE_TRAINING_SUCCESS')" :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_VOICE_TRAINING_SUCCESS')" /> -->
                <UButton
                    icon="i-material-symbols-translate"
                    color="red"
                    variant="soft"
                    :disabled="!canTriggerWebhook"
                    :label="$t('TTS_TEXT_FAILED')"
                    :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_TEXT_FAILED')"
                />
                <UButton
                    icon="i-mdi-file-document-outline"
                    color="red"
                    variant="soft"
                    :disabled="!canTriggerWebhook"
                    :label="$t('TTS_DOCUMENT_FAILED')"
                    :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_DOCUMENT_FAILED')"
                />
                <!-- <UButton icon="i-fluent:people-chat-24-regular" color="red" variant="soft"
                    :label="$t('TTS_STORY_FAILED')" :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_STORY_FAILED')" />
                <UButton icon="i-mingcute-voice-line" color="red" variant="soft"
                    :label="$t('TTS_VOICE_TRAINING_FAILED')" :ui="{ rounded: 'rounded-lg' }"
                    @click="onTriggerWebhook('TTS_VOICE_TRAINING_FAILED')" /> -->
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
const { t } = useI18n()

const integrationStore = useIntegrationStore()
const { webhook, webhookUrl } = storeToRefs(integrationStore)

const webhookUrlState = ref(webhookUrl.value)

const onSubmit = async () => {
    if (!webhookUrlState.value) {
        return
    }
    webhookUrl.value = webhookUrlState.value
    const result = await integrationStore.saveWebhook(webhookUrlState.value)
    const toast = useToast()
    if (result) {
        toast.add({
            title: t('Webhook'),
            description: t('Webhook saved successfully.'),
            color: 'green',
        })
    } else {
        toast.add({
            title: t('Webhook'),
            description: t('Webhook saved failed'),
            color: 'red',
        })
        webhookUrl.value = ''
    }
}

const onClearWebhookUrl = () => {
    webhookUrlState.value = ''
}

const canTriggerWebhook = computed(() => {
    return !!integrationStore.webhookUrl
})

onMounted(async () => {
    await integrationStore.getWebhook()
    webhookUrlState.value = webhookUrl.value
})

const onTriggerWebhook = async (event_name: string) => {
    if (!webhookUrlState.value) {
        return
    }
    const result = await integrationStore.triggerWebhook(event_name)
    const toast = useToast()
    if (result) {
        toast.add({
            title: t('Triggered Webhook'),
            description: t('Triggered webhook event {event_name} successfully', { event_name: event_name }),
            color: 'green',
        })
    } else {
        toast.add({
            title: t('Triggered Webhook'),
            description: t('Triggered webhook event {event_name} failed', { event_name: event_name }),
            color: 'red',
        })
    }
}

const testButtons = computed(() => {
    return [
        {
            label: t("TTS_TEXT_SUCCESS"),
            icon: 'i-material-symbols-translate',
        },
    ]
})
</script>

<script lang="ts" setup>
import { useWebhookHistoryStore, type WebhookHistory } from '~/stores/webhook-history'
import { storeToRefs } from 'pinia'

const webhookHistoryStore = useWebhookHistoryStore()
const { getWebhookHistories, total } = storeToRefs(webhookHistoryStore)
const { t } = useI18n()

// Columns configuration
const columns = computed(() => {
    return [
        {
            key: 'event_name',
            label: t('Webhook Event'),
            sortable: true,
            class: 'w-44',
        },
        {
            key: 'created_at',
            label: t('Webhook Time'),
            sortable: true,
            class: 'grow',
        },
        {
            key: 'http_response',
            label: t('Status'),
            sortable: true,
            class: 'w-32',
        },
        {
            key: 'actions',
            label: t('Actions'),
            sortable: false,
            class: 'w-24',
        },
    ]
})

const selectedColumns = ref(columns)
const columnsTable = computed(() => columns.value.filter((column) => selectedColumns.value.includes(column)))

// Selected Rows State
const selectedRows = ref([])

const isAllSelected = computed(() => {
    return getWebhookHistories.value.length > 0 && selectedRows.value.length === getWebhookHistories.value.length
})

const isIndeterminate = computed(() => {
    return selectedRows.value.length > 0 && selectedRows.value.length < getWebhookHistories.value.length
})

// Data Fetching
const pending = ref(false)

const selectedRow = ref({})
const selectedJsonData = ref({})

const fetchData = async () => {
    pending.value = true
    try {
        await webhookHistoryStore.fetchWebhookHistories({
            page: page.value,
            items_per_page: pageCount.value,
        })
        // Reset selection when data changes
        selectedRows.value = []
    } finally {
        pending.value = false
    }
}

// Pagination States
const sort = ref({ column: 'id', direction: 'asc' as const })
const page = ref(1)
const pageCount = ref(10)
const pageTotal = computed(() => total.value)
const pageFrom = computed(() => (page.value - 1) * pageCount.value + 1)
const pageTo = computed(() => Math.min(page.value * pageCount.value, pageTotal.value))

// Watchers for pagination and filters
watch([page, pageCount, sort], () => {
    fetchData()
})

// watch pageCount, reset page to 1
watch(pageCount, () => {
    page.value = 1
})

const open = ref(false)
const loading = ref(false)

const onShowDetailModal = (json: WebhookHistory, row: any) => {
    webhookHistoryStore.setIsShowDetailModal(true)
    selectedJsonData.value = json
    selectedRow.value = row
}

//
const onRetry = async () => {
    try {
        loading.value = true
        const { uuid } = selectedRow.value as WebhookHistory
        const result = await webhookHistoryStore.retryWebhookHistories([uuid])
        const toast = useToast()

        if (result) {
            toast.add({
                title: t('Success'),
                description: t('Retry success'),
                color: 'green',
                icon: 'i-heroicons-check-circle-20-solid',
                timeout: 2000,
            })
            fetchData()
        } else {
            toast.add({
                title: t('Error'),
                description: t('Retry failed'),
                color: 'red',
                icon: 'i-heroicons-x-circle-20-solid',
                timeout: 2000,
            })
        }
    } catch (error) {
        console.error(error)
    } finally {
        loading.value = false
        open.value = false
    }
}

function isJSON(str: string) {
    try {
        var obj = JSON.parse(str)
        if (obj && typeof obj === 'object' && obj !== null) {
            return true
        }
    } catch (err) {}
    return false
}

// Initial data fetch
onMounted(async () => {
    await fetchData()
})
</script>

<template>
    <UCard
        class="w-full border border-gray-200 dark:border-gray-700"
        :ui="{
            base: '',
            ring: 'ring-0',
            shadow: 'shadow-none',
            divide: 'divide-y divide-gray-200 dark:divide-gray-700',
            header: { padding: 'px-4 py-5' },
            body: {
                padding: '!px-0 !pt-2',
                base: 'divide-y divide-gray-200 dark:divide-gray-700',
            },
            td: { base: 'ps-4' },
            footer: { padding: 'p-4' },
        }"
    >
        <!-- Header and Action buttons -->
        <div class="flex justify-between items-center w-full pr-3 py-3">
            <div class="flex items-center gap-1.5 ps-3">
                <span class="text-sm leading-5">{{ t('Rows per page') }}:</span>
                <USelect v-model="pageCount" :options="[3, 5, 10, 20, 30, 40]" class="me-2 w-20" size="xs" />
            </div>
            <!-- button reload -->
            <UButton icon="i-ci-arrow-reload-02" color="gray" size="xs" @click="fetchData" :loading="pending">
                {{ t('Reload') }}
            </UButton>
        </div>

        <!-- Table -->
        <UTable
            v-model:sort="sort"
            :rows="getWebhookHistories"
            :columns="columnsTable"
            :loading="pending"
            sort-asc-icon="i-heroicons-arrow-up"
            sort-desc-icon="i-heroicons-arrow-down"
            sort-mode="manual"
            class="w-full"
            :ui="{ td: { base: 'truncate' } }"
        >
            <template #created_at-data="{ row }">
                <div>{{ formatDatetime(row.created_at) }}</div>
            </template>

            <template #event_name-data="{ row }">
                <div
                    class="cursor-pointer group-name hover:text-primary-500"
                    @click="
                        onShowDetailModal(isJSON(row.event_data) ? JSON.parse(row.event_data) : row.event_data, row)
                    "
                >
                    <div class="w-full font-bold">{{ t(row.event_name) }}</div>
                    <div class="text-xs text-slate-500">{{ row.uuid }}</div>
                </div>
            </template>

            <template #http_response-data="{ row }">
                <div class="flex flex-row gap-1 items-center">
                  <UBadge
                        v-if="row.http_response"
                        size="xs"
                        :label="row.http_response"
                        :color="row.http_response === '200' ? 'emerald' : 'red'"
                        variant="subtle"
                    />
                    <UPopover
                    class="h-full"
                    mode="hover"
                    :popper="{ placement: 'top' }"
                    :ui="{ wrapper: 'items-center justify-center flex' }"
                >
                    <UIcon name="i-ri-question-fill" class="me-1 text-xs" />

                    <template #panel>
                        <div class="p-2 text-xs">
                            {{
                                $t(
                                    'This is the status code we get returned from your webhook.'
                                )
                            }}
                        </div>
                    </template>
                </UPopover>
                </div>
            </template>

            <template #completed-data="{ row }">
                <UBadge
                    size="xs"
                    :label="row.completed ? '200' : 'Internal error'"
                    :color="row.completed ? 'emerald' : 'red'"
                    variant="subtle"
                />
            </template>

            <template #actions-data="{ row }">
                <div class="flex items-center gap-1 justify-center">
                    <UButton
                        class="mx-2"
                        @click="
                            open = true;
                            selectedRow = row
                        "
                        icon="i-mdi-send-outline"
                        color="gray"
                        size="xs"
                    >
                        {{ t('Resend') }}
                    </UButton>
                </div>
            </template>
        </UTable>

        <!-- Pagination -->
        <template #footer>
            <div class="flex flex-wrap justify-between items-center">
                <div>
                    <span class="text-sm leading-5">
                        {{ t('Showing') }}
                        <span class="font-medium">{{ pageFrom }}</span>
                        {{ t('to') }}
                        <span class="font-medium">{{ pageTo }}</span>
                        {{ t('of') }}
                        <span class="font-medium">{{ pageTotal }}</span>
                        {{ t('results') }}
                    </span>
                </div>

                <UPagination
                    v-model="page"
                    :page-count="pageCount"
                    :total="pageTotal"
                    :ui="{
                        wrapper: 'flex items-center gap-1',
                        rounded: '!rounded-full min-w-[32px] justify-center',
                        default: {
                            activeButton: {
                                variant: 'outline',
                            },
                        },
                    }"
                />
            </div>
        </template>
    </UCard>
    <DetailModal :json-data="selectedJsonData" :item="selectedRow" />
    <UDashboardModal
        v-model="open"
        :title="t('Resend Webhook')"
        :description="t('Are you sure you want to resend this webhook?')"
        icon="i-heroicons-exclamation-circle"
        :ui="{
            icon: { base: 'text-primary-500 dark:text-primary-400' },
            footer: { base: 'ml-16' },
        }"
    >
        <template #footer>
            <UButton color="primary" :label="t('Resend')" :loading="loading" @click="onRetry" />
            <UButton color="white" :label="t('Cancel')" @click="open = false" />
        </template>
    </UDashboardModal>
</template>

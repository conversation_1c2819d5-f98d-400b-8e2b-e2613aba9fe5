<template>
  <div class="text-sm">
    <div class="flex flex-col gap-2">
      <p>
        {{
          $t(
            "Your secret API keys are listed below. Please note that we do not display your secret API keys again after you generate them."
          )
        }}
      </p>
      <p>
        {{
          $t(
            "Do not share your API key with others, or expose it in the browser or other client-side code. In order to protect the security of your account, TTSOpenAI may also automatically disable any API key that we've found has leaked publicly."
          )
        }}
      </p>
      <UTable
        :columns="columns"
        :rows="apiKeys"
        :emptyState="{
          label: $t('No API keys found.'),
        }"
        :ui="{
          th: {
            base: 'uppercase',
          },
        }"
      >
        <template #actions-data="{ row }">
          <div class="flex flex-row gap-1 items-center">
            <UTooltip :text="$t('Revoke key')" :popper="{ placement: 'top' }">
              <UButton
                :padded="false"
                icon="i-gg:trash"
                size="sm"
                color="gray"
                square
                variant="link"
                @click="onDeleteApiKey(row.id)"
              />
            </UTooltip>
          </div>
        </template>
        <template #created_at-data="{ row }">
          <div class="flex flex-row gap-1 items-center">
            {{ formatDatetime(row.created_at) }}
          </div>
        </template>
        <template #updated_at-data="{ row }">
          <div class="flex flex-row gap-1 items-center">
            {{ formatDatetime(row.updated_at) }}
          </div>
        </template>
      </UTable>
      <div>
        <UButton
          @click="onCreateNewKey"
          icon="i-mdi:plus"
          variant="solid"
          :label="$t('Create new secret key')"
          :trailing="false"
          color="white"
          :ui="{
            rounded: 'rounded-lg',
            base: 'dark:hover:bg-gray-700 dark:focus:ring-gray-700',
          }"
        />
      </div>
    </div>

    <IntegrationAPIKeyForm
      :is-open="isOpenForm"
      @close="onCloseForm"
      @submit="onSubmit"
    />
    <IntegrationAPIKeySave
      :is-open="isOpenKeySave"
      :secretKey="secretKey"
      @close="isOpenKeySave = false"
    />

    <UDashboardModal
      v-model="open"
      :title="t('Delete API Key')"
      :description="t('Are you sure you want to delete this API key?')"
      icon="i-heroicons-exclamation-circle"
      :ui="{
        icon: { base: 'text-primary-500 dark:text-primary-400' },
        footer: { base: 'ml-16' },
      }"
    >
      <template #footer>
        <UButton color="primary" :label="t('Delete')" @click="onDelete" />
        <UButton color="white" :label="t('Cancel')" @click="open = false" />
      </template>
    </UDashboardModal>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";

const { t } = useI18n();
const columns = computed(() => {
  return [
    {
      key: "name",
      label: t("NAME"),
    },
    {
      key: "api_key",
      label: t("SECRET KEY"),
    },
    {
      key: "created_at",
      label: t("CREATED"),
    },
    {
      key: "updated_at",
      label: t("UPDATED"),
    },
    {
      key: "actions",
    },
  ];
});

const open = ref(false);
const integrationStore = useIntegrationStore();
const { apiKeys } = storeToRefs(integrationStore);
const apiKeyTobeDeleted = ref();

const onDeleteApiKey = (id: number) => {
  apiKeyTobeDeleted.value = id;
  //TODO: Can't show confirm popup
  //open()
  open.value = true;
};

const isOpenForm = ref(false);
const isOpenKeySave = ref(false);
const onCreateNewKey = () => {
  isOpenForm.value = true;
};

const onCloseForm = () => {
  isOpenForm.value = false;
};

const secretKey = ref("");
const onSubmit = async () => {
  const sk = await integrationStore.createAPIKey(integrationStore.apiKeyName);
  if (sk) {
    secretKey.value = sk;
    isOpenForm.value = false;
    isOpenKeySave.value = true;
    await integrationStore.getAPIKeys();
  }
};

const onDelete = async () => {
  var result = await integrationStore.deleteAPIKey(apiKeyTobeDeleted.value);

  const toast = useToast();
  if (result) {
    toast.add({
      title: t("Success"),
      description: t("Delete key successfully."),
      color: "green",
    });
    await integrationStore.getAPIKeys();
  } else {
    toast.add({
      title: t("Webhook"),
      description: t("Revoke key failed"),
      color: "red",
    });
  }

  open.value = false;
};

onMounted(() => {
  integrationStore.getAPIKeys();
});
</script>

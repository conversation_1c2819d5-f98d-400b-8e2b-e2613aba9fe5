<template>
    <div class="text-sm">
        <div class="flex flex-col gap-2">
            <p>
                {{
                    $t(
                        `We'll send a POST request to the URL you provide below every time a vocalization is completed. This allows you to keep your system in sync with our vocalization service.`
                    )
                }}
            </p>
            <div class="flex flex-row gap-4">
                <UInput
                    class="flex-1"
                    placeholder="URL"
                    icon="i-mdi:webhook"
                    autocomplete="off"
                    size="lg"
                    v-model="webhookUrlState"
                    :ui="{ icon: { trailing: { pointer: '' } }, rounded: 'rounded-lg' }"
                >
                    <template #trailing>
                        <UButton
                            color="gray"
                            variant="link"
                            icon="i-heroicons-x-mark-20-solid"
                            :padded="false"
                            @click="onClearWebhookUrl"
                        />
                    </template>
                </UInput>
                <UButton
                    icon="i-material-symbols:save-outline"
                    color="primary"
                    variant="solid"
                    :label="$t('Save')"
                    :disabled="!webhookUrlState"
                    :trailing="false"
                    :ui="{
                        rounded: 'rounded-lg',
                    }"
                    @click="onSubmit"
                />
            </div>
            <UDivider type="dashed" class="my-3" />

            <div>
                <div class="mb-3">
                    {{$t("Try to trigger webhook with the following events:")}}
                </div>
                <div class="flex flex-row gap-6">
                    <div>
                        <div class="font-semibold mb-2">{{ $t("Events:") }}</div>
                        <UAsideLinks :links="testButtons" />
                    </div>
                    <div class="dashed flex-1 rounded-md p-4 bg-gray-50 dark:bg-gray-950">
                        <div v-if="loading.triggerWebhook" class="flex justify-center items-center h-full">
                            <UIcon name="line-md:loading-twotone-loop" class="text-3xl text-gray-500" />
                        </div>
                        <div
                            v-else-if="!triggerWebhookResponse"
                            class="flex flex-col gap-2 h-full items-center justify-center p-6 text-gray-500"
                        >
                            <UIcon name="ri:thunderstorms-fill" class="text-4xl text-gray-300" />
                            <div>
                                {{$t("Select an event on the left to trigger the webhook.")}}
                            </div>
                        </div>
                        <div v-else>
                            <div class="font-semibold mb-2">
                                {{ $t("Response:") }}
                            </div>
                            <pre class="text-xs text-gray-500">{{ triggerWebhookResponse }}</pre>
                            <div class="my-2 text-xs">
                                {{ $t(statusCodeDes[triggerWebhookResponse?.status_code] || "") }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
const { t } = useI18n()

const integrationStore = useIntegrationStore()
const { loading, webhookUrl, triggerWebhookResponse } = storeToRefs(integrationStore)

const webhookUrlState = ref(webhookUrl.value)

const onSubmit = async () => {
    if (!webhookUrlState.value) {
        return
    }
    webhookUrl.value = webhookUrlState.value
    const result = await integrationStore.saveWebhook(webhookUrlState.value)
    const toast = useToast()
    if (result) {
        toast.add({
            title: t('Webhook'),
            description: t('Webhook saved successfully.'),
            color: 'green',
        })
    } else {
        toast.add({
            title: t('Webhook'),
            description: t('Webhook saved failed'),
            color: 'red',
        })
        webhookUrl.value = ''
    }
}

const onClearWebhookUrl = () => {
    webhookUrlState.value = ''
}

const canTriggerWebhook = computed(() => {
    return !!integrationStore.webhookUrl && !loading.value['triggerWebhook']
})

onMounted(async () => {
    await integrationStore.getWebhook()
    webhookUrlState.value = webhookUrl.value
})

const onTriggerWebhook = async (event_name: string) => {
    activeTestButton.value = event_name
    if (!webhookUrlState.value) {
        return
    }
    const result = await integrationStore.triggerWebhook(event_name)
    const toast = useToast()
    if (result) {
        toast.add({
            title: t('Triggered Webhook'),
            description: t('Triggered webhook event {event_name} successfully', { event_name: event_name }),
            color: 'green',
        })
    } else {
        toast.add({
            title: t('Triggered Webhook'),
            description: t('Triggered webhook event {event_name} failed', { event_name: event_name }),
            color: 'red',
        })
    }
}

const activeTestButton = ref<string | null>(null)
const testButtons = computed(() => {
    return [
        {
            label: t('TTS_TEXT_SUCCESS'),
            icon: 'i-material-symbols-translate',
            click: () => onTriggerWebhook('TTS_TEXT_SUCCESS'),
            disabled: !canTriggerWebhook.value,
            active: activeTestButton.value === 'TTS_TEXT_SUCCESS',
        },
        {
            label: t('TTS_TEXT_FAILED'),
            icon: 'i-material-symbols-translate',
            click: () => onTriggerWebhook('TTS_TEXT_FAILED'),
            disabled: !canTriggerWebhook.value,
            color: 'red',
            active: activeTestButton.value === 'TTS_TEXT_FAILED',
        },
        {
            label: t('TTS_DOCUMENT_SUCCESS'),
            icon: 'i-mdi-file-document-outline',
            click: () => onTriggerWebhook('TTS_DOCUMENT_SUCCESS'),
            disabled: !canTriggerWebhook.value,
            active: activeTestButton.value === 'TTS_DOCUMENT_SUCCESS',
        },
        {
            label: t('TTS_DOCUMENT_FAILED'),
            icon: 'i-mdi-file-document-outline',
            click: () => onTriggerWebhook('TTS_DOCUMENT_FAILED'),
            disabled: !canTriggerWebhook.value,
            color: 'red',
            active: activeTestButton.value === 'TTS_DOCUMENT_FAILED',
        },
        {
            label: t('TTS_STORY_SUCCESS'),
            icon: 'i-fluent:people-chat-24-regular',
            click: () => onTriggerWebhook('TTS_STORY_SUCCESS'),
            disabled: !canTriggerWebhook.value,
            active: activeTestButton.value === 'TTS_STORY_SUCCESS',
        },
        {
            label: t('TTS_STORY_FAILED'),
            icon: 'i-fluent:people-chat-24-regular',
            click: () => onTriggerWebhook('TTS_STORY_FAILED'),
            disabled: !canTriggerWebhook.value,
            color: 'red',
            active: activeTestButton.value === 'TTS_STORY_FAILED',
        },
    ]
})

const statusCodeDes = {
    404: '404 means our system is unable to reach the webhook URL. Please check the URL and make sure it is accessible from the internet.',
    200: '200 means the webhook URL is reachable and the request is successful.',
}
</script>

<template>
  <div v-if="user" class="space-y-3 p-0 md:p-4">
    <div class="p-6">
      <div class="">
        <h4 class="font-semibold text-lg">
          {{ $t("Redeem a gift card") }}
        </h4>
        <hr class="h-px mt-2 mb-5 bg-gray-200 border-0 dark:bg-gray-700" />
        <UForm
          :key="locale"
          ref="formRef"
          :schema="schema"
          :state="state"
          class="space-y-4"
          @submit="onSubmit"
        >
          <UFormGroup
            class="w-1/2"
            :label="$t('Enter claim code')"
            name="claimCode"
            size="xl"
          >
            <UInput
              :placeholder="'tts-openai-XXXXXXXXXX'"
              icon="i-fluent-gift-card-add-20-regular"
              v-model="state.claimCode"
              v-mask="'AAA-AAAAAA-XXXXXXXXXX'"
              class="uppercase"
              :ui="{
                base: 'uppercase',
                icon: { trailing: { pointer: '' } },
                rounded: 'rounded-lg',
              }"
            >
              <template #trailing>
                <UButton color="gray" variant="solid" @click="onPaste" size="xs">
                  {{ $t("Paste") }}
                </UButton>
              </template>
            </UInput>
          </UFormGroup>
          <vue-turnstile
            v-if="isOpenProcess"
            @error="onTurnstileError"
            @unsupported="onUnsupportedTurnstile"
            appearance="always"
            ref="turnstileRef"
            :site-key="config.public.NUXT_TURNSTILE_SITE_KEY"
            v-model="turnstileToken"
            :language="locale"
            :theme="colorMode.value === 'dark' ? 'dark' : 'light'"
            :key="colorMode.value + locale"
          />
          <UButton
            type="submit"
            :disabled="!isValid"
            :loading="isOpenProcess || loading['redeemGiftCard']"
          >
            {{ $t("Redeem it here") }}
          </UButton>
        </UForm>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import VueTurnstile from "vue-turnstile";
const authStore = useAuthStore();
const userStore = useUserStore();
const { user } = storeToRefs(authStore);
const config = useRuntimeConfig();
import { object, string, type InferType } from 'yup'
import type { FormSubmitEvent } from '#ui/types'
const paymentsStore = usePaymentsStore()
const colorMode = useColorMode();
const { loading } = storeToRefs(paymentsStore);
const turnstileToken = ref("");
const turnstileRef = ref(null);
const { t, locale } = useI18n()
// check claim code with format: tts-openai-XXXXXXXXXX
const schema = computed(() => {
  return object({
  claimCode: string().length(21, t('Invalid claim code')).required('Required'),
})
})

type Schema = InferType<typeof schema.value>

const state = reactive({
    claimCode: '',
})

async function onSubmit (event: FormSubmitEvent<Schema>) {
  // Do something with event.data
  console.log(event.data)
  isOpenProcess.value = true
}

function onPaste () {
  navigator.clipboard.readText().then((text: string) => {
    state.claimCode = text
  })
}

const isValid = computed(() => {
  return schema.value.isValidSync(state)
})

const formRef = ref(null)
watch(() => locale.value, () => {
  formRef.value?.validate()
})

const onUnsupportedTurnstile = () => {
  alert("Please enable cookies to use this feature");
};

const onTurnstileError = (error: string) => {
  console.log("turnslite error:", error);
};
const toast = useToast()
const isOpenProcess = ref(false)
const hasValidToken = ref(false);
watch(
  () => isOpenProcess.value,
  (val) => {
    if (!val) {
      hasValidToken.value = false;
      turnstileToken.value = "";
    }
  }
);

watch(
  () => turnstileToken.value,
  async (val) => {
    console.log("🚀 ~ val:", val)
    if (val && isOpenProcess.value) {
      hasValidToken.value = true;
      const result = await paymentsStore.redeemGiftCard(state.claimCode, val);
      isOpenProcess.value = false;

      if(result){
        authStore.syncUserTokenInfo();

toast.add({
  title: t("Gift card redeemed"),
  description: t("Your gift card has been redeemed successfully"),
  color: 'green',
  timeout: 5000,
});
      } else {
        toast.add({
          title: t("Gift card error"),
          description: t("There was an error redeeming your gift card"),
          color: 'red',
          timeout: 5000,
        });
      }

    }
  }
);
</script>

<template>
  <div>
    <NuxtLayout name="non-translate">
      <ULandingSection
        v-if="isBeta"
        id="plan"
        :title="$t('Pricing Plans')"
        :description="
          $t(
            'Free 1,000,000 credits for everyone with beta version, you can create speech up to 400 pages document ~ about 10 hours of speech.'
          )
        "
        class="scroll-mt-[var(--header-height)]"
      >
        <div class="flex justify-center w-full">
          <div class="lg:grid lg:grid-cols-1 lg:space-y-0 lg:space-x-4 w-full max-w-3xl">
            <!-- Pricing Card -->
            <div
              v-if="loading['getAllProductsAndPlans']"
              v-for="plan in 1"
              class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
            >
              <h3 class="mb-0 text-2xl font-semibold">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                </div>
              </h3>
              <div class="flex justify-center items-baseline mt-8">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                </div>
              </div>
              <div class="flex justify-center items-baseline mb-8 mt-2">
                <span class="mr-1 text-xl font-extrabold text-primary-500">
                  <div role="status" class="max-w-sm animate-pulse">
                    <div
                      class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                    ></div>
                  </div>
                </span>
              </div>
              <!-- List -->
              <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                <div role="status" class="max-w-sm animate-pulse">
                  <div
                    class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                  ></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px] mb-2.5"
                  ></div>
                  <div class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 mb-2.5"></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[330px] mb-2.5"
                  ></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[300px] mb-2.5"
                  ></div>
                  <div
                    class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px]"
                  ></div>
                </div>
              </ul>
            </div>
            <div
              v-else
              v-for="plan in plans"
              class="relative flex flex-col text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
              :class="{
                '!border-primary-600': user?.current_plan === plan.id,
              }"
              @mouseover="onMouseOverPlan(plan)"
              @mouseleave="onMouseLeavePlan(plan)"
            >
              <div v-if="plan.bonus" class="ribbon ribbon-top-left">
                <span v-if="plan.isHover">+{{ formatUserTokens(plan.tokenBonus) }}</span>
                <span v-else>{{ $t("Bonus top up", { bonus: plan.bonus }) }}</span>
              </div>
              <h3 class="mb-0 text-2xl font-semibold">{{ plan.title }}</h3>
              <div
                class="flex justify-center items-baseline mt-8"
                :class="{ 'scale-125 transition duration-500 ease-in-out': plan.isHover }"
              >
                <span class="mr-2 text-4xl font-extrabold">${{ plan.price }}</span>
                <span class="text-gray-500 dark:text-gray-400">/{{ $t("month") }}</span>
              </div>
              <div class="flex justify-center items-baseline mb-8 mt-2">
                <span class="mr-1 text-xl font-extrabold text-primary-500">
                  <number
                    :from="+plan.base_credit"
                    :to="+plan.tokensAfterBonus || plan.base_credit"
                    :format="formatUserTokens"
                    :duration="0.5"
                    easing="Power1.easeOut"
                  />
                </span>
                <span class="text-gray-500 dark:text-gray-400">{{ $t("credits") }}</span>
              </div>
              <!-- List -->
              <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                <li v-for="feature in plan.features" class="flex items-center space-x-2">
                  <!-- Icon -->
                  <svg
                    class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <span>{{ feature }}</span>
                </li>
              </ul>
              <a
                v-if="!isLoggedIn && plan.price > 0"
                @click="changePlan(plan)"
                class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
              >
                {{ $t("Get started") }}
              </a>
              <a
                v-else-if="isUnverified && plan.price > 0"
                class="w-full cursor-pointer text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800"
              >
                {{ $t("Account unverified") }}
              </a>
              <a
                v-else-if="plan.price > 0 && currentSubscriptionPlan !== plan.id"
                @click="changePlan(plan)"
                class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                :class="{
                  '!bg-yellow-700 hover:!bg-yellow-800 dark:!bg-yellow-600 dark:hover:!bg-yellow-700':
                    user?.current_plan === plan.id,
                }"
              >
                {{
                  user?.current_plan === plan.id
                    ? $t("Extend current plan")
                    : $t("Get started")
                }}
              </a>
              <a
                v-else-if="currentSubscriptionPlan === plan.id"
                class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                :class="{
                  '!bg-gray-700 hover:!bg-gray-800 dark:!bg-gray-600 dark:hover:!bg-gray-700':
                    user?.current_plan === plan.id,
                }"
              >
                {{ $t("Current plan") }}
              </a>
            </div>
          </div>
        </div>
      </ULandingSection>
      <ULandingSection
        v-else
        id="plan"
        :title="$t('Pricing Plans')"
        :description="
          $t(
            'Our plans are designed to meet the requirements of both beginners and players. Get the right plan that suits you.'
          )
        "
        class="scroll-mt-[var(--header-height)]"
      >
        <div>
          <div class="flex justify-center w-full">
            <div
              class="lg:grid lg:grid-cols-2 lg:space-y-0 lg:space-x-4 w-full max-w-3xl"
            >
              <!-- Pricing Card -->
              <div
                v-if="loading['getAllProductsAndPlans']"
                v-for="plan in 2"
                class="relative flex flex-col p-0 text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
              >
                <h3 class="mb-0 text-2xl font-semibold">
                  <div role="status" class="max-w-sm animate-pulse">
                    <div
                      class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                    ></div>
                  </div>
                </h3>
                <div class="flex justify-center items-baseline mt-8">
                  <div role="status" class="max-w-sm animate-pulse">
                    <div
                      class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                    ></div>
                  </div>
                </div>
                <div class="flex justify-center items-baseline mb-8 mt-2">
                  <span class="mr-1 text-xl font-extrabold text-primary-500">
                    <div role="status" class="max-w-sm animate-pulse">
                      <div
                        class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                      ></div>
                    </div>
                  </span>
                </div>
                <!-- List -->
                <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                  <div role="status" class="max-w-sm animate-pulse">
                    <div
                      class="h-2.5 bg-gray-500 rounded-full dark:bg-gray-800 w-48 mb-4"
                    ></div>
                    <div
                      class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px] mb-2.5"
                    ></div>
                    <div
                      class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 mb-2.5"
                    ></div>
                    <div
                      class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[330px] mb-2.5"
                    ></div>
                    <div
                      class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[300px] mb-2.5"
                    ></div>
                    <div
                      class="h-2 bg-gray-500 rounded-full dark:bg-gray-800 max-w-[360px]"
                    ></div>
                  </div>
                </ul>
              </div>
              <div
                v-else
                v-for="plan in plans"
                class="relative flex flex-col text-center text-gray-900 border-2 border-white dark:border-gray-800 bg-gray-100 rounded-lg p-4 pt-6 dark:bg-gray-700 dark:text-white hover:shadow-lg !hover:border-primary-600 hover:cursor-pointer hover:scale-105 transition duration-300 ease-in-out"
                :class="{
                  '!border-primary-600': user?.current_plan === plan.id,
                }"
                @mouseover="onMouseOverPlan(plan)"
                @mouseleave="onMouseLeavePlan(plan)"
              >
                <div v-if="plan.bonus" class="ribbon ribbon-top-left">
                  <span v-if="plan.isHover"
                    >+{{ formatUserTokens(plan.tokenBonus) }}</span
                  >
                  <span v-else>{{ $t("Bonus top up", { bonus: plan.bonus }) }}</span>
                </div>
                <h3 class="mb-0 text-2xl font-semibold">{{ plan.title }}</h3>
                <div
                  class="flex justify-center items-baseline mt-8"
                  :class="{
                    'scale-125 transition duration-500 ease-in-out': plan.isHover,
                  }"
                >
                  <span class="mr-2 text-4xl font-extrabold">${{ plan.price }}</span>
                </div>
                <div
                  v-if="plan.price > 0"
                  class="flex justify-center items-baseline mb-8 mt-2"
                >
                  <span class="mr-1 text-xl font-extrabold text-primary-500">
                    <number
                      :from="+plan.base_credit"
                      :to="+plan.tokensAfterBonus || plan.base_credit"
                      :format="formatUserTokens"
                      :duration="0.5"
                      easing="Power1.easeOut"
                    />
                  </span>

                  <span class="text-gray-500 dark:text-gray-400">{{
                    $t("non-expiring credits")
                  }}</span>
                </div>
                <div v-else class="mb-8 mt-2" />
                <!-- List -->
                <ul role="list" class="mb-8 space-y-3 text-left text-sm min-h-[250px]">
                  <li
                    v-for="feature in plan.features"
                    class="flex items-center space-x-2"
                  >
                    <!-- Icon -->
                    <svg
                      class="flex-shrink-0 w-5 h-5 text-green-500 dark:text-green-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>{{ feature }}</span>
                  </li>
                </ul>
                <a
                  v-if="!isLoggedIn && plan.price > 0"
                  @click="changePlan(plan)"
                  class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                >
                  {{ $t("Get started") }}
                </a>
                <a
                  v-else-if="isUnverified && plan.price > 0"
                  class="w-full cursor-pointer text-white bg-gray-700 hover:bg-gray-800 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-600 dark:hover:bg-gray-700 focus:outline-none dark:focus:ring-gray-800"
                >
                  {{ $t("Account unverified") }}
                </a>
                <a
                  v-else-if="plan.price > 0 && currentSubscriptionPlan !== plan.id"
                  @click="changePlan(plan)"
                  class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                  :class="{
                    '!bg-yellow-700 hover:!bg-yellow-800 dark:!bg-yellow-600 dark:hover:!bg-yellow-700':
                      user?.current_plan === plan.id,
                  }"
                >
                  {{
                    user?.current_plan === plan.id
                      ? $t("Extend current plan")
                      : $t("Get started")
                  }}
                </a>
                <a
                  v-else-if="currentSubscriptionPlan === plan.id"
                  class="w-full cursor-pointer text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800"
                  :class="{
                    '!bg-gray-700 hover:!bg-gray-800 dark:!bg-gray-600 dark:hover:!bg-gray-700':
                      user?.current_plan === plan.id,
                  }"
                >
                  {{ $t("Current plan") }}
                </a>
              </div>
            </div>
          </div>
          <div class="text-center mt-4 text-sm">
            ※
            {{
              $t(
                "1,000 credits = 1,000 characters with High Quality Voice (500 characters with HD Quality Voice)"
              )
            }}
          </div>
        </div>

        <!-- <PaymentModal v-bind="paymentInfo" /> -->
      </ULandingSection>
      <ULandingSection
        :title="page.features.title"
        :description="page.features.description"
        :headline="page.features.headline"
      >
        <UPageGrid
          id="features"
          class="scroll-mt-[calc(var(--header-height)+140px+128px+96px)]"
        >
          <ULandingCard
            v-for="(item, index) in page.features.items"
            :key="index"
            v-bind="item"
          />
        </UPageGrid>
      </ULandingSection>
      <ULandingSection
        id="faq"
        :title="$t('Frequently asked questions')"
        class="scroll-mt-[var(--header-height)]"
      >
        <template #description>
          <i18n-t
            keypath="If you can't find the answer you are looking for here, please contact us: {0}"
            tag="p"
          >
            <a
              class="font-semibold underline cursor-pointer"
              :href="'mailto:' + contactEmail"
              :class="'text-primary-500 dark:text-primary-500'"
              >{{ contactEmail }}</a
            >
          </i18n-t>
        </template>
        <ULandingFAQ
          multiple
          :items="faqs"
          :ui="{
            button: {
              label: 'font-semibold',
              trailingIcon: {
                base: 'w-6 h-6',
              },
            },
          }"
          class="max-w-4xl mx-auto"
        />
      </ULandingSection>

      <ULandingSection
        :headline="page.testimonials.headline"
        :title="page.testimonials.title"
        :description="page.testimonials.description"
      >
        <UPageColumns
          id="testimonials"
          class="xl:columns-4 scroll-mt-[calc(var(--header-height)+140px+128px+96px)]"
        >
          <div
            v-for="(testimonial, index) in page.testimonials.items"
            :key="index"
            class="break-inside-avoid"
          >
            <ULandingTestimonial v-bind="testimonial" />
          </div>
        </UPageColumns>
      </ULandingSection>

      <ULandingSection class="bg-primary-50 dark:bg-primary-400 dark:bg-opacity-10">
        <ULandingCTA v-bind="page.cta" :card="false" />
      </ULandingSection>
    </NuxtLayout>
  </div>
</template>

<script setup lang="ts">
import PaymentModal from "~/base-components/PaymentModal.vue";
import { useModal } from "vue-final-modal";
import type { PopoverOptions, PopoverInterface } from "flowbite";
import type { Product } from "~/stores/payments";
import { AccessEnum } from "~/types";
const appStore = useAppStore();
const popover = ref<PopoverInterface | null>(null);
const paymentStore = usePaymentsStore();
const {
  planList,
  loading,
  paymentInfo,
  isOpen,
  tokenUnit,
  specialBonusCredit,
  buyTokenPlan,
} = storeToRefs(paymentStore);
const { contactEmail } = storeToRefs(appStore);
const config = useRuntimeConfig();
const isBeta = config.public.isBeta;
onMounted(async () => {
  await paymentStore.getAllProductsAndPlans();
});

definePageMeta({
  layoutTransition: true,
  layout: false,
});
const authStore = useAuthStore();
const { isUnverified, user, isLoggedIn, currentSubscriptionPlan } = storeToRefs(
  authStore
);
const { t } = useI18n({ useScope: "global" });
const router = useRouter();
const { open, close, options } = useModal({
  component: PaymentModal,
  attrs: {
    onClose() {
      close();
    },
    onSubmit() {
      close();
    },
  },
});

const plans = computed(() => {
  return planList.value
    ?.map((plan: Product) => {
      const features = [];
      // if (plan.id !== 'FP0001') {
      //     features.push(t('GPT-4'))
      // }

      if (plan.id === "PP0001") {
        features.push(t("Create speech from text up to 10k characters"));
        features.push(t("Retry unlimited times with half price"));
      } else if (plan.id === "FP0001") {
        features.push(t("Create speech from text"));
      }
      plan?.usable_models?.forEach((model: string) => {
        if (model === "tts-1" || plan.price) {
          features.push(t("usable_models_" + model));
        }
      });
      for (const feature of plan.features) {
        switch (feature) {
          case AccessEnum.IS_CREATE_VOICE:
            features.push(t("Create my voice"));
            break;
          // case AccessEnum.IS_NO_ADVERTISEMENT:
          //   features.push(t("A few Ads"));
          //   break;
          // case AccessEnum.IS_A_FEW_ADVERTISEMENT:
          //   features.push(t("A few Ads"));
          //   break;
          case AccessEnum.IS_DOWNLOAD:
            features.push(t("Can download audio files"));
            break;
          case AccessEnum.IS_UP_FILE_TO_CONVERT:
            if (plan.max_monthly_total_file === 999999999999 && plan.price) {
              features.push(t("Document to speech"));
            } else {
              features.push(t("documents/month", { num: +plan.max_monthly_total_file }));
            }
            break;
          case AccessEnum.IS_ACCESS_VOICE_LIB:
            features.push(t("Access to voice library"));
            break;
          case AccessEnum.IS_CREATE_STORY:
            features.push(t("Create story from text"));
            break;
          case AccessEnum.IS_USE_HD:
            features.push(t("Use HD voice"));
            break;
        }
      }
      // if (plan.max_monthly_total_file === 999999999999 && plan.price) {
      //   features.push(t("Unlimited document to speech"));
      // } else {
      //   // features.push(t("documents/month", { num: +plan.max_monthly_total_file }));
      // }

      // convert bytes to mb
      const max_file_size = Math.round(+plan.max_file_size / 1024 / 1024);
      if (plan.price) {
        features.push(t("Up to size per document", { size: max_file_size + "MB" }));
      }

      if (plan.id === "PP0001") {
        features.push(t("Integrate services using our API"));
      }

      // plan?.usable_models?.forEach((model: string) => {
      //   if (model === "tts-1" || plan.price) {
      //     features.push(t("usable_models_" + model));
      //   }
      // });

      // if (plan?.features?.includes("is_download")) {
      //   features.push(t("Can download audio files"));
      // }

      // if (plan?.price) {
      //   features.push(t("No Ads"));
      // }
      return {
        ...plan,
        title: t(plan.id),
        features,
      };
    })
    .filter((obj) => (isBeta && obj.id === "FP0001") || !isBeta);
});
const changePlan = (plan: any) => {
  if (!isLoggedIn.value) {
    router.push("/signin");
    return;
  }
  paymentInfo.value = {
    type: "change-plan",
    plan: plan,
    quantity: plan?.base_credit / tokenUnit.value,
    id: "BC0001",
    bonus: plan.base_credit,
    specialBonusCredit: specialBonusCredit.value,
    // plan: buyTokenPlan.value,
  };
  isOpen.value = true;
  open();
};

const onMouseOverPlan = (plan) => {
  plan.isHover = true;
  plan.tokenBonus = Math.round((plan.token * plan.bonus) / 100);
  plan.tokensAfterBonus = +plan.token + Math.round((plan.token * plan.bonus) / 100);
};

const onMouseLeavePlan = (plan) => {
  plan.isHover = false;
  plan.tokensAfterBonus = +plan.token;
};

const formatUserTokens = (number) => {
  if (!number || number === "NaN") return 0;
  return new Intl.NumberFormat().format(number?.toFixed(0));
};
const faqs = [
  {
    label: t("Why choose ttsopenai.com over other TTS tools?"),
    content: t(
      "ttsopenai.com utilizes OpenAI's TTS API, providing natural-sounding and high-quality voices at a lower cost compared to many other tools on the market. Additionally, we support the conversion of various document formats, from plain text to PDFs, DOCX, and ebooks."
    ),
  },
  {
    label: t("How do I use the ttsopenai.com service?"),
    content: t(
      "Our service is designed to be user-friendly. Simply upload your document and select your desired voice; the system will automatically convert your document into speech or an audiobook."
    ),
  },
  {
    label: t("Do I need programming knowledge to use ttsopenai.com?"),
    content: t(
      "No, you do not need programming knowledge. We have integrated OpenAI's TTS API into the website, making the process of converting text to speech simple and convenient for everyone."
    ),
  },
  {
    label: t("What types of documents can be converted to speech on ttsopenai.com?"),
    content: t(
      "We support a variety of document formats including plain text (txt), PDFs, DOCX, and ebook file formats."
    ),
  },
  {
    label: t("Can I customize the voice?"),
    content: t(
      "Yes, we provide various voice options, allowing you to customize the voice according to your specific preferences."
    ),
  },
  {
    label: t("What is the cost of using ttsopenai.com?"),
    content: t(
      "We price based on OpenAI's pricing, ensuring costs are lower than many other text-to-speech conversion tools on the market."
    ),
  },
  {
    label: t("Is there a limit to the number of documents I can convert?"),
    content: t(
      "We offer different service packages to suit the needs of each user. Please refer directly to the website for detailed information."
    ),
  },
  {
    label: t("Can I use ttsopenai.com for commercial purposes?"),
    content: t(
      "Yes, our service supports both personal and commercial purposes. However, please ensure compliance with our terms of use."
    ),
  },
  {
    label: t("What is the quality of the voices on ttsopenai.com?"),
    content: t(
      "Our voice quality is very high, with natural and easy-to-listen-to voices, thanks to advanced TTS technology from OpenAI."
    ),
  },
  {
    label: t(
      "Where can I contact technical support if I encounter issues using the service?"
    ),
    content: t(
      "We provide support via email and live chat on the website. Our support team is always ready to answer any questions and assist you whenever needed."
    ),
  },
  {
    label: t("Can I convert large-sized documents?"),
    content: t(
      "Yes, we support conversion of large-sized documents. However, processing time may increase depending on the size of the document. To ensure the best experience, we recommend splitting large documents into smaller parts if possible."
    ),
  },
  {
    label: t("What is the output file format of the speech?"),
    content: t(
      "The primary output file format is MP3, which ensures compatibility with most devices and music playback software."
    ),
  },
  {
    label: t("How do I know which voice is suitable for my document?"),
    content: t(
      "We provide a diverse selection of voices, including both male and female voices with various tones and languages. You can listen to voice samples before deciding which voice best suits the content of your document."
    ),
  },
  {
    label: t("Does ttsopenai.com support languages other than English?"),
    content: t(
      "Yes, we support various languages through OpenAI's TTS technology, making it easy for you to convert documents into natural-sounding speech in many different languages."
    ),
  },
  {
    label: t("Can I edit or customize the output audio?"),
    content: t(
      "While we do not provide direct editing functions on the platform, you can customize some settings such as reading speed and tone before conversion. This allows you to have better control over the feel and final sound of the output file."
    ),
  },
  {
    label: t("How do I protect my privacy and data on ttsopenai.com?"),
    content: t(
      "User security and privacy are our top priorities. We employ advanced security measures to protect your data and do not share information with any third parties without your consent."
    ),
  },
  {
    label: t("Can I use ttsopenai.com to create content for my website or blog?"),
    content: t(
      "Yes, you can use our service to create audio content for your website, blog, or social media platforms, enriching the way you deliver information to your readers or customers."
    ),
  },
  {
    label: t("Do I need to create an account to use the service?"),
    content: t(
      "Yes, creating an account helps you manage converted documents easily and access advanced features and better customer support services."
    ),
  },
  {
    label: t("Can I request additional voices or new languages?"),
    content: t(
      "We always listen to user feedback and strive to expand our services. If you have specific requests for a particular voice or language, please feel free to submit them to our support system."
    ),
  },
  {
    label: t("Can I use the ttsopenai.com service on my mobile phone?"),
    content: t(
      "Yes, our website is designed to be compatible with both computers and mobile devices, allowing you to easily access and use our services anytime, anywhere."
    ),
  },
];

const page = {
  features: {
    title: t("Features"),
    description: t(
      "Our service is designed to provide a seamless and convenient experience for users, offering a variety of features to meet your needs."
    ),
    headline: t("Explore the features of ttsopenai.com"),
    items: [
      {
        title: t("High-quality voices"),
        description: t(
          "We provide a diverse selection of high-quality voices, including both genders and various tones."
        ),
        icon: "i-iconoir-voice-circle",
      },
      {
        title: t("Multiple languages"),
        description: t(
          "Our service supports multiple languages, allowing you to convert documents into speech in various languages."
        ),
        icon: "i-fluent:emoji-sparkle-16-regular",
      },
      {
        title: t("Customizable settings"),
        description: t(
          "You can customize various settings such as reading speed and tone before converting your document into speech."
        ),
        icon: "i-bx:customize",
      },
      {
        title: t("Support for various document formats"),
        description: t(
          "We support a variety of document formats, including plain text, PDFs, DOCX, and ebooks."
        ),
        icon: "i-et:documents",
      },
      {
        title: t("Commercial use"),
        description: t(
          "Our service supports both personal and commercial purposes, providing flexibility for users."
        ),
        icon: "i-mingcute:group-3-line",
      },
      {
        title: t("User-friendly interface"),
        description: t(
          "Our website is designed to be user-friendly, making the process of converting text to speech simple and convenient for everyone."
        ),
        icon: "i-fluent:card-ui-20-regular",
      },
      {
        title: t("Privacy and security"),
        description: t(
          "We employ advanced security measures to protect your data and do not share information with any third parties without your consent."
        ),
        icon: "i-material-symbols:privacy-tip-outline",
      },
      {
        title: t("Mobile compatibility"),
        description: t(
          "Our website is designed to be compatible with both computers and mobile devices, allowing you to easily access and use our services anytime, anywhere."
        ),
        icon: "i-et:mobile",
      },
      {
        title: t("Customer support"),
        description: t(
          "We provide support via email. Our support team is always ready to answer any questions and assist you whenever needed."
        ),
        icon: "i-mdi:headset",
      },
    ],
  },

  testimonials: {
    headline: t("What our users say"),
    title: t("Testimonials"),
    description: t(
      "Our service has received positive feedback from many users. Here are some of the testimonials we have received."
    ),
    items: [
      {
        quote: t(
          "I have been using ttsopenai.com for a while now and I am very satisfied with the quality of the voices and the ease of use. I highly recommend this service to anyone looking for a reliable text-to-speech tool."
        ),
        author: {
          name: "Trinh The Dinh",
          description: t("CTO of Pal Company"),
          avatar: {
            src:
              "https://scontent-nrt1-1.xx.fbcdn.net/v/t39.30808-6/278800328_3238505893047342_951133557254112757_n.jpg?_nc_cat=103&ccb=1-7&_nc_sid=5f2048&_nc_ohc=OrWJtvbXK1cAX_p5Kuh&_nc_ht=scontent-nrt1-1.xx&oh=00_AfAuAnbymVk5vmOk1LLuGeGUo3kS4jhM2lTGqawTo4KpEQ&oe=65F99EA6",
            loading: "lazy",
          },
        },
      },
      {
        quote: t(
          "As someone with a busy schedule, ttsopenai.com has been a game-changer for quickly generating voiceovers for my presentations. The voices sound incredibly natural, saving me valuable time and effort."
        ),
        author: {
          name: "Quinn Barnett",
          description: t("Student at University"),
          avatar: {
            src: "https://i.pravatar.cc/120?img=4",
            loading: "lazy",
          },
        },
      },
      {
        quote: t(
          "Being visually impaired, I rely on ttsopenai.com to access written content easily. The accuracy and clarity of the voices make it a reliable companion in my daily life."
        ),
        author: {
          name: "Megan Smith",
          description: t("Patient at A Hospital"),
          avatar: {
            alt: "Megan Smith",
            loading: "lazy",
          },
        },
      },
      {
        quote: t(
          "Thanks to ttsopenai.com, I can now offer audio versions of my blog posts, reaching a wider audience and enhancing user experience. It's simple, efficient, and the results speak for themselves."
        ),
        author: {
          name: "Michael Harris",
          description: t("Blogger at X Blog"),
          avatar: {
            src: "https://i.pravatar.cc/120?img=6",
            loading: "lazy",
          },
        },
      },
      {
        quote: t(
          "I needed a solution for creating engaging voiceovers for my videos, and ttsopenai.com delivered exactly what I needed. The variety of voices and languages available make it perfect for reaching diverse audiences."
        ),
        author: {
          name: "Philip Johnson",
          description: t("YouTuber at Y Channel"),
          avatar: {
            src: "https://i.pravatar.cc/120?img=7",
            loading: "lazy",
          },
        },
      },
      {
        quote: t(
          "I run a language learning platform, and ttsopenai.com has been instrumental in providing authentic pronunciation examples for our students. It's an invaluable resource for language educators."
        ),
        author: {
          name: "John Doe",
          description: t("Teacher at Z School"),
          avatar: {
            src: "https://i.pravatar.cc/120?img=8",
            loading: "lazy",
          },
        },
      },
    ],
  },

  cta: {
    title: t("Ready to get started?"),
    description: t(
      "Join ttsopenai.com today and experience the convenience and quality of our text-to-speech service."
    ),
    links: [
      {
        label: t("Get started"),
        icon: "i-heroicons:arrow-right",
        size: "xl",
        click: () => {
          navigateTo("/signup");
        },
        trailing: true,
        class: "px-10 justify-center",
      },
    ],
  },
};
</script>

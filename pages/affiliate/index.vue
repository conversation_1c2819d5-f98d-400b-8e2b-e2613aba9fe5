<template>
    <div class="">
        <ULandingHero
            :title="page.hero.title"
            :description="page.hero.description"
            :links="page.hero.links"
            orientation="horizontal"
        >
            <template #headline>
                <UBadge
                    v-if="page.hero.headline"
                    variant="subtle"
                    size="sm"
                    class="relative rounded-full font-semibold"
                >
                    <NuxtLink :to="page.hero.headline.to" target="_blank" class="focus:outline-none" tabindex="-1">
                        <span class="absolute inset-0" aria-hidden="true" />
                    </NuxtLink>

                    {{ page.hero.headline.label }}

                    <UIcon
                        v-if="page.hero.headline.icon"
                        :name="page.hero.headline.icon"
                        class="ml-1 w-3 h-3 pointer-events-none"
                    />
                </UBadge>
            </template>
            <template #default>
                <img :src="`/assets/images/aff.svg`" />
            </template>
        </ULandingHero>

        <ULandingSection
            :ui="{
                wrapper: '!sm:pt-0',
            }"
            :headline="t('How it works')"
            :title="$t('How the TTSOpenAI program works')"
        >
            <UPageGrid>
                <ULandingCard v-for="(item, index) in hows" :key="index" v-bind="item" />
            </UPageGrid>
        </ULandingSection>

        <ULandingSection
            id="faq"
            headline="FAQ"
            :title="page.faq.title"
            :description="page.faq.description"
            class="scroll-mt-[var(--header-height)]"
        >
            <ULandingFAQ
                multiple
                :items="page.faq.items"
                :ui="{
                    button: {
                        label: 'font-semibold',
                        trailingIcon: {
                            base: 'w-6 h-6',
                        },
                    },
                }"
                class="max-w-4xl mx-auto"
            />
        </ULandingSection>

        <ULandingSection>
            <ULandingCTA
              v-bind="page.cta"
              class="bg-gray-100/50 dark:bg-gray-800/50"
            />
          </ULandingSection>
    </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const page = computed(() => {
    return {
        hero: {
            title: t('TTSOpenAI 20% Revenue Share (3 years)'),
            description:
                t('TTSOpenAI is a website that converts text into lifelike speech. It can also transform PDFs, DOCX files, and eBooks into MP3s (audiobooks). Enjoy high-quality, natural voice output, making content accessible and convenient to listen to anywhere.'),
            links: [
                {
                    label: t('Join Now'),
                    to: 'https://dash.partnerstack.com/application?company=ttsopenai&group=default',
                    color: 'primary',
                    icon: 'i-heroicons-arrow-right-20-solid',
                    trailing: true,
                    external: true,
                    target: '_blank',
                    size: 'xl',
                },
                {
                    label: t('Partner Login'),
                    to: 'https://dash.partnerstack.com/handshake/login',
                    color: 'gray',
                    icon: 'solar:login-3-bold',
                    trailing: false,
                    external: true,
                    target: '_blank',
                    size: 'xl',
                },
            ],
            headline: {
                label: t('Terms of Service'),
                to: '/terms',
                icon: 'mdi:file-document-check-outline',
            },
        },
        faq: {
            title: t('Frequently asked questions'),
            description: t('Here are some common questions about the TTSOpenAI program'),
            items: [
                {
                    label: t('How long do commissions last for?'),
                    // 3 years
                    content: t('We offer a 20% revenue share for 3 years.'),
                    defaultOpen: true
                },
                {
                    label: t('How do I get paid?'),
                    content: t('At the end of each month, your commissions are calculated and available for cash-out via PayPal or Stripe (alternative methods available for non-PayPal regions)'),
                },
                {
                    label: t('When do I get paid?'),
                    content: t('Payments are verified and paid-out the month after your commission is earned. For example, any commissions earned in February will be available March 16th.'),
                },
                {
                    label: t('How long do cookies last for?'),
                    content: t('We use Cookies to track your referrals. The cookies last for 90 days after someone clicks on your link. If they click again, the 90 days starts again.'),
                },
                {
                    label: t('What is PartnerStack?'),
                    content: t('PartnerStack helps give you the tools and resources you need to promote our product, and reward you for it!'),
                },
            ]
        },
        cta: {
            title: t('Ready to get started?'),
            description: t('Join the TTSOpenAI program and start earning commissions today'),
            links: [
                {
                    label: t('Join Now'),
                    to: 'https://dash.partnerstack.com/application?company=ttsopenai&group=default',
                    color: 'primary',
                    icon: 'i-heroicons-arrow-right-20-solid',
                    trailing: true,
                    external: true,
                    target: '_blank',
                    size: 'xl',
                },
                {
                    label: t('Partner Login'),
                    to: 'https://dash.partnerstack.com/handshake/login',
                    color: 'gray',
                    icon: 'solar:login-3-bold',
                    trailing: false,
                    external: true,
                    target: '_blank',
                    size: 'xl',
                }
            ]
        }
    }
})

const hows = computed(() => {
    return [
        {
            title: t('Step 1: Join Program'),
            description: t('and get your referral link'),
            icon: 'material-symbols:ads-click',
        },
        {
            title: t('Step 2: Share Link'),
            description: t('on social media and more'),
            icon: 'pepicons-print:share-android-circle',
        },
        {
            title: t('Step 3: Earn commissions'),
            description: t('for promoting TTSOpenAI'),
            icon: 'hugeicons:save-money-yen',
        },
    ]
})

const authStore = useAuthStore()

onMounted(() => {
    authStore.modals.manual = false
})
</script>

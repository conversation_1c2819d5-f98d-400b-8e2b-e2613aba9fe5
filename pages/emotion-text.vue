<script setup lang="ts">
const voiceLibraryStore = useVoiceLibraryStore()
const { voiceLibrariesAll, openAIVoicesPro, openAIVoices, filter } = storeToRefs(voiceLibraryStore)

const translateStore = useTranslateStore()

const { voiceIdSelected } = storeToRefs(translateStore)
definePageMeta({
    layout: false,
})

onMounted(() => {
    translateStore.clearAudioResult()

    // update voiceLibrariesAll.value.openai_voice with openAIVoicesPro + openAIVoices
    nextTick(() => {
        voiceLibrariesAll.value.openai_voice = openAIVoicesPro.value.concat(openAIVoices.value as any)
        voiceLibraryStore.fetchVoiceLibraryByType('openai_voice', true)
    })
})

onUnmounted(() => {
    voiceLibrariesAll.value.openai_voice = openAIVoices.value as any
    voiceLibraryStore.fetchVoiceLibraryByType('openai_voice', true)
    // if voiceIdSelected in openAIVoicesPro, then set voiceIdSelected to first voiceId in openAIVoices
    if (openAIVoicesPro.value.some((voice) => voice.id === voiceIdSelected.value)) {
        voiceIdSelected.value = openAIVoices.value[0].id
        filter.value.voiceTypes = 'openai_voice'
    }
})
</script>

<template>
    <div>
        <NuxtLayout name="speech-create">
            <InputTextPro />
        </NuxtLayout>
    </div>
</template>

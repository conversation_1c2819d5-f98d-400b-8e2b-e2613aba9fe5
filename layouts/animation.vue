<script setup>
import TheTermsOfService from '~/components/TheTermsOfService.vue'
import Languages from '~/components/Languages.vue'
import DarkModeToggle from '~/components/DarkModeToggle.vue'
import { onMounted } from 'vue'

const { locale } = useI18n()
useHead({
    htmlAttrs: {
        lang: locale,
    },
})

const MIN_SPEED = 0.5
const MAX_SPEED = 1.2

function randomNumber(min, max) {
    return Math.random() * (max - min) + min
}

class Blob {
    constructor(el) {
        this.el = el
        const boundingRect = this.el.getBoundingClientRect()
        this.size = boundingRect.width
        this.initialX = randomNumber(0, window.innerWidth - this.size)
        this.initialY = randomNumber(0, window.innerHeight - this.size)
        this.el.style.top = `${this.initialY}px`
        this.el.style.left = `${this.initialX}px`
        this.vx = randomNumber(MIN_SPEED, MAX_SPEED) * (Math.random() > 0.5 ? 1 : -1)
        this.vy = randomNumber(MIN_SPEED, MAX_SPEED) * (Math.random() > 0.5 ? 1 : -1)
        this.x = this.initialX
        this.y = this.initialY
    }

    update() {
        this.x += this.vx
        this.y += this.vy
        if (this.x >= window.innerWidth - this.size) {
            this.x = window.innerWidth - this.size
            this.vx *= -1
        }
        if (this.y >= window.innerHeight - this.size) {
            this.y = window.innerHeight - this.size
            this.vy *= -1
        }
        if (this.x <= 0) {
            this.x = 0
            this.vx *= -1
        }
        if (this.y <= 0) {
            this.y = 0
            this.vy *= -1
        }
    }

    move() {
        this.el.style.transform = `translate(${this.x - this.initialX}px, ${this.y - this.initialY}px)`
    }
}

function initBlobs() {
    const blobEls = document.querySelectorAll('.bouncing-blob')
    const blobs = Array.from(blobEls).map((blobEl) => new Blob(blobEl))

    function update() {
        requestAnimationFrame(update)
        blobs.forEach((blob) => {
            blob.update()
            blob.move()
        })
    }

    requestAnimationFrame(update)
}

onMounted(() => {
    if (process.client) {
        initBlobs()
    }
})
</script>

<template>
    <div>
        <div class="bouncing-blobs-container bg-gray-500/20">
            <div class="bouncing-blobs-glass"></div>
            <div class="bouncing-blobs">
                <div class="bouncing-blob bouncing-blob--blue"></div>
                <div class="bouncing-blob bouncing-blob--blue"></div>
                <div class="bouncing-blob bouncing-blob--blue"></div>
                <div class="bouncing-blob bouncing-blob--white"></div>
                <div class="bouncing-blob bouncing-blob--purple"></div>
                <div class="bouncing-blob bouncing-blob--purple"></div>
                <div class="bouncing-blob bouncing-blob--pink"></div>
            </div>
        </div>

        <section class="min-h-screen">
            <div class="z-10 min-h-[600px]">
                <Suspense>
                    <slot/>
                 </Suspense>
            </div>

            <div class="absolute bottom-5 w-screen text-center text-sm">© Copyright 2024, TTSOpenAPI</div>
            <div class="absolute flex flex-inline top-5 right-10 space-x-3 items-center z-30">
                <div>
                    <Languages class="text-black" />
                </div>
                <div>
                    <DarkModeToggle />
                </div>
            </div>
        </section>
        <TheTermsOfService />
    </div>
</template>

<style scoped>
.bouncing-blob {
    width: 32vw;
    aspect-ratio: 1;
    border-radius: 50%;
    will-change: transform;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    transform-origin: left top;
}

.bouncing-blob--blue {
    background: #52ac9d;
}

.bouncing-blob--white {
    background: #ffffff;
    z-index: 2;
    width: 15vw;
}

.bouncing-blob--purple {
    background: #8c8ff1;
}

.bouncing-blob--pink {
    background: #e289cd50;
}

.bouncing-blobs-container {
    position: fixed;
    z-index: -1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.bouncing-blobs-glass {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(140px);
    -webkit-backdrop-filter: blur(140px);
    pointer-events: none;
}

.bouncing-blobs {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

@media (max-width: 1200px) {
    .bouncing-blobs-glass {
        backdrop-filter: blur(120px);
        -webkit-backdrop-filter: blur(120px);
    }
}

@media (max-width: 500px) {
    .bouncing-blob {
        width: 60vw;
    }
    .bouncing-blob--white {
        width: 30vw;
    }
    .bouncing-blobs-glass {
        backdrop-filter: blur(90px);
        -webkit-backdrop-filter: blur(90px);
    }
}
</style>

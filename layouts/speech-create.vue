<script lang="ts" setup>
import TheHeader from '~/components/DefaultLayout/TheHeader.vue'
import TheFooter from '~/components/DefaultLayout/TheFooter.vue'
import { ModalsContainer } from 'vue-final-modal'
import VueTurnstile from 'vue-turnstile'

const voiceLibraryStore = useVoiceLibraryStore()
const runtimeConfig = useRuntimeConfig()
const { getVoiceLibraryById, filter } = storeToRefs(voiceLibraryStore)
const { t } = useI18n()
const config = useRuntimeConfig()
const { locale } = useI18n()
const translateStore = useTranslateStore()
const storyStore = useStoryStore()
const authStore = useAuthStore()
const { user, isSuperUser, showAds, isUsingPremiumPlan, canAccess } = storeToRefs(authStore)
const { showVoiceLibrariesModal } = storeToRefs(storyStore)
import { AccessEnum } from '~/types'
const {
    speed,
    voiceSelectedObj,
    modelSelectedObj,
    loadings,
    audioResult,
    showAudioPlayer,
    isPlaying,
    voiceIdSelected,
    voiceObject,

    canShowAudioPlayerOnCurrentPage,
    turnstileToken,
    turnstileRef,
    translateError,
    isOpenProcess,
    processSuccess,
    processError,
    inputFile,
    inputText,
} = storeToRefs(translateStore)

const speechStudioStore = useSpeechStudioStore()
const { isSpeechStudioInputValid } = storeToRefs(speechStudioStore)



useHead({
    htmlAttrs: {
        lang: locale,
    },
})

const colorMode = useColorMode()
const route = useRoute()



const isRequesting = ref(false)
const inputTextProStore = useInputTextProStore()
const { input: inputTextPro } = storeToRefs(inputTextProStore)
const onCreateSpeech = async () => {
    // check if selected voice is system_voice
    if (!user.value) {
        navigateTo('/signin')
        return
    }

    // check if route name is emotion-text
    if (route.name === 'emotion-text') {
        if (user.value) {
            if (isUsingPremiumPlan.value) {
                inputTextProStore.createSpeech(t)
            } else {
                navigateTo('/pricing')
            }
            return
        } else {
            navigateTo('/signin')
            return
        }
    }

    // if user is using premium plan, just create speech
    if (isUsingPremiumPlan.value || user.value) {
        await translateStore.createSpeech(t, undefined)
        return
    }

    if (!authStore.user && route.name === 'documents') {
        navigateTo('/signin')
        loadings.value['createSpeech'] = false
        return
    }

    if (route.name === 'documents' && !canAccess.value(AccessEnum.IS_UP_FILE_TO_CONVERT)) {
        navigateTo('/pricing')
        loadings.value['createSpeech'] = false
        return
    }

    isRequesting.value = true
    isOpenProcess.value = true
}
const hasValidToken = ref(false)
watch(
    () => turnstileToken.value,
    async (val) => {
        if (val && isRequesting.value) {
            hasValidToken.value = true
            await translateStore.createSpeech(t, val)
            isRequesting.value = false
        }
    }
)

watch(
    () => isOpenProcess.value,
    (val) => {
        if (!val) {
            hasValidToken.value = false
            turnstileToken.value = ''
        }
    }
)

watch(
    () => processSuccess.value,
    (val) => {
        if (val) {
            setTimeout(() => {
                isOpenProcess.value = false
            }, 1000)
        }
    }
)

const notificationsStore = useNotificationsStore()
const { needSyncData } = storeToRefs(notificationsStore)

watch(
    () => needSyncData.value,
    async (val) => {
        if (val) {
            translateStore.fetchTtsResult()
        }
    }
)
const { getAccentByValue } = useVoiceLibrary(t)
const onSelectVoice = (voiceId: string) => {
    voiceIdSelected.value = voiceId
    const voiceObj = getVoiceLibraryById.value(voiceId)
    if (voiceObj) {
        voiceObject.value = {
            ...voiceObj,
            avatar: {
                src: `/assets/images/avatars/${voiceObj.speaker_name?.toLowerCase()}.svg`,
            },
        }
    }
    showVoiceLibrariesModal.value = false
}

const isShowAudioPlayer = computed(() => {
    return audioResult.value.link && canShowAudioPlayerOnCurrentPage.value
})

const onChangeSpeed = async (value: number) => {
    speed.value = value
}

const errorMessage = ref('')
const onError = (error: string) => {
    errorMessage.value = error
}

const isEnabledButton = computed(() => {
    return (
        ((inputTextPro?.value?.trim().length > 0 && route.name === 'emotion-text') ||
            (inputText?.value?.trim().length > 0 && route.name === 'index') ||
            (inputFile.value && route.name === 'documents' && inputFile.value?.size <= 100 * 1024 * 1024) ||
            (isSpeechStudioInputValid.value && route.name === 'speech-studio')) &&
        !errorMessage.value
    )
})

onUnmounted(() => {
    translateError.value = ''
})

const onUnsupportedTurnstile = () => {
    alert('Please enable cookies to use this feature')
}

const onTurnstileError = (error: string) => {
    console.log('turnslite error:', error)
}

const showAccentSelect = computed(() => {
    return voiceObject.value.type === 'system_voice' && route.name === 'index'
})
</script>

<template>
    <div>
        <!-- <TheDrawer /> -->

        <div class="flex flex-col h-screen w-screen">
            <div class="shrink-0">
                <TheHeader />
            </div>
            <div class="flex-1 w-full flex flex-row lg:max-w-screen-2xl mx-auto">
                <div v-if="showAds" class="lg:w-[120px] hidden lg:block lg:pt-4">
                    <ins
                        class="adsbygoogle"
                        style="display: inline-block; width: 120px; height: 600px"
                        data-ad-client="ca-pub-****************"
                        data-ad-slot="6390089774"
                    ></ins>
                </div>
                <div class="flex-1 w-full ms-auto">
                    <div class="flex flex-col h-full w-full">
                        <div class="flex-1 pb-44 lg:pb-4 mt-4">
                            <div
                                class="relative h-full px-2 lg:px-4 grid grid-cols-1 lg:grid-cols-1 lg:grid-cols-4 lg:gap-4 content-stretch transition-all duration-200"
                            >
                                <div class="lg:col-span-2 transition-all duration-200 flex flex-col space-y-6 h-full">
                                    <UCard
                                        class="relative w-full flex-1"
                                        :ui="{ body: { padding: '!p-0', base: 'h-full' } }"
                                    >
                                        <div class="relative px-0 rounded-lg h-full">
                                            <div class="flex flex-col h-full">
                                                <TheFeatureMenu />
                                                <Suspense>
                                                    <slot />
                                                </Suspense>
                                            </div>
                                        </div>
                                    </UCard>
                                    <div v-if="showAds" class="h-auto w-full">
                                        <ins
                                            class="adsbygoogle h-auto w-full"
                                            style="display: block"
                                            data-ad-client="ca-pub-****************"
                                            data-ad-slot="6432384001"
                                            data-ad-format="auto"
                                            data-full-width-responsive="true"
                                        ></ins>
                                    </div>
                                </div>
                                <div class="group lg:col-span-2 relative flex-grow h-full transition-all duration-200">
                                    <VoiceLibrary
                                        class="hidden lg:flex flex-col flex-1"
                                        :active-voice-id="voiceIdSelected"
                                        @select="onSelectVoice"
                                    />
                                    <div
                                        v-if="showAccentSelect && (runtimeConfig.public.features.accent || isSuperUser)"
                                        class="lg:hidden border-t dark:border-gray-700 p-2 mt-6"
                                    >
                                        <BaseAccentSelect
                                            :voice-type="filter.voiceTypes"
                                            :language="voiceObject?.accent"
                                            @update:accent="translateStore.accent = $event"
                                        />
                                    </div>
                                    <div
                                        class="mt-4 lg:relative z-30 lg:z-10 lg:px-0 lg:bottom-0 lg:py-0 lg:bg-transparent flex flex-row justify-between lg:space-x-1 space-x-0 fixed bottom-1 w-full left-0 px-4 py-4"
                                    >
                                        <div
                                            v-if="audioResult?.link"
                                            @click="showAudioPlayer = true"
                                            class="flex px-4 lg:hidden items-center justify-center p-3 dark:bg-gray-800 rounded-l-full border border-gray-300 border-r-0 dark:border-gray-700 hover:bottom-0 cursor-pointer transition-all duration-200"
                                        >
                                            <div
                                                class="relative flex flex-row items-center justify-center ring-1 ring-gray-200 dark:ring-gray-800 h-6 w-6 bg-white/75 backdrop-blur dark:bg-gray-900/80 rounded-full"
                                            >
                                                <span
                                                    v-if="isPlaying"
                                                    class="animate-ping absolute inline-flex h-10 w-10 rounded-full bg-primary-200 dark:bg-primary-900 opacity-20"
                                                ></span>
                                                <UAvatar
                                                    :src="voiceObject.avatar.src"
                                                    :icon="
                                                        getAccentByValue(voiceObject?.accent || '')?.icon ||
                                                        'i-iconoir-voice-circle'
                                                    "
                                                    size="md"
                                                    :class="{
                                                        innertape2: isPlaying,
                                                    }"
                                                />
                                            </div>
                                        </div>

                                        <div
                                            class="flex overflow-hidden flex-col lg:flex-row gap-1 w-1/2 lg:w-full border rounded-s-full border-gray-200 dark:border-gray-700 pr-10 lg:pr-0 lg:border-0"
                                        >
                                            <SliderSpeed
                                                class="lg:w-64 w-full flex flex-col justify-center flex-1 lg:border-2 border-gray-200 rounded-l-full pt-1 px-4 bg-white dark:bg-gray-900 dark:border-gray-700"
                                                :modelValue="speed"
                                                @onChangeSpeed="onChangeSpeed"
                                                @onError="onError"
                                                :customCss="true"
                                            />
                                            <BaseAudioSetting />
                                        </div>

                                        <div
                                            class="border-8 bg-white lg:bg-transparent opacity-100 hover:shadow-2xl shadow-md rounded-full border-gray-50 dark:border-gray-950 lg:border-none lg:flex-1 absolute top-1/2 left-1/2 transform lg:transform-none lg:top-0 lg:left-0 -translate-x-1/2 -translate-y-1/2 lg:relative"
                                        >
                                            <UButton
                                                :key="route.name"
                                                @click="onCreateSpeech"
                                                icon="i-ri-chat-voice-fill"
                                                size="xl"
                                                color="primary"
                                                variant="solid"
                                                :trailing="true"
                                                class=""
                                                :ui="{
                                                    inline: 'lg:justify-between justify-center',
                                                    rounded: 'lg:rounded-l-none',
                                                    icon: {
                                                        size: {
                                                            xl: 'w-14 h-14 lg:w-6 lg:h-6',
                                                        },
                                                    },
                                                    base: 'w-20 h-20 lg:w-full lg:h-full',
                                                }"
                                                :loading="loadings.createSpeech"
                                                :disabled="!isEnabledButton"
                                                block
                                            >
                                                <div class="hidden lg:block truncate">
                                                    <span class="hidden lg:block">{{ $t('Create Speech') }}</span>
                                                </div>
                                            </UButton>
                                        </div>
                                        <UButton
                                            @click="showVoiceLibrariesModal = true"
                                            icon="i-icon-park-solid-voice-one"
                                            size="xl"
                                            color="white"
                                            square
                                            variant="solid"
                                            class="pl-4 pr-3 lg:hidden inline-flex flex-1 justify-end items-center"
                                            :ui="{
                                                rounded: 'rounded-l-none',
                                            }"
                                            :trailing="true"
                                        >
                                            <div>
                                                <span class="block lg:hidden text-xs sm:text-base">{{
                                                    voiceObject?.speaker_name
                                                }}</span>
                                                <div class="text-xs dark:text-gray-400">
                                                    {{ $t(modelSelectedObj?.label || '') }}
                                                </div>
                                            </div>
                                            <template #trailing>
                                                <UAvatar
                                                    :src="voiceObject.avatar.src"
                                                    :icon="
                                                        getAccentByValue(voiceObject?.accent || '')?.icon ||
                                                        'i-iconoir-voice-circle'
                                                    "
                                                    :ui="{ icon: { size: { sm: 'w-7 h-7' } } }"
                                                    :alt="voiceSelectedObj?.text"
                                                />
                                            </template>
                                        </UButton>
                                    </div>
                                    <div
                                        v-if="errorMessage"
                                        class="hidden lg:block sm:hidden text-red-500 text-xs mx-2"
                                    >
                                        {{ errorMessage }}
                                    </div>
                                </div>
                            </div>
                            <ModalsContainer />
                            <div v-if="errorMessage" class="text-center lg:hidden text-red-500 text-xs mx-2">
                                {{ errorMessage }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <StoryCreationModal />
            <div class="lg:shrink-0 hidden lg:block">
                <TheFooter />
            </div>

            <div
                v-if="isShowAudioPlayer"
                @click="showAudioPlayer = true"
                class="fixed lg:block hidden -bottom-2 left-1/2 p-3 dark:bg-gray-800 rounded-t-full border-t border-l border-r dark:border-gray-700 hover:bottom-0 cursor-pointer transition-all duration-200"
            >
                <div
                    v-if="isPlaying"
                    class="absolute flex flex-row items-center justify-center -bottom-7 -left-6 ring-1 ring-gray-200 dark:ring-gray-800 h-20 w-20 bg-white/75 backdrop-blur dark:bg-gray-900/80 rounded-full"
                >
                    <span
                        class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-200 dark:bg-primary-900 opacity-20"
                    ></span>
                    <UAvatar :src="`/assets/images/avatars/${audioResult?.voice}.svg`" size="2xl" class="innertape2" />
                </div>

                <UIcon v-else name="ri:skip-up-line" class="text-3xl text-primary-600 animate-bounce" />
            </div>
        </div>
        <TranslateResultAudio />
        <VoiceLibraryModal @select="onSelectVoice" />

        <UModal v-model="isOpenProcess" :ui="{ width: 'w-fit' }" prevent-close>
            <div class="p-4 flex flex-col justify-center">
                <div v-if="isRequesting" class="text-sm pb-2">
                    {{ $t('Please wait while we are creating your speech...') }}
                </div>
                <div class="w-fit">
                    <!-- :site-key="config.public.NUXT_TURNSTILE_SITE_KEY" -->
                    <vue-turnstile
                        @error="onTurnstileError"
                        @unsupported="onUnsupportedTurnstile"
                        appearance="always"
                        ref="turnstileRef"
                        :site-key="config.public.NUXT_TURNSTILE_SITE_KEY"
                        v-model="turnstileToken"
                        :language="locale"
                        :theme="colorMode.value === 'dark' ? 'dark' : 'light'"
                        :key="colorMode.value + locale"
                    />
                </div>
                <div
                    v-if="hasValidToken"
                    class="border border-gray-200 dark:border-gray-500 dark:bg-slate-900 rounded-sm p-3 flex flex-row gap-3 items-center"
                >
                    <template v-if="loadings['createSpeech']">
                        <UIcon name="i-eos-icons-loading" class="w-9 h-9 text-gray-600" />
                        <div class="text-sm text-gray-800 dark:text-gray-300">
                            {{ $t('Creating speech...') }}
                        </div>
                    </template>
                    <template v-else-if="processSuccess">
                        <UIcon name="i-fa-solid-check-circle" class="w-8 h-8 text-green-600" />
                        <div class="text-sm text-green-800 dark:text-green-400">
                            {{ $t('Speech created successfully') }}
                        </div>
                    </template>
                    <template v-else-if="processError">
                        <UIcon name="i-mingcute-close-circle-fill" class="w-9 h-9 text-red-600" />
                        <div class="text-sm text-red-800 dark:text-red-500">
                            {{ $t('Failed to create speech.') }}
                        </div>
                    </template>
                    <BaseSoundWave :static="!loadings['createSpeech']" class="cursor-pointer !m-0 !ml-auto" />
                </div>
                <UButton
                    class="w-fit mt-4 mx-auto"
                    icon="i-iconamoon-close-bold"
                    :label="$t('Close')"
                    @click="isOpenProcess = false"
                />
            </div>
        </UModal>
    </div>
</template>

<style scoped></style>

<script setup lang="ts">
import type { ParsedContent } from "@nuxt/content";
const { data: navigation } = await useAsyncData("navigation", () =>
  fetchContentNavigation()
);
const { data: files } = useLazyFetch<ParsedContent[]>("/api/search.json", {
  default: () => [],
  server: false,
});
provide("navigation", navigation);

const links = computed(() => {
  return mapContentNavigation(navigation.value || [])[0]?.children?.[0]?.children || [];
});
</script>

<template>
  <div class="h-auto absolute w-full">
    <TheDocsAppHeader />

    <UMain>
      <UContainer>
        <UPage>
          <template #left>
            <UAside>
              <UNavigationTree :links="links" />
            </UAside>
          </template>

          <div>
            <slot />
          </div>
        </UPage>
      </UContainer>
    </UMain>

    <TheDocsAppFooter />

    <ClientOnly>
      <LazyUContentSearch :files="files" :navigation="navigation" />
    </ClientOnly>

    <UNotifications />
  </div>
</template>

<script lang="ts" setup>
const { locale } = useI18n();
useHead({
  htmlAttrs: {
    lang: locale,
  },
});

onMounted(() => {
  // create script tag
  const script = document.createElement("script");
  script.src =
    "https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4149002346699299";
  script.async = true;
  script.crossOrigin = "anonymous";
  // append script tag to body
  document.body.appendChild(script);

});
</script>
<template>
  <div>
    <TheHeader class="border-b -mb-px w-full z-50" fixed />

    <UMain class="pt-0">
      <Suspense>
        <slot />
      </Suspense>
    </UMain>

    <TheFooter />
  </div>
</template>

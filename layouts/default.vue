<script lang="ts" setup>
import TheHeader from "~/components/DefaultLayout/TheHeader.vue";
import TheFooter from "~/components/DefaultLayout/TheFooter.vue";
import { ModalsContainer } from "vue-final-modal";
const { t } = useI18n();
const config = useRuntimeConfig();
const { locale } = useI18n();
const translateStore = useTranslateStore();
const { showAudioPlayer, isPlaying, loadings, audioResult } = storeToRefs(translateStore);
useHead({
  htmlAttrs: {
    lang: locale,
  },
});
const features = config.public.features;
const authStore = useAuthStore();
const { isSuperUser } = storeToRefs(authStore);
const links = computed(() => {
  return [
    {
      label: t("Input Text"),
      icon: "i-material-symbols-translate",
      to: "/",
    },
    {
      label: t("Emotion Text"),
      icon: "i-material-symbols-translate",
      to: "/emotion-text",
      hide: !(features.textToSpeechPro || isSuperUser.value),
    },
    {
      label: t("Story Maker"),
      icon: "i-fluent:people-chat-24-regular",
      to: "/story-maker",
      hide: !(features.storyMaker || isSuperUser.value),
    },
    {
      label: t("Document"),
      icon: "i-mdi-file-document-outline",
      to: "/documents",
    },
  ].filter((item) => !item.hide);
});

const route = useRoute();
</script>

<template>
  <div>
    <!-- <TheDrawer /> -->

    <div class="flex flex-col h-screen w-screen">
      <div class="shrink-0">
        <TheHeader />
      </div>
      <div class="flex-1 w-full md:max-w-screen-xl mx-auto">
        <div class="flex flex-col h-full w-full">
          <div class="flex-1 pb-44 md:pb-8 mt-4">
            <div
              class="relative h-full px-2 md:px-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-6 content-stretch transition-all duration-200"
            >
              <div
                class="lg:col-span-2 transition-all duration-200 flex flex-col space-y-6 h-full"
              >
                <UCard
                  class="relative w-full flex-1"
                  :ui="{ body: { padding: '!p-0', base: 'h-full' } }"
                >
                  <!-- <loading
                                        loader="bars"
                                        height="35"
                                        v-model:active="loadings['createSpeech']"
                                        :is-full-page="false"
                                        opacity="0"
                                    >
                                        <div class="text-lg text-center text-gray-500 dark:text-gray-600">
                                            <div>
                                                {{ $t('Processing...') }}
                                            </div>
                                            <div>
                                                {{ $t('Please wait a moment') }}
                                            </div>
                                        </div>
                                    </loading> -->
                  <div class="relative px-0 pb-6 rounded-lg h-full">
                    <div class="flex flex-col h-full wrapper">
                      <UHorizontalNavigation
                        :links="links"
                        class="border-b border-gray-200 dark:border-gray-800 px-2"
                      />

                      <Suspense>
                        <slot />
                      </Suspense>
                    </div>
                  </div>
                </UCard>
              </div>
              <div class="relative flex-grow h-full transition-all duration-200">
                <TranslateSettings />
              </div>
            </div>

            <ModalsContainer />
          </div>
          <!-- <div class="shrink-0 sticky top-0 bg-gray-100 border mt-4">
                    <img
                        src="https://marketplace.canva.com/EAFMEGXBFHc/1/0/1600w/canva-dark-blue-orange-modern-travel-with-us-leaderboard-ad-YJRVHo0iF_Y.jpg"
                        alt="banner"
                    />
                </div> -->
        </div>
      </div>

      <div class="md:shrink-0 hidden md:block">
        <TheFooter />
      </div>

      <div
        v-if="audioResult?.link"
        @click="showAudioPlayer = true"
        class="fixed md:block hidden -bottom-2 left-1/2 p-3 dark:bg-gray-800 rounded-t-full border-t border-l border-r dark:border-gray-700 hover:bottom-0 cursor-pointer transition-all duration-200"
      >
        <div
          v-if="isPlaying"
          class="absolute flex flex-row items-center justify-center -bottom-7 -left-6 ring-1 ring-gray-200 dark:ring-gray-800 h-20 w-20 bg-white/75 backdrop-blur dark:bg-gray-900/80 rounded-full"
        >
          <span
            class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-200 dark:bg-primary-900 opacity-20"
          ></span>
          <UAvatar
            :src="`/assets/images/avatars/${audioResult?.voice}.svg`"
            size="2xl"
            class="innertape2"
          />
        </div>

        <UIcon
          v-else
          name="ri:skip-up-line"
          class="text-3xl text-primary-600 animate-bounce"
        />
      </div>
    </div>
    <TranslateResultAudio />
    <!-- <MobileBottomMenu /> -->
    <!-- <TheNotificationPopup /> -->
  </div>
</template>

<style scoped></style>

<script lang="ts" setup>
import TheHeader from "~/components/DefaultLayout/TheHeader.vue";
import TheFooter from "~/components/DefaultLayout/TheFooter.vue";
import { ModalsContainer } from "vue-final-modal";
const { t } = useI18n();
const { locale } = useI18n();
const translateStore = useTranslateStore();
const storyStore = useStoryStore();
const { loadings, isOpenCreateStoryModal, stories, storyOptions } = storeToRefs(
  storyStore
);
const { modelOptions, audioResult, showAudioPlayer, isPlaying } = storeToRefs(
  translateStore
);
const authStore = useAuthStore();
const { user, showAds } = storeToRefs(authStore);
useHead({
  htmlAttrs: {
    lang: locale,
  },
});

const translatedModelOptions = computed(() => {
  return modelOptions.value.map((option) => ({
    ...option,
    label: t(option.label), // Use global i18n instance
  }));
});

const route = useRoute();

const qualityOptions = computed(() => {
  return [
    {
      label: "",
      icon: "i-carbon-mp3",
    },
    {
      label: t("Output to wav"),
      icon: "i-bi-filetype-wav",
    },
  ];
});
const toast = useToast();
const onCreateSpeech = async () => {
  const result: any = await storyStore.createStoryBlocks();
  if (result?.history_uuid) {
    const router = useRouter();
    toast.add({
      id: "create-speech",
      color: "primary",
      title: t("Success"),
      description: t(
        "Your story has been created successfully, you can check the result in history."
      ),
      actions: [
        {
          label: t("Go to history"),
          variant: "solid",
          color: "primary",
          click: () => {
            router.push({ name: "history", query: { id: result.history_uuid } });
            toast.remove("create-speech");
          },
        },
      ],
      timeout: 30000,
      icon: "i-ooui:success",
    });
  }
};

onMounted(() => {
  translateStore.clearAudioResult();

});
</script>

<template>
  <div>
    <!-- <TheDrawer /> -->

    <div class="flex flex-col h-screen w-screen">
      <div class="shrink-0">
        <TheHeader />
      </div>
      <div class="flex-1 w-full flex flex-row md:max-w-screen-2xl mx-auto">
        <div v-if="showAds" class="md:w-[120px] hidden md:block md:pt-4">
          <ins
            class="adsbygoogle"
            style="display: inline-block; width: 120px; height: 600px"
            data-ad-client="ca-pub-****************"
            data-ad-slot="6390089774"
          ></ins>
        </div>
        <div class="flex-1 w-full ms-auto">
          <div class="flex flex-col h-full w-full">
            <div class="flex-1 pb-44 md:pb-8 mt-4">
              <div
                class="relative h-full px-2 md:px-4 grid grid-cols-1 md:grid-cols-1 lg:grid-cols-4 md:gap-6 content-stretch transition-all duration-200"
              >
                <div
                  class="lg:col-span-2 transition-all duration-200 flex flex-col space-y-6 h-full"
                >
                  <UCard
                    class="relative w-full flex-1"
                    :ui="{ body: { padding: '!p-0', base: 'h-full' } }"
                  >
                    <div class="relative px-0 rounded-lg h-full">
                      <div class="flex flex-col h-full">
                        <TheFeatureMenu />

                        <Suspense>
                          <slot />
                        </Suspense>
                      </div>
                    </div>
                  </UCard>
                  <div v-if="showAds" class="pt-2 h-auto w-full">
                    <ins
                      class="adsbygoogle h-auto w-full mt-2"
                      style="display: block"
                      data-ad-client="ca-pub-****************"
                      data-ad-slot="6432384001"
                      data-ad-format="auto"
                      data-full-width-responsive="true"
                    ></ins>
                  </div>
                </div>
                <div
                  class="lg:col-span-2 relative flex-grow h-full transition-all duration-200"
                >
                  <VoiceLibrary
                    class="hidden lg:flex flex-col flex-1"
                    @select="storyStore.setVoiceToActiveStory"
                  />

                  <div
                    class="mt-3 md:relative z-30 md:z-10 md:px-0 md:bottom-0 md:py-0 md:bg-transparent flex flex-row justify-between md:space-x-1 space-x-0 fixed bottom-1 w-full left-0 px-4 py-4"
                  >
                    <USelect
                      icon="i-material-symbols-light-audio-file-outline"
                      color="white"
                      size="xl"
                      :options="translatedModelOptions"
                      v-model="storyOptions.model"
                      :placeholder="$t('Select audio quality')"
                      class="w-fit inline-flex flex-1 md:flex-none"
                      :ui="{
                        rounded: 'rounded-none md:rounded-l-full',
                        size: {
                          xl: 'text-xs md:text-base',
                        },
                      }"
                    />

                    <div
                      class="border-8 hover:shadow-2xl shadow-md rounded-full border-gray-50 dark:border-gray-950 md:border-none md:flex-1 absolute top-1/2 left-1/2 transform md:transform-none md:top-0 md:left-0 -translate-x-1/2 -translate-y-1/2 md:relative"
                    >
                      <UButton
                        icon="i-ri-chat-voice-fill"
                        size="xl"
                        color="primary"
                        variant="solid"
                        :trailing="true"
                        :ui="{
                          inline: 'md:justify-between justify-center',
                          rounded: 'md:rounded-l-none',
                          icon: {
                            size: {
                              xl: 'w-14 h-14 md:w-6 md:h-6',
                            },
                          },
                          base: 'w-20 h-20 md:w-full md:h-full',
                        }"
                        :loading="loadings.createStoryBlocks"
                        @click="
                          user ? (isOpenCreateStoryModal = true) : navigateTo('/signin')
                        "
                        :disabled="stories.length === 0"
                      >
                        <div class="hidden md:block truncate">
                          <span class="hidden md:block">{{ $t("Create Story") }}</span>
                        </div>
                      </UButton>
                    </div>
                  </div>
                </div>
              </div>
              <ModalsContainer />
            </div>
          </div>
        </div>
      </div>
      <StoryCreationModal />
      <div class="md:shrink-0 hidden md:block">
        <TheFooter />
      </div>
      <!-- <div
                v-if="audioResult?.link"
                @click="showAudioPlayer = true"
                class="fixed md:block hidden -bottom-2 left-1/2 p-3 dark:bg-gray-800 rounded-t-full border-t border-l border-r dark:border-gray-700 hover:bottom-0 cursor-pointer transition-all duration-200"
            >
                <div
                    v-if="isPlaying"
                    class="absolute flex flex-row items-center justify-center -bottom-7 -left-6 ring-1 ring-gray-200 dark:ring-gray-800 h-20 w-20 bg-white/75 backdrop-blur dark:bg-gray-900/80 rounded-full"
                >
                    <span
                        class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-200 dark:bg-primary-900 opacity-20"
                    ></span>
                    <UAvatar :src="`/assets/images/avatars/${audioResult?.voice}.svg`" size="2xl" class="innertape2" />
                </div>

                <UIcon v-else name="ri:skip-up-line" class="text-3xl text-primary-600 animate-bounce" />
            </div> -->
    </div>
    <TranslateResultAudio />
  </div>
</template>

<style scoped></style>

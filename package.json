{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "translate-locales": "./node_modules/.bin/translate-locales"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxthq/studio": "^2.2.1", "@nuxtjs/color-mode": "^3.3.0", "@nuxtjs/device": "3.2.4", "@nuxtjs/eslint-config-typescript": "^12.1.0", "@nuxtjs/eslint-module": "^4.1.0", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/supabase": "^1.3.5", "@nuxtjs/svg": "^0.4.1", "@pinia-plugin-persistedstate/nuxt": "^1.2.0", "@stefanobartoletti/nuxt-social-share": "1.2.0", "@types/node": "^18", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "@unlok-co/nuxt-stripe": "4.0.1", "nuxt": "^3.0.0", "nuxt-svgo": "4.0.9", "sass": "^1.71.1", "tailwind-scrollbar": "^3.0.4", "yup": "^1.4.0"}, "dependencies": {"@iconify/vue": "^4.1.1", "@nuxt/content": "^2.13.4", "@nuxt/fonts": "^0.10.0", "@nuxt/image": "^1.8.1", "@nuxt/ui": "^2.20.0", "@nuxt/ui-pro": "^1.6.0", "@nuxtjs/google-adsense": "^3.0.0", "@nuxtjs/i18n": "8.0.0-rc.5", "@paypal/paypal-js": "^7.0.2", "@pinia/nuxt": "^0.5.1", "@popperjs/core": "^2.11.8", "@types/lodash": "^4.14.195", "@vuelidate/core": "^2.0.2", "@vuelidate/validators": "^2.0.2", "@vueuse/components": "^10.9.0", "@vueuse/core": "^10.9.0", "axios": "^1.4.0", "click-outside-vue3": "^4.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.8", "file-saver": "^2.0.5", "firebase": "9.23.0", "flowbite": "^2.3.0", "lodash": "^4.17.21", "nuxt-og-image": "^3.0.6", "papaparse": "^5.4.1", "pinia": "^2.1.3", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1", "qrcode.vue": "^3.6.0", "srt-parser-2": "^1.2.3", "translate-locales": "^1.1.2", "uuidv4": "^6.2.13", "vue-final-modal": "^4.5.4", "vue-loading-overlay": "^6.0.3", "vue-number-animation": "^1.1.2", "vue-the-mask": "^0.11.1", "vue-turnstile": "^1.0.10", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-google-signin": "^2.1.1", "vue3-slider": "^1.9.0", "vuedraggable": "^4.1.0"}, "packageManager": "pnpm@8.9.2+sha1.5f2fa48d614263457cf5d7fb7be8b878da318d87"}
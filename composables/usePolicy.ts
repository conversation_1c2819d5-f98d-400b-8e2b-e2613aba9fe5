export const usePolicy = () => {
    const { t } = useI18n()
    return [
        {
            title: t('Privacy Policy for Text To Speech OpenAI'),
            content: t(
                'At Text To Speech OpenAI, we prioritize the protection of your privacy and the security of your personal information. This privacy policy outlines how we collect, utilize, and safeguard the information you provide when using our speech creation services. By accessing and using our website (ttsopenai.com), you consent to the practices described in this policy.'
            ),
        },
        {
            title: t('Information Collection'),
            content: t(
                'When you create an account on our website, we collect certain personal information such as your email address and complete name. This information is necessary to grant you access to our services, provide updates or changes to our services, and for statistical analysis to enhance our offerings. Additionally, any text or documents uploaded for speech creation are stored temporarily solely for the purpose of generating the speech output.'
            ),
        },
        {
            title: t('Credit Calculation'),
            content: t(
                'To ensure accurate billing, the number of credits required for speech creation is calculated based on the text or document provided. This calculation is performed using our proprietary algorithm and is directly proportional to the complexity and length of the input.'
            ),
        },
        {
            title: t('Payment & Security'),
            content: t(
                'For payment processing, we offer PayPal and credit card options. We do not store credit card information on our servers. All payment transactions are securely handled by trusted third-party payment service providers in compliance with their respective privacy and security policies.'
            ),
        },
        {
            title: t('Email Notification and Access to Speech Output'),
            content: t(
                'Upon completion of speech creation, you will receive an email notification containing a secure link to access and download the generated speech output. This link remains active for a specified period of time for your convenience.'
            ),
        },
        {
            title: t('Data Security'),
            content: t(
                'We implement industry-standard security measures to protect your personal information and uploaded documents from unauthorized access, disclosure, or alteration. While we strive to maintain the highest level of security, please note that no method of transmission over the internet or electronic storage is entirely secure.'
            ),
        },
        {
            title: t('Third-Party Services'),
            content: t(
                'We may utilize third-party services, such as analytics providers, to enhance our services and analyze usage patterns. These services may collect information about your usage but do not have access to your personal information.'
            ),
        },
        {
            title: t('Cookies and Tracking Technologies'),
            content: t(
                'Our website employs cookies and similar tracking technologies to improve user experience and analyze website usage. You have the option to disable cookies through your browser settings, but please be aware that some features of our website may not function properly as a result.'
            ),
        },
        {
            title: t('Third-Party Links'),
            content: t(
                'Our website may contain links to third-party websites. We are not responsible for the privacy practices or content of these websites and encourage you to review their respective privacy policies.'
            ),
        },
        {
            title: t("Children's Privacy"),
            content: t(
                'Our services are not intended for individuals under the age of 18, and we do not knowingly collect or store personal information from anyone under this age. If we become aware of inadvertent collection of personal information from a child under 18, we will take steps to remove such information from our records.'
            ),
        },
        {
            title: t('Updates to our Privacy Policy'),
            content: t(
                'We may periodically update our Privacy Policy to reflect changes in practices or legal requirements. Any revisions will be effective immediately upon posting the updated policy on our website. We encourage you to review this Privacy Policy periodically for the latest information.'
            ),
        },
        {
            title: t('Commercial Use'),
            content: t(
                'You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.'
            ),
        },
        {
            title: t('Other people’s privacy'),
            content: t(
                'You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.'
            ),
        },
        {
            title: t('Contact Us'),
            content: t(
                "contact-us-policy", {
                    email: "<EMAIL>"
                }
            ),
        },
        {
            title: t('Unsubscribe'),
            content: t(
                "unsubscribe-policy"
            ),
        }
    ]
}

export const useMyVoicePolicy = () => {
    const { t } = useI18n()
    return [
        {
            title: t('(a) Inputs and Outputs'),
            content: t(
                'You may provide content as input to our Services (“*Input”) and may receive content as output from the Services (“Output”, together with the Input, collectively, the “Content”). Inputs may include, without limitation, recordings of your voice, text descriptions, or any other content that you may provide to us through the Services. Your access to and use of the Services, including for the purposes of providing Input to the Services and receiving and using the Output from the Services, is subject to our Prohibited Use Policy. We may enable you to download Output from some (but not all) of the Services; in such cases, you are permitted to use such Output outside of the Services but always subject to these Terms and our Prohibited Use Policy. If you choose to make any of your information publicly available through the Services or otherwise, you do so at your own risk.'
            ),
        },
        {
            title: t('(b) Voice Models'),
            content: t(
                'Some of our Services allow for the creation of a voice model that can be used to generate synthetic audio sounding like your voice or a voice you’re authorized to share with us (a “Voice Model”). To create a Voice Model through our Services, you may be asked to upload audio recordings of your voice as Input to our Services, and TTSOpenAI is permitted to use those audio recordings of your voice subject to subsection (d) below. For more information on how we collect, use, share, retain, and destroy your audio recordings, please see the Voice Processing Notice in our Privacy Policy. You may request deletion of Voice Models created with your audio recordings through your account.'
            ),
        },
        {
            title: t('(c) Rights to your Inputs'),
            content: t(
                'Except for the license you grant below, as between you and TTSOpenAI, you retain all rights in and to your Input.'
            ),
        },
        {
            title: t('(d) License to Input'),
            content: t(
                'You hereby grant to TTSOpenAI a * perpetual and irrevocable (which means this license cannot be withdrawn), * nonexclusive (which means you can license your Input to others), * royalty-free and fully paid (which means there are no monetary fees for this license), * worldwide (which means it’s valid anywhere in the world) and * sub-licensable, through multiple tiers (which means we can make it available to others) License to use, reproduce, modify, adapt, publish, translate, create derivative works from, distribute, publicly or otherwise perform and display, and use your Input to provide the Services (including the trust and safety features therein), to improve the Services, and to develop new services and products. For avoidance of doubt, to the extent that your Input includes your voice, the foregoing license allows TTSOpenAI to reproduce, modify, publish, create derivative works from, distribute, publicly or otherwise perform, and use your voice, and other indicia of your persona that may be contained therein, to provide and improve the Services, and to develop new services and products. Notwithstanding the foregoing, we will not commercialize your voice on a standalone basis without your permission to do so.'
            ),
        },
        {
            title: t('(e) License to Voice Models and Output'),
            content: t(
                'To the extent you own or acquire any intellectual property rights in or to any Voice Models or Output, you hereby grant to TTSOpenAI a * perpetual and irrevocable (which means this license cannot be withdrawn), * nonexclusive (which means you can license your Input to others), * royalty-free and fully paid (which means there are no monetary fees for this license), * worldwide (which means it’s valid anywhere in the world) and * sub-licensable, through multiple tiers (which means we can make it available to others). License to use, reproduce, modify, adapt, publish, translate, create derivative works from, distribute, publicly or otherwise perform and display, and use the Voice Models and Output to provide the Services (including the trust and safety features therein) to you, to improve the Services, and to develop new services and products.'
            ),
        },
    ]
}

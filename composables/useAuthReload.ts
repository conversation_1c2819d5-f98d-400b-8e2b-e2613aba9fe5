import { onMounted, onUnmounted } from 'vue'

export const useAuthReload = () => {
    const authStore = useAuthStore()
    let isFirstLoad = true

    const checkIfBrowserReload = () => {
        try {
            if (isFirstLoad) {
                isFirstLoad = false
                return
            }

            const navigationEntries = performance.getEntriesByType('navigation')
            if (navigationEntries.length > 0) {
                const navigation = navigationEntries[0] as PerformanceNavigationTiming

                const isBrowserReload = navigation.type === 'reload'

                if (isBrowserReload) {
                    console.log('Browser reload detected, fetching user info...')
                    authStore.fetchUserInfo()
                }
            }
        } catch (error) {
            console.error('Error checking reload status:', error)
        }
    }

    onMounted(() => {
        window.addEventListener('beforeunload', (e) => {
            sessionStorage.setItem('browser_reload', 'true')
        })

        if (sessionStorage.getItem('browser_reload') === 'true') {
            console.log('Browser reload confirmed, fetching user info...')
            authStore.fetchUserInfo()
            // Clear flag
            sessionStorage.removeItem('browser_reload')
        }
    })

    return {
        checkIfBrowserReload,
    }
}

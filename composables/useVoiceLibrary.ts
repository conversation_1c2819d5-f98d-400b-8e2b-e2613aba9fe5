import { TrainingStatusEnum } from '~/types'

const voiceTypes = (t: any) => {
    const config = useRuntimeConfig()
    return [
        {
            value: 'openai_voice',
            label: t('OpenAI Voices'),
            icon: 'i-mingcute-openai-fill',
        },
        {
            value: 'system_voice',
            label: t('System Voices'),
            icon: 'i-mingcute-voice-line',
        },
        {
            value: 'user_voice',
            label: t('My Voices'),
            icon: 'i-fa6-solid-user-tag',
            hidden: !config.public.features.myVoice,
        },
        {
            value: 'favorite_voice',
            label: t('Favorite Voices'),
            icon: 'i-mdi-notebook-favorite',
        },
    ].filter((voice) => !voice.hidden)
}

const voiceGenders = (t: any) => [
    {
        value: 'Male',
        label: t('Male'),
    },
    {
        value: 'Female',
        label: t('Female'),
    },
]

const voiceTrainingStatus = (t: any) => [
    {
        value: TrainingStatusEnum.STARTED,
        label: t('Started'),
        color: 'yellow',
    },
    {
        value: TrainingStatusEnum.COMPLETED,
        label: t('Completed'),
        color: 'green',
    },
    {
        value: TrainingStatusEnum.FAILED,
        label: t('Failed'),
        color: 'red',
    },
]

const voiceAges = (t: any) => [
    {
        value: 'Young',
        label: t('Young'),
    },
    {
        value: 'Middle Aged',
        label: t('Middle Aged'),
    },
    {
        value: 'Old',
        label: t('Old'),
    },
]

const voiceLanguages = (t: any) => [
    {
        value: 'English',
        label: t('English'),
    },
    {
        value: 'Chinese',
        label: t('Chinese'),
    },
    {
        value: 'Spanish',
        label: t('Spanish'),
    },
]

const voiceAccents = (t: any) => [
    {
        value: 'American',
        label: t('American'),
        icon: 'i-emojione-flag-for-united-states',
        languages: ['english'],
    },
    {
        value: 'British',
        label: t('British'),
        icon: 'i-emojione-flag-for-united-kingdom',
        languages: ['english'],
    },
    {
        value: 'Australian',
        label: t('Australian'),
        icon: 'i-emojione-flag-for-australia',
        languages: ['english'],
    },
    {
        value: 'Indian',
        label: t('Indian'),
        icon: 'i-emojione-flag-for-india',
        languages: ['english'],
    },
    {
        value: 'Chinese',
        label: t('Chinese'),
        icon: 'i-emojione-flag-for-china',
        languages: ['chinese'],
    },
    {
        value: 'Spanish',
        label: t('Spanish'),
        icon: 'i-emojione-flag-for-spain',
        languages: ['spanish'],
    },
    // Canaddian
    {
        value: 'Canadian',
        label: t('Canadian'),
        icon: 'i-emojione-flag-for-canada',
        languages: ['english'],
    },
    {
        // irish
        value: 'Irish',
        label: t('Irish'),
        icon: 'i-emojione-flag-for-ireland',
        languages: ['english'],
    },
    {
        // singaporean
        value: 'Singaporean',
        label: t('Singaporean'),
        icon: 'i-emojione-flag-for-singapore',
        languages: ['english'],
    },
    {
        //russian
        value: 'Russian',
        label: t('Russian'),
        icon: 'i-emojione-flag-for-russia',
        languages: ['russian'],
    },
    {
        // german
        value: 'German',
        label: t('German'),
        icon: 'i-emojione-flag-for-germany',
        languages: ['german'],
    },
    {
        // portuguese
        value: 'Portuguese',
        label: t('Portuguese'),
        icon: 'i-emojione-flag-for-portugal',
        languages: ['portuguese'],
    },
    {
        // hindi
        value: 'Hindi',
        label: t('Hindi'),
        icon: 'i-emojione-flag-for-india',
        languages: ['hindi'],
    },
    {
        // mexican
        value: 'Mexican',
        label: t('Mexican'),
        icon: 'i-emojione-flag-for-mexico',
        languages: ['spanish'],
    },
    {
        // latin american
        value: 'Latin American',
        label: t('Latin American'),
        icon: 'i-emojione-flag-for-mexico',
        languages: ['spanish'],
    },
    {
        // argentine
        value: 'Argentine',
        label: t('Argentine'),
        icon: 'i-emojione-flag-for-argentina',
        languages: ['spanish'],
    },
    {
        // peninsular
        value: 'Peninsular',
        label: t('Peninsular'),
        icon: 'i-emojione-flag-for-spain',
        languages: ['spanish'],
    },
    {
        // peninsular
        value: 'Peninsular',
        label: t('Peninsular'),
        icon: 'i-emojione-flag-for-spain',
        languages: ['spanish'],
    },
    {
        // french
        value: 'French',
        label: t('French'),
        icon: 'i-emojione-flag-for-france',
        languages: ['french'],
    },
    {
        // parisian
        value: 'Parisian',
        label: t('Parisian'),
        icon: 'i-emojione-flag-for-france',
        languages: ['french'],
    },
    {
        // standard
        value: 'Standard',
        label: t('Standard'),
        icon: 'i-emojione-flag-for-france',
        languages: ['french'],
    },
    {
        // brazilian
        value: 'Brazilian',
        label: t('Brazilian'),
        icon: 'i-emojione-flag-for-brazil',
        languages: ['portuguese'],
    },
    {
        //Turkish
        value: 'Turkish',
        label: t('Turkish'),
        icon: 'i-emojione-flag-for-turkey',
        languages: ['turkish'],
    },
    {
        // istanbul
        value: 'Istanbul',
        label: t('Istanbul'),
        icon: 'i-emojione-flag-for-turkey',
        languages: ['turkish'],
    },
    {
        // bavarian
        value: 'Bavarian',
        label: t('Bavarian'),
        icon: 'i-emojione-flag-for-germany',
        languages: ['german'],
    },
    {
        // polish
        value: 'Polish',
        label: t('Polish'),
        icon: 'i-emojione-flag-for-poland',
        languages: ['polish'],
    },
    {
        // italian
        value: 'Italian',
        label: t('Italian'),
        icon: 'i-emojione-flag-for-italy',
        languages: ['italian'],
    },
    {
        // australian
        value: 'Australian',
        label: t('Australian'),
        icon: 'i-emojione-flag-for-australia',
        languages: ['english'],
    },
    {
        // south african
        value: 'South African',
        label: t('South African'),
        icon: 'i-emojione-flag-for-south-africa',
        languages: ['english'],
    },
    {
        // scottish
        value: 'Scottish',
        label: t('Scottish'),
        icon: 'i-emojione-flag-for-united-kingdom',
        languages: ['english'],
    },
    {
        // welsh
        value: 'Welsh',
        label: t('Welsh'),
        icon: 'i-emojione-flag-for-united-kingdom',
        languages: ['english'],
    },
    {
        // new zealand
        value: 'New Zealand',
        label: t('New Zealand'),
        icon: 'i-emojione-flag-for-new-zealand',
        languages: ['english'],
    },
    {
        // dutch
        value: 'Dutch',
        label: t('Dutch'),
        icon: 'i-emojione-flag-for-netherlands',
        languages: ['dutch'],
    },
    {
        // belgian
        value: 'Belgian',
        label: t('Belgian'),
        icon: 'i-emojione-flag-for-belgium',
        languages: ['dutch'],
    },
    {
        // swedish
        value: 'Swedish',
        label: t('Swedish'),
        icon: 'i-emojione-flag-for-sweden',
        languages: ['swedish'],
    },
    {
        // norwegian
        value: 'Norwegian',
        label: t('Norwegian'),
        icon: 'i-emojione-flag-for-norway',
        languages: ['norwegian'],
    },
    {
        // danish
        value: 'Danish',
        label: t('Danish'),
        icon: 'i-emojione-flag-for-denmark',
        languages: ['danish'],
    },
    {
        // italian
        value: 'Italian',
        label: t('Italian'),
        icon: 'i-emojione-flag-for-italy',
        languages: ['italian'],
    },
    {
        // korean
        value: 'Korean',
        label: t('Korean'),
        icon: 'i-emojione-flag-for-south-korea',
        languages: ['korean'],
    },
    {
        // korean, seoul
        value: 'Korean, Seoul',
        label: t('Korean, Seoul'),
        icon: 'i-emojione-flag-for-south-korea',
        languages: ['korean'],
    },
    {
        // japanese
        value: 'Japanese',
        label: t('Japanese'),
        icon: 'i-emojione-flag-for-japan',
        languages: ['japanese'],
    },
    {
        // dutch
        value: 'Dutch',
        label: t('Dutch'),
        icon: 'i-emojione-flag-for-netherlands',
        languages: ['dutch'],
    },
    {
        // croatian
        value: 'Croatian',
        label: t('Croatian'),
        icon: 'i-emojione-flag-for-croatia',
        languages: ['croatian'],
    },
    {
        // czech
        value: 'Czech',
        label: t('Czech'),
        icon: 'i-emojione-flag-for-czechia',
        languages: ['czech'],
    },
    {
        // moravian
        value: 'Moravian',
        label: t('Moravian'),
        icon: 'i-emojione-flag-for-czechia',
        languages: ['czech'],
    },
    {
        // zealandic
        value: 'Zealandic',
        label: t('Zealandic'),
        icon: 'i-emojione-flag-for-denmark',
        languages: ['danish'],
    },
    {
        // indonesian
        value: 'Indonesian',
        label: t('Indonesian'),
        icon: 'i-emojione-flag-for-indonesia',
        languages: ['indonesian'],
    },
    {
        // javanese
        value: 'Javanese',
        label: t('Javanese'),
        icon: 'i-emojione-flag-for-indonesia',
        languages: ['indonesian'],
    },
    {
        // romanian
        value: 'Romanian',
        label: t('Romanian'),
        icon: 'i-emojione-flag-for-romania',
        languages: ['romanian'],
    },
    {
        // swiss
        value: 'Swiss',
        label: t('Swiss'),
        icon: 'i-emojione-flag-for-switzerland',
        languages: ['swiss'],
    },
    {
        // vietnamese
        value: 'Vietnamese',
        label: t('Vietnamese'),
        icon: 'i-emojione-flag-for-vietnam',
        languages: ['vietnamese'],
    },
    {
        value: 'Arabic',
        label: t('Arabic'),
        icon: 'i-emojione-flag-for-saudi-arabia',
        languages: ['arabic'],
    },
    {
        value: 'Bulgarian',
        label: t('Bulgarian'),
        icon: 'i-emojione-flag-for-bulgaria',
        languages: ['bulgarian'],
    },
    {
        value: 'Finnish',
        label: t('Finnish'),
        icon: 'i-emojione-flag-for-finland',
        languages: ['finnish'],
    },
    {
        value: 'Greek',
        label: t('Greek'),
        icon: 'i-emojione-flag-for-greece',
        languages: ['greek'],
    },
    {
        value: 'Hungarian',
        label: t('Hungarian'),
        icon: 'i-emojione-flag-for-hungary',
        languages: ['hungarian'],
    },
    {
        value: 'Filipino',
        label: t('Filipino'),
        icon: 'i-emojione-flag-for-philippines',
        languages: ['filipino'],
    },
]

const getAccentByValue = (value: string, t: any) => {
    return voiceAccents(t).find((accent) => accent.value?.toLowerCase() === value?.toLowerCase())
}

const voiceCountries = (t: any) => [
    {
        value: 'indian_english',
        label: t('Indian English'),
        icon: 'i-emojione-flag-for-india',
        input: 'indian_english',
        languages: ['english'],
        gender: 'Female'
    },
    {
        value: 'australian_english',
        label: t('Australian English'),
        icon: 'i-emojione-flag-for-australia',
        input: 'australian_english',
        languages: ['english'],
        gender: 'Female'
    },
    {
        value: 'canadian_english',
        label: t('Canadian English'),
        icon: 'i-emojione-flag-for-canada',
        input: 'canadian_english',
        languages: ['english'],
        gender: 'Female'
    },
    {
        value: 'british_english',
        label: t('British English'),
        icon: 'i-emojione-flag-for-united-kingdom',
        input: 'british_english',
        languages: ['english'],
        gender: 'Female'
    },
    {
        value: 'south_african_english',
        label: t('South African English'),
        icon: 'i-emojione-flag-for-south-africa',
        input: 'south_african_english',
        languages: ['english'],
        gender: 'Female'
    },
    {
        value: 'philippine_english',
        label: t('Philippine English'),
        icon: 'i-emojione-flag-for-philippines',
        input: 'philippine_english',
        languages: ['english'],
        gender: 'Female'
    }
]

const getCountryByValue = (value: string, t: any) => {
    return voiceCountries(t).find((country) => country.value?.toLowerCase() === value?.toLowerCase())
}

const voiceEmotions = (t: any) => {
    return [
        {
            value: 'angry',
            label: t('Angry'),
            icon: 'i-emojione-angry-face',
        },
        {
            value: 'happy',
            label: t('Happy'),
            icon: 'i-emojione-smiling-face',
        },
        {
            value: 'sad',
            label: t('Sad'),
            icon: 'i-emojione-crying-face',
        },
        {
            value: 'fearful',
            label: t('Fearful'),
            icon: 'i-emojione-fearful-face',
        },
        {
            value: 'disgusted',
            label: t('Disgusted'),
            icon: 'i-emojione-confounded-face',
        },
        {
            value: 'surprised',
            label: t('Surprised'),
            icon: 'i-emojione-astonished-face',
        },
        {
            value: 'neutral',
            label: t('Neutral'),
            icon: 'i-emojione-neutral-face',
        },
    ]
}

const getVoiceEmotionByValue = (value: string, t: any) => {
    return voiceEmotions(t).find((emotion) => emotion.value === value?.toLowerCase())
}

const getVoiceTrainingStatusByValue = (value: TrainingStatusEnum | null, t: any) => {
    return voiceTrainingStatus(t).find((status) => status.value === value)
}

export const useVoiceLibrary = (t: any) => {
    return {
        voiceTypes: voiceTypes(t),
        voiceGenders: voiceGenders(t),
        voiceAges: voiceAges(t),
        voiceLanguages: voiceLanguages(t),
        voiceAccents: voiceAccents(t),
        voiceCountries: voiceCountries(t),
        getAccentByValue: (value: string) => getAccentByValue(value, t),
        getCountryByValue: (value: string) => getCountryByValue(value, t),
        voiceEmotions: voiceEmotions(t),
        getVoiceEmotionByValue: (value: string) => getVoiceEmotionByValue(value, t),
        voiceTrainingStatus: voiceTrainingStatus(t),
        getVoiceTrainingStatusByValue: (value: TrainingStatusEnum | null) => getVoiceTrainingStatusByValue(value, t),
    }
}

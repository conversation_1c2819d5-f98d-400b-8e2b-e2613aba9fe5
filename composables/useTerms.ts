import { title } from 'process'

export const useTerms = () => {
    const { t } = useI18n()
    return [
        {
            title: t('Terms Acceptance'),
            content: t(
                'By utilizing the Speech Creation Service, you agree to adhere to these Terms of Service. If you disagree with any part of these terms, please refrain from using our services.'
            ),
        },
        {
            title: t('User Accounts'),
            content: t(
                'Accessing certain features of our service may necessitate the creation of a user account. You commit to maintaining the confidentiality of your account credentials and accept sole responsibility for all activities conducted under your account.'
            ),
        },
        {
            title: t('Payment and Credits'),
            content: t(
                'Our speech creation service operates on a credit-based system. The number of credits required for speech creation is determined by our proprietary algorithm and is accurately calculated based on the input text or documents. Upon depletion of your credit balance, you must recharge your account. Payments can be made via PayPal or credit card.'
            ),
        },
        {
            title: t('Service Usage'),
            content: t(
                'You undertake to employ our services solely for lawful purposes. You shall refrain from uploading, transmitting, or storing any content that is unlawful, harmful, defamatory, or infringes upon the rights of others. You are solely liable for any content submitted for speech creation.'
            ),
        },
        {
            title: t('Intellectual Property'),
            content: t(
                'All content and materials accessible through our service, encompassing text, graphics, logos, and software, are the property of ttsopenai.com and are safeguarded by intellectual property laws. Reproduction, modification, distribution, or creation of derivative works of the content without prior written consent is prohibited.'
            ),
        },
        {
            title: t('Limitation of Liability'),
            content: t(
                'ttsopenai.com shall not be held liable for any direct, indirect, incidental, special, or consequential damages arising from or related to your use of our services. We do not warrant the accuracy, completeness, or availability of the services and disclaim all warranties, whether express or implied, regarding their use or outcomes.'
            ),
        },
        {
            title: t('Termination'),
            content: t(
                'We retain the right to suspend or terminate your account and access to our services at our discretion, with or without cause. Upon termination, your account and associated data will be deleted, except for information mandated to be retained for legal or accounting purposes.'
            ),
        },
        {
            title: t('Governing Law'),
            content: t(
                'These Terms of Service shall be interpreted and governed in accordance with the laws of Viet Nam, without regard to its conflict of law principles. Any disputes arising from or relating to these Terms and the utilization of our services shall be subject to the exclusive jurisdiction of the courts in Viet Nam.'
            ),
        },
        {
            title: t('Clarification Regarding Affiliation with OpenAI'),
            content: t(
                "ttsopenai.com is an independent entity and is not affiliated with OpenAI. Our text-to-speech services utilize the OpenAI API to convert text into speech, but we operate independently from OpenAI. This clarification is made to prevent any confusion or misunderstanding regarding the relationship between ttsopenai.com and OpenAI. Users should be aware that while we use OpenAI's technology to provide our services, ttsopenai.com is solely responsible for the operation of our services and the adherence to these Terms of Service."
            ),
        },
    ]
}

const emotionList = computed(() => [
    { id: 1, emojis: 'Happy.svg', name: '<PERSON>', description: 'Bright, gentle voice, expressing excitement.' },
    { id: 2, emojis: 'Sad.svg', name: 'Sad', description: 'Low, slow voice, conveying deep emotions.' },
    { id: 3, emojis: 'Angry.svg', name: 'Angry', description: 'Sharp, exaggerated voice, expressing frustration.' },
    { id: 4, emojis: 'Excited.svg', name: 'Excited', description: 'Fast, lively voice, full of enthusiasm.' },
    {
        id: 5,
        emojis: 'Laughing.svg',
        name: 'Laughing',
        description: 'Interrupted, joyful voice, interspersed with laughter.',
    },
    { id: 6, emojis: 'Crying.svg', name: 'Crying', description: 'Shaky, low voice, expressing pain.' },
    { id: 7, emojis: 'Calm.svg', name: '<PERSON><PERSON>', description: 'Gentle, steady voice, providing reassurance.' },
    { id: 8, emojis: 'Serious.svg', name: 'Ser<PERSON>', description: 'Mature, clear voice, suitable for formal content.' },
    { id: 9, emojis: 'Frustrated.svg', name: 'Frustrated', description: 'Weary, slightly irritated voice.' },
    { id: 10, emojis: 'Hopeful.svg', name: 'Hopeful', description: 'Bright voice, conveying positivity and hope.' },
    { id: 11, emojis: 'Narrative.svg', name: 'Narrative', description: 'Natural, gentle voice with a slow rhythm.' },
    {
        id: 12,
        emojis: "Kids' Storytelling.svg",
        name: "Kids' Storytelling",
        description: 'Lively, engaging voice, captivating for children.',
    },
    {
        id: 13,
        emojis: 'Audiobook.svg',
        name: 'Audiobook',
        description: 'Even, slow voice, emphasizing content meaning.',
    },
    { id: 14, emojis: 'Poetic.svg', name: 'Poetic', description: 'Rhythmic, emotional voice, conveying subtlety.' },
    { id: 15, emojis: 'Mysterious.svg', name: 'Mysterious', description: 'Low, slow voice, evoking curiosity.' },
    {
        id: 16,
        emojis: 'Inspirational.svg',
        name: 'Inspirational',
        description: 'Strong, passionate voice, driving action.',
    },
    {
        id: 17,
        emojis: 'Surprised.svg',
        name: 'Surprised',
        description: 'High, interrupted voice, expressing astonishment.',
    },
    {
        id: 18,
        emojis: 'Confident.svg',
        name: 'Confident',
        description: 'Firm, powerful voice, persuasive and assuring.',
    },
    {
        id: 19,
        emojis: 'Romantic.svg',
        name: 'Romantic',
        description: 'Sweet, gentle voice, suitable for emotional content.',
    },
    { id: 20, emojis: 'Scared.svg', name: 'Scared', description: 'Shaky, interrupted voice, conveying anxiety.' },
    {
        id: 21,
        emojis: 'Trailer Voice.svg',
        name: 'Trailer Voice',
        description: 'Deep, strong voice with emphasis, creating suspense.',
    },
    {
        id: 22,
        emojis: 'Advertising.svg',
        name: 'Advertising',
        description: 'Engaging, lively voice, emphasizing product benefits.',
    },
    { id: 23, emojis: 'Speech.svg', name: 'Speech', description: 'Formal, clear voice with focus on key points.' },
    {
        id: 24,
        emojis: 'Documentary.svg',
        name: 'Documentary',
        description: 'Calm, profound voice, delivering authenticity.',
    },
    {
        id: 25,
        emojis: 'Newsreader.svg',
        name: 'Newsreader',
        description: 'Standard, neutral voice, clear and precise.',
    },
    {
        id: 26,
        emojis: 'Weather Report.svg',
        name: 'Weather Report',
        description: 'Bright, neutral voice, suitable for concise updates.',
    },
    {
        id: 27,
        emojis: 'Game Commentary.svg',
        name: 'Game Commentary',
        description: 'Fast, lively voice, stimulating excitement.',
    },
    {
        id: 28,
        emojis: 'Interactive.svg',
        name: 'Interactive',
        description: 'Friendly, approachable voice, encouraging engagement.',
    },
    {
        id: 29,
        emojis: 'Customer Support.svg',
        name: 'Customer Support',
        description: 'Empathetic, gentle voice, easy to connect with.',
    },
    { id: 30, emojis: 'FAQ.svg', name: 'FAQ', description: 'Clear voice, emphasizing questions and answers.' },
    { id: 31, emojis: 'Playful.svg', name: 'Playful', description: 'Cheerful, playful voice with a hint of mischief.' },
    { id: 32, emojis: 'Tired.svg', name: 'Tired', description: 'Slow, soft voice lacking energy.' },
    { id: 33, emojis: 'Sarcastic.svg', name: 'Sarcastic', description: 'Ironic, sharp voice, sometimes humorous.' },
    { id: 34, emojis: 'Disgusted.svg', name: 'Disgusted', description: 'Cold voice, clearly expressing discomfort.' },
    { id: 35, emojis: 'Whispering.svg', name: 'Whispering', description: 'Soft, mysterious voice, creating intimacy.' },
    {
        id: 36,
        emojis: 'Persuasive.svg',
        name: 'Persuasive',
        description: 'Emotional voice, convincing the listener to act.',
    },
    {
        id: 37,
        emojis: 'Nostalgic.svg',
        name: 'Nostalgic',
        description: 'Gentle voice, evoking feelings of reminiscence.',
    },
    {
        id: 38,
        emojis: 'Meditative.svg',
        name: 'Meditative',
        description: 'Even, relaxing voice, suitable for mindfulness.',
    },
    { id: 39, emojis: 'Announcement.svg', name: 'Announcement', description: 'Clear voice, emphasizing key words.' },
    {
        id: 40,
        emojis: 'Professional Pitch.svg',
        name: 'Professional Pitch',
        description: 'Confident, clear voice, ideal for business presentations.',
    },
    {
        id: 41,
        emojis: 'Casual.svg',
        name: 'Casual',
        description: 'Natural, friendly voice, as if talking to a friend.',
    },
    {
        id: 42,
        emojis: 'Exciting Trailer.svg',
        name: 'Exciting Trailer',
        description: 'Fast, powerful voice, creating tension and excitement.',
    },
    {
        id: 43,
        emojis: 'Dramatic.svg',
        name: 'Dramatic',
        description: 'Emphasized, suspenseful voice, creating intensity.',
    },
    {
        id: 44,
        emojis: 'Corporate.svg',
        name: 'Corporate',
        description: 'Professional, formal voice, suitable for business content.',
    },
    {
        id: 45,
        emojis: 'Tech Enthusiast.svg',
        name: 'Tech Enthusiast',
        description: 'Energetic, lively voice, introducing new technologies.',
    },
    {
        id: 46,
        emojis: 'Youthful.svg',
        name: 'Youthful',
        description: 'Vibrant, cheerful voice, appealing to younger audiences.',
    },
    {
        id: 47,
        emojis: 'Calming Reassurance.svg',
        name: 'Calming Reassurance',
        description: 'Gentle, empathetic voice, easing concerns.',
    },
    { id: 48, emojis: 'Heroic.svg', name: 'Heroic', description: 'Strong, decisive voice, full of inspiration.' },
    {
        id: 49,
        emojis: 'Festive.svg',
        name: 'Festive',
        description: 'Bright, excited voice, suitable for celebrations.',
    },
    { id: 50, emojis: 'Urgent.svg', name: 'Urgent', description: 'Fast, strong voice, emphasizing urgency.' },
    {
        id: 51,
        emojis: 'Motivational.svg',
        name: 'Motivational',
        description: 'Passionate, inspiring voice, encouraging action.',
    },
    {
        id: 52,
        emojis: 'Friendly.svg',
        name: 'Friendly',
        description: 'Warm, approachable voice, fostering connection.',
    },
    {
        id: 53,
        emojis: 'Energetic.svg',
        name: 'Energetic',
        description: 'Fast, powerful voice, brimming with enthusiasm.',
    },
    { id: 54, emojis: 'Serene.svg', name: 'Serene', description: 'Slow, gentle voice, evoking peace and tranquility.' },
    { id: 55, emojis: 'Bold.svg', name: 'Bold', description: 'Firm, assertive voice, exuding confidence.' },
    {
        id: 56,
        emojis: 'Charming.svg',
        name: 'Charming',
        description: 'Warm, captivating voice, leaving a strong impression.',
    },
    {
        id: 57,
        emojis: 'Monotone.svg',
        name: 'Monotone',
        description: 'Flat, unvaried voice, conveying neutrality or irony.',
    },
    { id: 58, emojis: 'Questioning.svg', name: 'Questioning', description: 'Curious voice, emphasizing questions.' },
    {
        id: 59,
        emojis: 'Directive.svg',
        name: 'Directive',
        description: 'Firm, clear voice, guiding the listener step-by-step.',
    },
    { id: 60, emojis: 'Dreamy.svg', name: 'Dreamy', description: 'Gentle, slow voice, evoking a floating sensation.' },
    { id: 61, emojis: 'Epic.svg', name: 'Epic', description: 'Deep, resonant voice, emphasizing grandeur.' },
    { id: 62, emojis: 'Lyrical.svg', name: 'Lyrical', description: 'Soft, melodic voice, similar to singing.' },
    { id: 63, emojis: 'Mystical.svg', name: 'Mystical', description: 'Low, drawn-out voice, evoking mystery.' },
    { id: 64, emojis: 'Melancholy.svg', name: 'Melancholy', description: 'Slow, low voice, conveying deep sadness.' },
    { id: 65, emojis: 'Cheerful.svg', name: 'Cheerful', description: 'Bright, energetic voice, full of positivity.' },
    { id: 66, emojis: 'Eerie.svg', name: 'Eerie', description: 'Low, whispery voice, evoking fear or strangeness.' },
    { id: 67, emojis: 'Flirtatious.svg', name: 'Flirtatious', description: 'Sweet, teasing voice, full of allure.' },
    {
        id: 68,
        emojis: 'Thoughtful.svg',
        name: 'Thoughtful',
        description: 'Slow, reflective voice, full of contemplation.',
    },
    {
        id: 69,
        emojis: 'Cinematic.svg',
        name: 'Cinematic',
        description: 'Resonant, emphasized voice, creating a movie-like effect.',
    },
    {
        id: 70,
        emojis: 'Humorous.svg',
        name: 'Humorous',
        description: 'Lighthearted, cheerful voice, sometimes exaggerated.',
    },
    {
        id: 71,
        emojis: 'Instructional.svg',
        name: 'Instructional',
        description: 'Clear, slow voice, guiding the listener step-by-step.',
    },
    {
        id: 72,
        emojis: 'Conversational.svg',
        name: 'Conversational',
        description: 'Natural voice, as if chatting with the listener.',
    },
    { id: 73, emojis: 'Apologetic.svg', name: 'Apologetic', description: 'Soft, sincere voice, expressing regret.' },
    {
        id: 74,
        name: 'Excuse-making',
        description: 'Hesitant, uncertain voice, sometimes awkward.',
        emojis: 'Excuse-making.svg',
    },
    {
        id: 75,
        name: 'Encouraging',
        description: 'Warm voice, providing motivation and support.',
        emojis: 'Encouraging.svg',
    },
    { id: 76, name: 'Neutral', description: 'Even voice, free of emotional bias.', emojis: 'Neutral.svg' },
    {
        id: 77,
        emojis: 'Authoritative.svg',
        name: 'Authoritative',
        description: 'Strong, powerful voice, exuding credibility.',
    },
    {
        id: 78,
        name: 'Sarcastic Cheerful',
        description: 'Cheerful voice with an undertone of mockery.',
        emojis: 'Sarcastic Cheerful.svg',
    },
    {
        id: 79,
        name: 'Reassuring',
        description: 'Gentle, empathetic voice, providing comfort.',
        emojis: 'Reassuring.svg',
    },
    { id: 80, name: 'Formal', description: 'Clear, polite voice, suited for formal occasions.', emojis: 'Formal.svg' },
    { id: 81, emojis: 'Anguished.svg', name: 'Anguished', description: 'Urgent, shaky voice, expressing distress.' },
    { id: 82, name: 'Giggling', description: 'Interrupted voice, mixed with light laughter.', emojis: 'Giggling.svg' },
    { id: 83, name: 'Exaggerated', description: 'Loud, emphasized voice, often humorous.', emojis: 'Exaggerated.svg' },
    { id: 84, name: 'Cold', description: 'Flat, unemotional voice, conveying detachment.', emojis: 'Cold.svg' },
    {
        id: 85,
        name: 'Hot-tempered',
        description: 'Fast, sharp voice, sometimes out of control.',
        emojis: 'Hot-tempered.svg',
    },
    { id: 86, emojis: 'Grateful.svg', name: 'Grateful', description: 'Warm, sincere voice, expressing appreciation.' },
    { id: 87, name: 'Regretful', description: 'Low, subdued voice, full of remorse.', emojis: 'Regretful.svg' },
    {
        id: 88,
        name: 'Provocative',
        description: 'Challenging, strong voice, full of insinuation.',
        emojis: 'Provocative.svg',
    },
    { id: 89, name: 'Triumphant', description: 'Loud, powerful voice, full of victory.', emojis: 'Triumphant.svg' },
    {
        id: 90,
        name: 'Vengeful',
        description: 'Low, cold voice, expressing determination for revenge.',
        emojis: 'Vengeful.svg',
    },
    {
        id: 91,
        name: 'Heroic Narration',
        description: 'Strong, inspiring voice, emphasizing heroic deeds.',
        emojis: 'Heroic Narration.svg',
    },
    { id: 92, name: 'Villainous', description: 'Low, drawn-out voice, full of scheming.', emojis: 'Villainous.svg' },
    {
        id: 93,
        name: 'Hypnotic',
        description: 'Even, repetitive voice, drawing the listener in.',
        emojis: 'Hypnotic.svg',
    },
    {
        id: 94,
        name: 'Desperate',
        description: 'Urgent, shaky voice, expressing hopelessness.',
        emojis: 'Desperate.svg',
    },
    { id: 95, name: 'Lamenting', description: 'Low, sorrowful voice, as if mourning.', emojis: 'Lamenting.svg' },
    {
        id: 96,
        emojis: 'Celebratory.svg',
        name: 'Celebratory',
        description: 'Excited, joyful voice, full of festive spirit.',
    },
    { id: 97, name: 'Teasing', description: 'Light, playful voice, sometimes mockingly.', emojis: 'Teasing.svg' },
    {
        id: 98,
        name: 'Exhausted',
        description: 'Weak, broken voice, expressing extreme fatigue.',
        emojis: 'Exhausted.svg',
    },
    {
        id: 99,
        name: 'Questioning Suspicious',
        description: 'Slow, emphasized voice, full of suspicion.',
        emojis: 'Questioning Suspicious.svg',
    },
    {
        id: 100,
        name: 'Optimistic',
        description: 'Bright, hopeful voice, creating positivity.',
        emojis: 'Optimistic.svg',
    },
])

export const useEmotions = () => {
    return {
        emotionList,
    }
}

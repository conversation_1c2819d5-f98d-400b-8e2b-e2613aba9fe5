// @ts-ignore
import axios from 'axios'
import { useAxios } from '@vueuse/integrations/useAxios'

import { useAuthStore } from '~/stores/auth'
import { useTranslateStore } from '~/stores/translate'

let isRefreshingToken = false
let requestsPending = [] as any[]

interface RefreshTokenResponse {
    access_token: string
    refresh_token?: string
}

export const useAPIOld: typeof useFetch = (request, opts?, token = null) => {
    const config = useRuntimeConfig()
    const access_token = localStorage.getItem('access_token')
    const refresh_token = localStorage.getItem('refresh_token')
    const headers = {
        Authorization: '',
    }
    if (access_token) {
        headers.Authorization = `Bearer ${token ? token : access_token}`
    }

    return useFetch(request, {
        baseURL: config.public.baseURL,
        ...opts,
        headers,
        async onResponseError({ request, response, options }) {
            console.log('🚀 ~ onResponseError ~ response:', response)
            let _errorResponse = response
            const originalRequest = options
            const status = _errorResponse.status
            const authStore = useAuthStore()
            const voiceLibraryStore = useVoiceLibraryStore()
            const translateStore = useTranslateStore()
            switch (status) {
                case 403:
                    if (response.url.includes('/refresh-token') || !refresh_token) {
                        authStore.setIsExpired(true)
                        authStore.logout()
                        return Promise.reject(response)
                    } else {
                        //retry with refrest token first
                        if (!originalRequest.retry) {
                            // save request if it is not refresh token request
                            if (isRefreshingToken) {
                                return new Promise(function (resolve, reject) {
                                    requestsPending.push(function () {
                                        resolve(useAPI(request, options))
                                    })
                                })
                            }
                            originalRequest.retry = 1
                            isRefreshingToken = true

                            try {
                                const { data }: { data: Ref<RefreshTokenResponse>; error: any } = await useAPI(
                                    '/refresh-token',
                                    {
                                        method: 'POST',
                                        body: {
                                            refresh_token,
                                        },
                                    }
                                )
                                translateStore.clearTranslateError()
                                if (data.value) {
                                    localStorage.setItem('access_token', data.value.access_token)
                                    localStorage.setItem('refresh_token', data.value.refresh_token || '')
                                    authStore.fetchUserInfo()

                                    return useAPI(request, options)
                                } else {
                                    authStore.setIsExpired(true)
                                    authStore.logout()
                                }
                            } catch (refreshError) {
                                isRefreshingToken = false
                                authStore.setIsExpired(true)
                                authStore.logout()
                            } finally {
                                isRefreshingToken = false
                                translateStore.clearTranslateError()
                                requestsPending.forEach((callback) => callback())
                                requestsPending = []
                            }
                        } else {
                            authStore.setIsExpired(true)
                            authStore.logout()
                        }
                    }
                    break
                default:
                    break
            }

            // throw response
        },
    })
}

export const useAPI = async (request: any, opts?: any, token = null) => {
    const config = useRuntimeConfig()

    const axiosInstance = axios.create({
        baseURL: config.public.baseURL,
        ...opts,
        data: opts.body,
        params: opts.query,
    })

    axiosInstance.interceptors.request.use((config) => {
        const access_token = localStorage.getItem('access_token')
        const refresh_token = localStorage.getItem('refresh_token')
        const headers = {
            Authorization: '',
        }
        if (access_token) {
            headers.Authorization = `Bearer ${token ? token : access_token}`
        }

        if (['/refresh-token', '/revoke_token'].includes(config.url as string) && refresh_token) {
            config.headers['Authorization'] = 'Bearer ' + refresh_token
        } else if (access_token) {
            config.headers['Authorization'] = 'Bearer ' + access_token
        }
        return config
    })

    axiosInstance.interceptors.response.use(
        (config) => {
            return config
        },
        async (error) => {
            const access_token = localStorage.getItem('access_token')
            const refresh_token = localStorage.getItem('refresh_token')
            console.log('🚀 ~ file: useAPI.ts:117 ~ error:', error)
            let _errorResponse = error.response
            const originalRequest = error.config
            const status = _errorResponse.status
            const authStore = useAuthStore()
            const historyStore = useHistoryStore()
            const voiceLibraryStore = useVoiceLibraryStore()
            switch (status) {
                case 403:
                    if (['/refresh-token'].includes(originalRequest.url) || !refresh_token) {
                        authStore.setIsExpired(true)
                        authStore.logout()
                        return Promise.reject(error)
                    } else {
                        //retry with refrest token first
                        if (!originalRequest._retry) {
                            // save request if it is not refresh token request
                            if (isRefreshingToken) {
                                return new Promise(function (resolve, reject) {
                                    requestsPending.push(function () {
                                        resolve(axiosInstance(originalRequest))
                                    })
                                })
                            }
                            originalRequest._retry = true
                            isRefreshingToken = true

                            try {
                                var refreshTokenRes = await axiosInstance.post('/refresh-token', {
                                    refresh_token,
                                })

                                if (refreshTokenRes.status === 200) {
                                    localStorage.setItem('access_token', refreshTokenRes.data.access_token)
                                    localStorage.setItem('refresh_token', refreshTokenRes.data.refresh_token)
                                    // authStore.fetchUserInfo()
                                    // const route = useRoute()
                                    // const translation_uuid = route.query.id;
                                    // if (translation_uuid) {
                                    //   historyStore.fetchHistoryById(translation_uuid as string);
                                    // } else {
                                    //   historyStore.filterHistories();
                                    // }
                                    // voiceLibraryStore.fetchVoiceLibraryByType('user_voice' as string, true);

                                    originalRequest.headers['Authorization'] =
                                        'Bearer ' + refreshTokenRes.data.access_token
                                    return axiosInstance(originalRequest)
                                } else {
                                    authStore.setIsExpired(true)
                                    authStore.logout()
                                }
                            } catch (refreshError) {
                                isRefreshingToken = false
                                authStore.setIsExpired(true)
                                authStore.logout()
                            } finally {
                                isRefreshingToken = false
                                requestsPending.forEach((callback) => callback())
                                requestsPending = []
                            }
                        } else {
                            authStore.setIsExpired(true)
                            authStore.logout()
                        }
                    }
                    break
                case 401:
                    // authStore.setIsExpired(true)
                    // authStore.logout()
                    break
                default:
                    break
            }
            return Promise.reject(error)
        }
    )

    // return axiosInstance(request)
    // return like useFetch

    let data = { value: null, headers: {} }
    let error = { value: null as any }

    try {
        const response = await axiosInstance(request, {
            ...opts,
            data: opts.body,
        })
        data = {
            value: {
                ...response.data,
            },
            headers: response.headers,
        }
    } catch (e: any) {
        error.value = {
            statusCode: e.response?.status,
            data: e.response?.data,
        }
    }

    return { data, error }
}

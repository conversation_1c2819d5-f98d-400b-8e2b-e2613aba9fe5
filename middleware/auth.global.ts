export const PROTECTED_ROUTES = [
    'profile',
    'profile-buy-credits',
    'profile-change-plan',
    'profile-thank-you',
    'profile-payment-history',
    'profile-cancel-successful',
    'profile-remove-ads',
    'profile-gift-card',
    'profile-integration-api-keys',
    'profile-integration-webhook',
    'profile-integration-webhook-history',
    'profile-integration-api-reference',
]

export default defineNuxtRouteMiddleware(async (to, from) => {
    if (PROTECTED_ROUTES.includes(to.name as string)) {
        if (localStorage.getItem('access_token')) {
            return
        } else {
            return navigateTo('/signin')
        }
    }

    // if (['signin', 'signup'].includes(to.name as string)) {
    //     const authStore = useAuthStore()
    //     if (localStorage.getItem('access_token') && !authStore.user) {
    //         return navigateTo('/')
    //     }
    // }
})

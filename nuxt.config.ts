// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    ssr: false,
    target: 'static',
    components: [
        {
            path: '~/components',
            pathPrefix: false,
        },
        {
            path: '~/base-components',
            pathPrefix: false,
        },
    ],

    devtools: { enabled: false },
    extends: ['@nuxt/ui-pro'],
    modules: [
        '@pinia/nuxt',
        '@nuxt/ui',
        '@nuxtjs/i18n',
        '@pinia-plugin-persistedstate/nuxt',
        '@nuxtjs/supabase',
        '@nuxt/content',
        '@nuxt/fonts',
        '@nuxt/image',
        '@nuxthq/studio',
        '@stefanobartoletti/nuxt-social-share',
        'nuxt-svgo',
        '@unlok-co/nuxt-stripe',
        '@nuxtjs/device',
    ],
    stripe: {
        client: {
          key: process.env.NUXT_STRIPE_PUBLIC_KEY,
          // manualClientLoad: true, // if you want to have control where you are going to load the client
          // your api options override for stripe client side https://stripe.com/docs/js/initializing#init_stripe_js-options
          options: {},
        },
      },
    hooks: {
        // Define `@nuxt/ui` components as global to use them in `.md` (feel free to add those you need)
        'components:extend': (components) => {
            const globals = components.filter((c) => ['UButton', 'UIcon'].includes(c.pascalName))

            globals.forEach((c) => (c.global = true))
        },
    },

    build: { transpile: ['vue-number-animation'] },

    tailwindcss: {
        cssPath: '~/assets/css/input.css',
    },

    i18n: {
        vueI18n: './i18n.config.ts',
    },

    pinia: {
        storesDirs: ['./stores/**'],
    },

    routeRules: {
        '/profile/integration/': {
            redirect: '/profile/integration/api-keys',
        },
        'story-maker': {},
        home: {
            ssr: true,
        },
        '/api/search.json': { prerender: true },
    },

    app: {
        head: {
            title: 'OpenAI Text To Speech | Advanced Voice Engine Technology',
            viewport: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
            script: [
                {
                    children: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','${process.env.NUXT_GOOGLE_ANALYTICS_ID}');`,
                    tagPosition: 'head',
                },
                {
                    src: 'https://www.youtube.com/iframe_api',
                },
                // {
                //     src: 'https://cdn.ampproject.org/v0/amp-ad-0.1.js',
                //     async: true,
                //     "custom-element": "amp-ad"
                // }
                // {
                //     src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-4149002346699299',
                //     async: true,
                //     crossorigin: 'anonymous',
                //     tagPosition: 'bodyOpen',
                // },
                {
                    type: 'text/javascript',
                    src: 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js',
                },
                // {
                //     type: 'text/javascript',
                //     children: `(function() {var gs = document.createElement('script');gs.src = 'https://affiliate.ttsopenai.com/pr/js';gs.type = 'text/javascript';gs.async = 'true';gs.onload = gs.onreadystatechange = function() {var rs = this.readyState;if (rs && rs != 'complete' && rs != 'loaded') return;try {growsumo._initialize('pk_LIoBU0OE5moz3k7PQaN5v3qGAwZ42PDQ'); if (typeof(growsumoInit) === 'function') {growsumoInit();}} catch (e) {}};var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(gs, s);})();`,
                //     tagPosition: 'head',
                // }
            ],
            noscript: [
                {
                    innerHTML: `<iframe src="https://www.googletagmanager.com/ns.html?id=${process.env.NUXT_GOOGLE_ANALYTICS_ID}"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
                    tagPosition: 'bodyOpen',
                },
            ],

            link: [
                {
                    rel: 'stylesheet',
                    href: 'https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200',
                },
                {
                    rel: 'stylesheet',
                    href: 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css',
                },

                // <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
                // <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
                // <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
                // <link rel="manifest" href="/site.webmanifest">
                // <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">

                {
                    rel: 'apple-touch-icon',
                    sizes: '180x180',
                    href: '/apple-touch-icon.png',
                },
                {
                    rel: 'icon',
                    type: 'image/png',
                    sizes: '32x32',
                    href: '/favicon-32x32.png',
                },
                {
                    rel: 'icon',
                    type: 'image/png',
                    sizes: '16x16',
                    href: '/favicon-16x16.png',
                },
                {
                    rel: 'manifest',
                    href: '/site.webmanifest',
                },
                {
                    rel: 'mask-icon',
                    href: '/safari-pinned-tab.svg',
                    color: '#5bbad5',
                },
            ],
            meta: [
                {
                    name: 'msapplication-TileColor',
                    content: '#da532c',
                },
                {
                    name: 'theme-color',
                    content: '#ffffff',
                },
            ],
        },
    },

    runtimeConfig: {
        public: {
            baseURL: process.env.NUXT_API_BASE_URL || 'https://api-dev.Text To Speech OpenAI .com/api/v1/',
            webhookBaseURL: process.env.NUXT_WEBHOOK_API_BASE_URL || 'https://dev.ttsopenai.com/uapi/v1/',
            // noStreamTranslate: process.env.NUXT_NO_STREAM_TRANSLATION || false,
            NUXT_FIREBASE_API_KEY: process.env.NUXT_FIREBASE_API_KEY || '',
            NUXT_FIREBASE_AUTH_DOMAIN: process.env.NUXT_FIREBASE_AUTH_DOMAIN || '',
            NUXT_FIREBASE_DATABASE_URL: process.env.NUXT_FIREBASE_DATABASE_URL || '',
            NUXT_FIREBASE_PROJECT_ID: process.env.NUXT_FIREBASE_PROJECT_ID || '',
            NUXT_FIREBASE_STORAGE_BUCKET: process.env.NUXT_FIREBASE_STORAGE_BUCKET || '',
            NUXT_FIREBASE_MESSAGING_SENDER_ID: process.env.NUXT_FIREBASE_MESSAGING_SENDER_ID || '',
            NUXT_FIREBASE_APP_ID: process.env.NUXT_FIREBASE_APP_ID || '',
            NUXT_PAYMENT_MAINTENANCE: process.env.NUXT_PAYMENT_MAINTENANCE == 'true' || false,
            NUXT_PAYPAL_ID: process.env.NUXT_PAYPAL_ID || '',
            NUXT_RECAPCHA_V3_SITE_KEY:
                process.env.NUXT_RECAPCHA_V3_SITE_KEY || '6LeU33EnAAAAAGiKxemEFI878I2mcKtzHswbnxn8',
            NUXT_MODEL_GPT_4: process.env.NUXT_MODEL_GPT_4 == 'true' || false,
            NUXT_APP_VERSION: process.env.NUXT_APP_VERSION || '0.0.1',
            NUXT_SHOW_NOTIFICATION: process.env.NUXT_SHOW_NOTIFICATION == 'true' || false,
            NUXT_NOTIFICATION_TABLE: process.env.NUXT_NOTIFICATION_TABLE || 'notifications',
            NUXT_DISABLE_LOGIN_BY_SOCIAL: process.env.NUXT_DISABLE_LOGIN_BY_SOCIAL == 'true' || false,
            isBeta: process.env.NUXT_BETA_MODE == 'true' || false,
            enableAds: process.env.NUXT_ENABLE_ADS == 'true' || false,
            features: {
                userAPI: process.env.NUXT_FEATURE_USER_API == 'true' || false,
                storyMaker: process.env.NUXT_FEATURE_STORY_MAKER == 'true' || false,
                googlePay: process.env.NUXT_FEATURE_GOOGLE_PAY == 'true' || false,
                myVoice: process.env.NUXT_FEATURE_MY_VOICE == 'true' || false,
                cryptoPay: process.env.NUXT_FEATURE_CRYPTO_PAY == 'true' || false,
                appleLogin: process.env.NUXT_FEATURE_APPLE_LOGIN == 'true' || false,
                textInputPro: process.env.NUXT_FEATURE_TEXT_INPUT_PRO == 'true' || false,
                news: process.env.NUXT_FEATURE_NEWS == 'true' || false,
                referral: process.env.NUXT_FEATURE_REFERRAL == 'true' || false,
                userSettings: process.env.NUXT_FEATURE_USER_SETTINGS == 'true' || false,
                textToSpeechPro: process.env.NUXT_FEATURE_INPUT_TEXT_PRO == 'true' || false,
                stripePay: process.env.NUXT_FEATURE_STRIPE == 'true' || false,
                pajilyPay: process.env.NUXT_FEATURE_PAJILY_PAY == 'true' || false,
                accent: process.env.NUXT_FEATURE_ACCENT == 'true' || false,
                stopProcessing: process.env.NUXT_FEATURE_STOP_PROCESSING == 'true' || false,
                outputChannel: process.env.NUXT_FEATURE_OUTPUT_CHANNEL == 'true' || false,
            },
            NUXT_DETECT_ADS_BLOCK: process.env.NUXT_DETECT_ADS_BLOCK == 'true' || false,
            NUXT_TURNSTILE_SITE_KEY: process.env.NUXT_TURNSTILE_SITE_KEY || '0x4AAAAAAAhYSzhn2WiT2jfK',
            NUXT_APPLE_LOGIN_CLIENT_ID: process.env.NUXT_APPLE_LOGIN_CLIENT_ID || 'com.aivoice.dev',
            NUXT_APPLE_LOGIN_REDIRECT_URI:
                process.env.NUXT_APPLE_LOGIN_REDIRECT_URI || 'https://dev.ttsopenai.com/auth/apple',
            NUXT_DOCS_URL: process.env.NUXT_DOCS_URL || 'https://docs-dev.ttsopenai.com',
            NUXT_STRIPE_PUBLIC_KEY: process.env.NUXT_STRIPE_PUBLIC_KEY || '',
            NUXT_STRIPE_USER_ID_GREATER_THAN: process.env.NUXT_STRIPE_USER_ID_GREATER_THAN || 60000,
            limited: {
                maxLengthTextPremium: process.env.NUXT_MAX_LENGTH_TEXT_PREMIUM || 30000,
                maxLengthTextFree: process.env.NUXT_MAX_LENGTH_TEXT_FREE || 3000,
                maxLengthTextNonLogin: process.env.NUXT_MAX_LENGTH_TEXT_NON_LOGIN || 500,
                maxLengthEmotionTextPremium: process.env.NUXT_MAX_LENGTH_EMOTION_TEXT_PREMIUM || 10000,
                maxLengthEmotionTextFree: process.env.NUXT_MAX_LENGTH_EMOTION_TEXT_FREE || 3000,
                maxLengthEmotionTextNonLogin: process.env.NUXT_MAX_LENGTH_EMOTION_TEXT_NON_LOGIN || 500,
            },
            NUXT_GOOGLE_CLIENT_ID: process.env.NUXT_GOOGLE_CLIENT_ID || '309877442422-22t81sfdbm47ah0fi8e9ggedh0ih2jc8.apps.googleusercontent.com',
        },
    },

    css: ['vue-final-modal/style.css', '~/assets/css/style.css', '~/assets/css/global.scss'],

    supabase: {
        redirect: false,
        url: process.env.SUPABASE_URL || 'https://realtime-dev.ttsopenai.com',
        key:
            process.env.SUPABASE_KEY ||
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzI0MDAwNDAwLAogICJleHAiOiA0MDkwODMyNDQzCn0.2jwTXghgcYZRJz38_dDpIPIhRfHq1ZtgRHAHkxHpBcg',
    },

    compatibilityDate: '2024-10-30',

    socialShare: {
        baseUrl: 'https://ttsopenai.com',
    },
})

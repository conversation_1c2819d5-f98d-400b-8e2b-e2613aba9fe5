# Hướng dẫn kiểm soát Feature bằng cờ (Feature Flags)

## Tổng quan

Hệ thống feature flags cho phép bật/tắt các tính năng một cách linh hoạt thông qua biến môi trường mà không cần thay đổi code. Điều này đặc biệt hữu ích cho việc:

- <PERSON><PERSON><PERSON> soát việc phát hành tính năng mới
- A/B testing
- Rollback nhanh chóng khi có vấn đề
- Cho phép superuser truy cập tất cả tính năng

## 1. <PERSON><PERSON><PERSON> nghĩa cờ feature trong nuxt.config.ts

### Vị trí: `nuxt.config.ts` > `runtimeConfig.public.features`

```typescript
runtimeConfig: {
    public: {
        features: {
            userAPI: process.env.NUXT_FEATURE_USER_API == 'true' || false,
            storyMaker: process.env.NUXT_FEATURE_STORY_MAKER == 'true' || false,
            googlePay: process.env.NUXT_FEATURE_GOOGLE_PAY == 'true' || false,
            myVoice: process.env.NUXT_FEATURE_MY_VOICE == 'true' || false,
            cryptoPay: process.env.NUXT_FEATURE_CRYPTO_PAY == 'true' || false,
            appleLogin: process.env.NUXT_FEATURE_APPLE_LOGIN == 'true' || false,
            textInputPro: process.env.NUXT_FEATURE_TEXT_INPUT_PRO == 'true' || false,
            news: process.env.NUXT_FEATURE_NEWS == 'true' || false,
            referral: process.env.NUXT_FEATURE_REFERRAL == 'true' || false,
            userSettings: process.env.NUXT_FEATURE_USER_SETTINGS == 'true' || false,
            textToSpeechPro: process.env.NUXT_FEATURE_INPUT_TEXT_PRO == 'true' || false,
            stripePay: process.env.NUXT_FEATURE_STRIPE == 'true' || false,
            pajilyPay: process.env.NUXT_FEATURE_PAJILY_PAY == 'true' || false,
            accent: process.env.NUXT_FEATURE_ACCENT == 'true' || false,
            stopProcessing: process.env.NUXT_FEATURE_STOP_PROCESSING == 'true' || false,
        },
    },
}
```

### Thêm feature mới

Để thêm một feature mới, thêm dòng sau vào object `features`:

```typescript
newFeatureName: process.env.NUXT_FEATURE_NEW_FEATURE_NAME == 'true' || false,
```

## 2. Sử dụng cờ feature trong component/page

### Cách 1: Sử dụng trực tiếp trong template

```vue
<template>
  <div>
    <!-- Hiển thị feature chỉ khi flag được bật -->
    <div v-if="$config.public.features.storyMaker">
      <StoryMakerComponent />
    </div>

    <!-- Hiển thị thông báo khi feature bị tắt -->
    <div v-else>
      <p>Tính năng Story Maker hiện không khả dụng</p>
    </div>
  </div>
</template>
```

### Cách 2: Sử dụng trong script setup

```vue
<script setup>
const config = useRuntimeConfig()

// Kiểm tra feature flag
const isStoryMakerEnabled = config.public.features.storyMaker

// Sử dụng trong logic
if (isStoryMakerEnabled) {
  // Thực hiện logic cho feature
}
</script>
```

### Cách 3: Sử dụng computed property

```vue
<script setup>
const config = useRuntimeConfig()

const canUseStoryMaker = computed(() => {
  return config.public.features.storyMaker
})
</script>

<template>
  <div v-if="canUseStoryMaker">
    <StoryMakerComponent />
  </div>
</template>
```

## 3. Kiểm soát với SuperUser

### Logic kiểm soát: Feature Flag OR SuperUser

Đối với các tính năng được kiểm soát bởi feature flags, logic nên là:
**Cho phép truy cập nếu: (Feature Flag được bật) HOẶC (User là SuperUser)**

### Cách triển khai

#### Trong component:

```vue
<script setup>
const config = useRuntimeConfig()
const authStore = useAuthStore()

const canUseFeature = computed(() => {
  // Cho phép nếu feature flag được bật HOẶC user là superuser
  return config.public.features.featureName || authStore.isSuperUser
})
</script>

<template>
  <div v-if="canUseFeature">
    <FeatureComponent />
  </div>
</template>
```

#### Trong store/composable:

```typescript
export const useFeatureAccess = () => {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()

  const canAccessFeature = (featureName: string) => {
    const featureEnabled = config.public.features[featureName]
    const isSuperUser = authStore.isSuperUser

    return featureEnabled || isSuperUser
  }

  return {
    canAccessFeature
  }
}
```

#### Sử dụng composable:

```vue
<script setup>
const { canAccessFeature } = useFeatureAccess()

const canUseStoryMaker = computed(() => {
  return canAccessFeature('storyMaker')
})
</script>
```

## 4. Ví dụ thực tế

### Ví dụ 1: Kiểm soát hiển thị menu

```vue
<template>
  <nav>
    <NuxtLink to="/">Home</NuxtLink>

    <!-- Chỉ hiển thị khi feature được bật hoặc user là superuser -->
    <NuxtLink
      v-if="canUseStoryMaker"
      to="/story-maker"
    >
      Story Maker
    </NuxtLink>

    <NuxtLink
      v-if="canUseMyVoice"
      to="/my-voice"
    >
      My Voice
    </NuxtLink>
  </nav>
</template>

<script setup>
const config = useRuntimeConfig()
const authStore = useAuthStore()

const canUseStoryMaker = computed(() => {
  return config.public.features.storyMaker || authStore.isSuperUser
})

const canUseMyVoice = computed(() => {
  return config.public.features.myVoice || authStore.isSuperUser
})
</script>
```

### Ví dụ 2: Kiểm soát trong middleware

```typescript
// middleware/feature-check.ts
export default defineNuxtRouteMiddleware((to) => {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()

  // Kiểm tra feature cho route story-maker
  if (to.path.startsWith('/story-maker')) {
    const canAccess = config.public.features.storyMaker || authStore.isSuperUser

    if (!canAccess) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Feature not available'
      })
    }
  }
})
```

### Ví dụ 3: Kiểm soát trong API call

```typescript
// composables/useStoryAPI.ts
export const useStoryAPI = () => {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()

  const createStory = async (data: any) => {
    // Kiểm tra quyền truy cập trước khi gọi API
    const canAccess = config.public.features.storyMaker || authStore.isSuperUser

    if (!canAccess) {
      throw new Error('Feature not available')
    }

    // Thực hiện API call
    return await $fetch('/api/stories', {
      method: 'POST',
      body: data
    })
  }

  return {
    createStory
  }
}
```

## 5. Best Practices

### 1. Naming Convention
- Sử dụng camelCase cho tên feature: `storyMaker`, `myVoice`
- Biến môi trường sử dụng UPPER_SNAKE_CASE: `NUXT_FEATURE_STORY_MAKER`

### 2. Default Values
- Luôn set default value là `false` để đảm bảo tính năng bị tắt mặc định
- Chỉ bật khi có biến môi trường rõ ràng

### 3. Documentation
- Luôn document các feature flags mới
- Ghi rõ mục đích và cách sử dụng

### 4. Testing
- Test cả trường hợp feature bật và tắt
- Test với superuser và user thường

### 5. Cleanup
- Định kỳ review và xóa các feature flags không còn cần thiết
- Khi feature đã stable, có thể xóa flag và để feature luôn bật

## 6. Biến môi trường

Để bật/tắt feature, set biến môi trường tương ứng:

```bash
# Bật feature
NUXT_FEATURE_STORY_MAKER=true

# Tắt feature (hoặc không set biến)
NUXT_FEATURE_STORY_MAKER=false
# hoặc
# NUXT_FEATURE_STORY_MAKER=
```

## 7. Troubleshooting

### Feature không hoạt động dù đã bật flag
1. Kiểm tra biến môi trường đã được set đúng
2. Restart server sau khi thay đổi biến môi trường
3. Kiểm tra logic trong component có đúng không

### SuperUser không thể truy cập feature
1. Kiểm tra `authStore.isSuperUser` có trả về đúng không
2. Đảm bảo logic OR (`||`) được sử dụng đúng cách

### Feature hiển thị cho user thường dù flag bị tắt
1. Kiểm tra logic điều kiện trong template/script
2. Đảm bảo không có logic khác override feature flag

## 8. Ví dụ hoàn chỉnh: Tạo feature mới

### Bước 1: Thêm feature flag vào config

```typescript
// nuxt.config.ts
runtimeConfig: {
    public: {
        features: {
            // ... existing features
            newAwesomeFeature: process.env.NUXT_FEATURE_NEW_AWESOME_FEATURE == 'true' || false,
        },
    },
}
```

### Bước 2: Tạo composable để kiểm tra quyền truy cập

```typescript
// composables/useNewAwesomeFeature.ts
export const useNewAwesomeFeature = () => {
  const config = useRuntimeConfig()
  const authStore = useAuthStore()

  const canUseNewAwesomeFeature = computed(() => {
    return config.public.features.newAwesomeFeature || authStore.isSuperUser
  })

  const checkAccess = () => {
    if (!canUseNewAwesomeFeature.value) {
      throw createError({
        statusCode: 403,
        statusMessage: 'New Awesome Feature is not available'
      })
    }
  }

  return {
    canUseNewAwesomeFeature,
    checkAccess
  }
}
```

### Bước 3: Sử dụng trong component

```vue
<!-- components/NewAwesomeFeature.vue -->
<template>
  <div v-if="canUseNewAwesomeFeature">
    <h2>New Awesome Feature</h2>
    <button @click="handleAwesomeAction">
      Do Something Awesome
    </button>
  </div>
  <div v-else>
    <p>This feature is not available yet.</p>
  </div>
</template>

<script setup>
const { canUseNewAwesomeFeature, checkAccess } = useNewAwesomeFeature()

const handleAwesomeAction = () => {
  try {
    checkAccess()
    // Thực hiện logic của feature
    console.log('Awesome action executed!')
  } catch (error) {
    console.error('Access denied:', error.message)
  }
}
</script>
```

### Bước 4: Thêm vào navigation (nếu cần)

```vue
<!-- components/Navigation.vue -->
<template>
  <nav>
    <!-- ... existing nav items -->
    <NuxtLink
      v-if="canUseNewAwesomeFeature"
      to="/new-awesome-feature"
    >
      New Awesome Feature
    </NuxtLink>
  </nav>
</template>

<script setup>
const { canUseNewAwesomeFeature } = useNewAwesomeFeature()
</script>
```

### Bước 5: Tạo page với middleware protection

```vue
<!-- pages/new-awesome-feature.vue -->
<template>
  <div>
    <h1>New Awesome Feature Page</h1>
    <NewAwesomeFeature />
  </div>
</template>

<script setup>
// Middleware để bảo vệ route
definePageMeta({
  middleware: 'new-awesome-feature-access'
})
</script>
```

```typescript
// middleware/new-awesome-feature-access.ts
export default defineNuxtRouteMiddleware(() => {
  const { canUseNewAwesomeFeature } = useNewAwesomeFeature()

  if (!canUseNewAwesomeFeature.value) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Page not found'
    })
  }
})
```

## 9. Testing Feature Flags

### Unit Test Example

```typescript
// tests/useNewAwesomeFeature.test.ts
import { describe, it, expect, vi } from 'vitest'
import { useNewAwesomeFeature } from '~/composables/useNewAwesomeFeature'

// Mock dependencies
vi.mock('#app', () => ({
  useRuntimeConfig: () => ({
    public: {
      features: {
        newAwesomeFeature: false // Test với feature tắt
      }
    }
  })
}))

vi.mock('~/stores/auth', () => ({
  useAuthStore: () => ({
    isSuperUser: false // Test với user thường
  })
}))

describe('useNewAwesomeFeature', () => {
  it('should deny access when feature is disabled and user is not superuser', () => {
    const { canUseNewAwesomeFeature } = useNewAwesomeFeature()
    expect(canUseNewAwesomeFeature.value).toBe(false)
  })

  it('should allow access for superuser even when feature is disabled', () => {
    // Override mock for this test
    vi.mocked(useAuthStore).mockReturnValue({
      isSuperUser: true
    })

    const { canUseNewAwesomeFeature } = useNewAwesomeFeature()
    expect(canUseNewAwesomeFeature.value).toBe(true)
  })
})
```

## 10. Monitoring và Analytics

### Tracking feature usage

```typescript
// composables/useFeatureTracking.ts
export const useFeatureTracking = () => {
  const trackFeatureUsage = (featureName: string, action: string) => {
    // Gửi analytics event
    if (typeof gtag !== 'undefined') {
      gtag('event', 'feature_usage', {
        feature_name: featureName,
        action: action,
        user_type: authStore.isSuperUser ? 'superuser' : 'regular'
      })
    }
  }

  return {
    trackFeatureUsage
  }
}
```

### Sử dụng tracking

```vue
<script setup>
const { trackFeatureUsage } = useFeatureTracking()
const { canUseNewAwesomeFeature } = useNewAwesomeFeature()

const handleAwesomeAction = () => {
  if (canUseNewAwesomeFeature.value) {
    trackFeatureUsage('newAwesomeFeature', 'button_click')
    // Thực hiện action
  }
}
</script>
```

---

## Tóm tắt

1. **Định nghĩa**: Thêm feature flag vào `nuxt.config.ts`
2. **Sử dụng**: Kiểm tra flag trong component với logic `flag || isSuperUser`
3. **Bảo vệ**: Sử dụng middleware để bảo vệ routes
4. **Test**: Viết test cho cả trường hợp bật/tắt feature
5. **Monitor**: Theo dõi usage để quyết định khi nào stable feature

Hệ thống feature flags giúp team phát triển linh hoạt trong việc release và kiểm soát tính năng, đồng thời đảm bảo superuser luôn có quyền truy cập đầy đủ để test và debug.

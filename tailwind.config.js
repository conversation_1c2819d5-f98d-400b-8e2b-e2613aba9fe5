const defaultTheme = require('tailwindcss/defaultTheme')

/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        './components/**/*.{js,vue,ts}',
        './layouts/**/*.vue',
        './pages/**/*.vue',
        './plugins/**/*.{js,ts}',
        './nuxt.config.{js,ts}',
        './node_modules/flowbite.{js,ts}',
        './node_modules/flowbite-vue/**/*.{js,jsx,ts,tsx}',
        './node_modules/flowbite/**/*.{js,jsx,ts,tsx}',
    ],
    theme: {
        screens: {
            ipad: { 'max': '1105px' },
            ...defaultTheme.screens,
        },
        extend: {
            colors: {
                primary: {
                    50: '#f3faf8',
                    100: '#d7f0ea',
                    200: '#afe0d5',
                    300: '#7ecabb',
                    400: '#52ac9d',
                    500: '#3a9284',
                    600: '#2c756b',
                    700: '#275e58',
                    800: '#234c47',
                    900: '#21403d',
                    950: '#0e2524',
                },
                chatgpt: {
                    50: '#f3faf8',
                    100: '#d7f0ea',
                    200: '#afe0d5',
                    300: '#7ecabb',
                    400: '#52ac9d',
                    500: '#3a9284',
                    600: '#2c756b',
                    700: '#275e58',
                    800: '#234c47',
                    900: '#21403d',
                    950: '#0e2524',
                },
            },
        },
        fontFamily: {
            body: [
                'Inter',
                'ui-sans-serif',
                'system-ui',
                '-apple-system',
                'system-ui',
                'Segoe UI',
                'Roboto',
                'Helvetica Neue',
                'Arial',
                'Noto Sans',
                'sans-serif',
                'Apple Color Emoji',
                'Segoe UI Emoji',
                'Segoe UI Symbol',
                'Noto Color Emoji',
            ],
            sans: [
                'Inter',
                'ui-sans-serif',
                'system-ui',
                '-apple-system',
                'system-ui',
                'Segoe UI',
                'Roboto',
                'Helvetica Neue',
                'Arial',
                'Noto Sans',
                'sans-serif',
                'Apple Color Emoji',
                'Segoe UI Emoji',
                'Segoe UI Symbol',
                'Noto Color Emoji',
            ],
            logo: ["logo", "sans-serif"],
        },
    },
    // plugins: [require('flowbite/plugin'), require('@tailwindcss/line-clamp')],
    // As of Tailwind CSS v3.3, the `@tailwindcss/line-clamp` plugin is now included by default.
    plugins: [require('flowbite/plugin'), require('tailwind-scrollbar')],
    darkMode: 'class',
    safelist: ['max-w-md', 'max-w-4xl', 'top-3', 'right-2.5', 'bg-red-600', 'hover:bg-gray-100', { pattern: /(bg|text|border)-./ }, 'dark:bg-red-600', 'max-h-full', 'h-full', 'right-0', 'left-0', 'fixed', 'w-full', 'mx-auto'],
}

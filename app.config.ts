export default defineAppConfig({
    ui: {
        gray: 'slate',
        icons: {
            dynamic: true,
        },
        button: {
            rounded: 'rounded-full',
            default: {
                size: 'md',
                color: 'primary',
            },
        },
        input: {
            rounded: 'rounded-full',
        },
        header: {
            wrapper: 'lg:!border-transparent bg-gray-50 dark:bg-slate-950/95',
            links: {
                wrapper:
                    'ring-1 ring-gray-200 dark:ring-gray-800 px-3 gap-x-0 rounded-full bg-white dark:bg-gray-900 shadow-sm hover:shadow-xl transition-transform duration-200',
                base: 'py-2 px-4 font-medium transition-colors relative after:absolute after:-bottom-px after:inset-x-2 after:h-px after:rounded-full after:opacity-0 after:bg-primary-600 dark:after:bg-white after:transition-opacity',
                active: 'text-primary-600 dark:text-white after:opacity-100',
                inactive: 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200',
            },
        },
        footer: {
            top: {
                wrapper: 'border-t border-gray-200 dark:border-gray-800',
                container: 'py-8 lg:py-16',
            },
            bottom: {
                wrapper: 'border-t border-gray-200 dark:border-gray-800',
            },
        },
        pricing: {
            card: {
                highlight: 'ring-gray-900 dark:ring-white',
                features: {
                    item: {
                        icon: {
                            base: 'text-gray-900 dark:text-white',
                        },
                    },
                },
            },
        },
        horizontalNavigation: {
            icon: {
                base: 'relative',
                inactive: 'text-gray-500 dark:text-gray-400 !group-hover:text-gray-600 !dark:group-hover:text-gray-200',
            },
            inactive: 'text-gray-500 dark:text-gray-400 !hover:text-gray-600 !dark:hover:text-white',
        },
    },
    header: {
        search: true,
        colorMode: true,
        links: [
            {
                icon: 'i-simple-icons-github',
                to: 'https://github.com/nuxt-ui-pro/docs',
                target: '_blank',
                'aria-label': 'Docs template on GitHub',
            },
        ],
    },
    footer: {
        credits: 'Copyright © ' + new Date().getFullYear(),
        colorMode: false,
        links: [
            {
                icon: 'i-simple-icons-nuxtdotjs',
                to: 'https://nuxt.com',
                target: '_blank',
                'aria-label': 'Nuxt Website',
            },
            {
                icon: 'i-simple-icons-discord',
                to: 'https://discord.com/invite/ps2h6QT',
                target: '_blank',
                'aria-label': 'Nuxt UI on Discord',
            },
            {
                icon: 'i-simple-icons-x',
                to: 'https://x.com/nuxt_js',
                target: '_blank',
                'aria-label': 'Nuxt on X',
            },
            {
                icon: 'i-simple-icons-github',
                to: 'https://github.com/nuxt/ui',
                target: '_blank',
                'aria-label': 'Nuxt UI on GitHub',
            },
        ],
    },
    toc: {
        title: 'Table of Contents',
        bottom: {
            title: 'Community',
            edit: 'https://github.com/nuxt-ui-pro/docs/edit/main/content',
            links: [
                {
                    icon: 'i-openmoji-youtube',
                    label: 'Follow on YouTube',
                    to: 'https://www.youtube.com/@TTSOPENAI86',
                    target: '_blank',
                },
                {
                    icon: 'i-openmoji-facebook',
                    label: 'Follow on Facebook',
                    to: 'https://www.facebook.com/profile.php?id=61556251485523',
                    target: '_blank',
                },
                {
                    icon: 'i-mingcute-voice-fill',
                    label: 'Purchase Premium',
                    to: 'https://ttsopenai.com/pricing',
                    target: '_blank',
                },
            ],
        },
    },
    page: {
        hero: {
            wrapper: 'lg:py-24',
        },
    },
})

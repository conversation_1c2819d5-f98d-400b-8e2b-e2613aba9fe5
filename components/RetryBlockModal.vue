<script setup lang="ts">
import { cloneDeep } from 'lodash'
import { ref } from 'vue'
import { object, string, number, type InferType } from 'yup'
import type { FormError, FormSubmitEvent } from '#ui/types'

const historyStore = useHistoryStore()
const { isShowRetryBlockModal, selectedBlock, loadingBtnRetry, errors, isRetryInputPro } = storeToRefs(historyStore)
const { onRetryBlock } = historyStore
const voiceLibraryStore = useVoiceLibraryStore()
const { getAllVoices, voiceLibrariesAll, openAIVoicesPro, openAIVoices } = storeToRefs(voiceLibraryStore)
const runtimeConfig = useRuntimeConfig()
const storyStore = useStoryStore()
const { stories, storiesElm, activeStory, showVoiceLibrariesModal } = storeToRefs(storyStore)
const authStore = useAuthStore()
const { isSuperUser } = storeToRefs(authStore)

const stateBlock = ref(cloneDeep(selectedBlock.value))

const { t } = useI18n()

const schema = object({
    // name: string().required(t('Name is required')),
    input: string().required(t('Input is required')),
    voice_id: string().required(t('Voice is required')),
    speed: number()
        .required(t('Speed is required'))
        .min(0.25, t('Speed must be greater than 0.25'))
        .max(4, t('Speed must be less than 4')),
})

type Schema = InferType<typeof schema>
const onRetry = async (event: FormSubmitEvent<Schema>) => {
    if (errorMessage.value) return
    const toast = useToast()
    const result = await onRetryBlock(stateBlock.value)
    if (result) {
        isShowRetryBlockModal.value = false
        toast.add({
            title: t('Block retry'),
            description: t('Block retry success'),
            color: 'green',
        })
    } else {
        let error = 'Block retry failed'
        if (errors.value?.onRetryBlock === 'NOT_ALLOW_PLAN_ID') {
            error = 'Free plan is not allowed to retry block'
        } else if (errors.value?.onRetryBlock === 'RETRY_BLOCK_PAUSE_DATA_INVALID') {
            error = 'Retry block data include pause invalid'
        }
        toast.add({
            title: t('Block retry'),
            description: t(error),
            color: 'red',
        })
    }
}

const animation = ref(false)
const currentVoiceId = ref(stateBlock.value.voice_id)

const voice = computed(() => {
    currentVoiceId.value = stateBlock.value.voice_id
    return getAllVoices.value.find((voice: any) => voice.id === currentVoiceId.value) as Record<string, any>
})

const onOpenVoiceLibraryModal = (voice_id: string) => {
    const voice = getAllVoices.value.find((voice: any) => voice.id === voice_id) as Record<string, any>
    storyStore.setActiveStory(voice)
    showVoiceLibrariesModal.value = true
}

const onSelectVoice = (voice_id: string) => {
    const voice = getAllVoices.value.find((voice: any) => voice.id === voice_id) as Record<string, any>
    storyStore.setActiveStory(voice)
    stateBlock.value.voice_id = voice_id
    currentVoiceId.value = voice_id
    showVoiceLibrariesModal.value = false
}

const onChangeSpeed = (speed: number) => {
    stateBlock.value.speed = speed
}

const errorMessage = ref('')

const onError = (error: string) => {
    errorMessage.value = error
}

const maxLengthText = ref(0)
const showAccentSelect = ref(selectedBlock.value.accent ? true : false)

watch(isShowRetryBlockModal, () => {
    if (isShowRetryBlockModal.value) {
        stateBlock.value = cloneDeep(selectedBlock.value)
        const max = stateBlock.value.input.length + 200
        if (isRetryInputPro.value) {
            maxLengthText.value = max > 5500 ? 5500 : max
        } else {
            maxLengthText.value = max > 4050 ? 4050 : max
        }

        if (isRetryInputPro.value && voiceLibrariesAll.value) {
            voiceLibrariesAll.value.openai_voice = openAIVoicesPro.value.concat(openAIVoices.value as any)
        }

        showAccentSelect.value = stateBlock.value.accent ? true : false
    } else {
        errors.value = {
            onRetryBlock: null,
        }
        if (voiceLibrariesAll.value) {
            voiceLibrariesAll.value.openai_voice = openAIVoices.value as any
        }

        showAccentSelect.value = false
    }
})

const { voiceTypes } = useVoiceLibrary(t)
onMounted(() => {
    voiceTypes.forEach((type) => {
        voiceLibraryStore.fetchVoiceLibraryByType(type.value as string, false)
    })
})

const onAccentChange = (accent: string) => {
    stateBlock.value.accent = accent
}
</script>

<template>
    <UModal
        v-model="isShowRetryBlockModal"
        prevent-close
        :ui="{
            width: 'sm:max-w-xl',
        }"
    >
        <UForm :state="stateBlock" :schema="schema" @submit="onRetry">
            <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
                <template #header>
                    <div class="flex items-center justify-between">
                        <h3 class="text-base font-semibold leading-6 text-gray-9000 dark:text-white">
                            {{ $t('Block Retry') }}
                        </h3>
                        <UButton
                            color="gray"
                            variant="ghost"
                            icon="i-heroicons-x-mark-20-solid"
                            class="-my-1"
                            @click="isShowRetryBlockModal = false"
                        />
                    </div>
                </template>
                <div class="space-y-4">
                    <UFormGroup size="lg" :label="$t('Name')" class="mb-3" name="name">
                        <div class="relative">
                            <UInput
                                :placeholder="$t('Enter name here')"
                                v-model="stateBlock.name"
                                :padded="true"
                                :ui="{
                                    rounded: 'rounded-md',
                                }"
                                :maxlength="50"
                            />
                        </div>
                    </UFormGroup>
                    <UFormGroup
                        size="lg"
                        :label="$t('Input')"
                        required
                        class="mb-3"
                        name="input"
                        :hint="`${stateBlock.input.length}/${maxLengthText}`"
                    >
                        <div class="relative">
                            <UTextarea
                                :placeholder="$t('Enter input here')"
                                v-model="stateBlock.input"
                                :padded="true"
                                :ui="{
                                    rounded: 'rounded-md',
                                }"
                                :maxlength="maxLengthText"
                                resize
                            />
                        </div>
                    </UFormGroup>
                    <div class="flex flex-row gap-4">
                        <UFormGroup class="basis-1/2" size="lg" :label="$t('Voice')" required name="voice">
                            <div class="relative">
                                <BaseVoiceBadge
                                    class="min-w-32 w-full animate__animated"
                                    :class="{
                                        animate__bounceInRight: animation,
                                    }"
                                    :voice_id="currentVoiceId"
                                    :voice="voice"
                                    @click="onOpenVoiceLibraryModal(stateBlock.voice_id)"
                                />
                            </div>
                        </UFormGroup>
                    </div>
                    <div v-if="showAccentSelect && (runtimeConfig.public.features.accent || isSuperUser)" class="my-auto">
                        <BaseAccentSelect :value="stateBlock.accent" @update:accent="onAccentChange" />
                    </div>
                    <SliderSpeed
                        size="lg"
                        class="basis-1/2 my-auto"
                        :modelValue="stateBlock.speed"
                        @onChangeSpeed="onChangeSpeed"
                        @onError="onError"
                    />
                    <div v-if="errorMessage" class="text-red-500 text-sm text-end">
                        {{ errorMessage }}
                    </div>
                    <br />
                    <UAlert
                        v-if="true"
                        :title="$t('Info')"
                        :description="$t('We will deduct 50% of the credit for each retry.')"
                        color="yellow"
                        variant="subtle"
                    />
                    <UAlert
                        v-if="
                            ['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(errors['onRetryBlock'] || '')
                        "
                        icon="i-heroicons-information-circle-solid"
                        color="red"
                        variant="soft"
                        :title="$t('Error')"
                        :description="$t(errors['onRetryBlock'] || '')"
                        :actions="[
                            {
                                label: $t('Buy credits'),
                                variant: 'solid',
                                color: 'red',
                                click: () => navigateTo('/profile/buy-credits'),
                            },
                        ]"
                    />
                </div>

                <template #footer>
                    <div class="flex flex-row gap-2 justify-end">
                        <UButton color="white" @click="isShowRetryBlockModal = false" class="px-6">
                            {{ t('Cancel') }}
                        </UButton>
                        <!-- Icon next -->
                        <UButton
                            icon="i-heroicons-arrow-right"
                            :trailing="true"
                            :ui="{
                                inline: 'md:justify-between justify-center',
                                icon: {
                                    size: {
                                        xl: 'w-14 h-14 md:w-6 md:h-6',
                                    },
                                },
                            }"
                            color="primary"
                            class="px-6"
                            type="submit"
                            :loading="loadingBtnRetry"
                            :disabled="!!errorMessage"
                        >
                            {{ t('Retry') }}
                        </UButton>
                    </div>
                </template>
            </UCard>
        </UForm>
    </UModal>
    <VoiceLibraryModal @select="onSelectVoice" />
</template>

<template>
    <div>
        <div class="relative content">
            <div class="mx-auto max-w-7xl">
                <div class="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
                    <h2 class="block font-sans text-5xl font-bold text-black mb-6">
                        {{ basic.title }}
                    </h2>
                    <p class="block font-serif text-xl font-normal text-gray-700">
                        {{ basic.description }}
                        <a class="text-black hover:text-gray-700" :href="basic.link.url">
                            {{ basic.link.title }}
                            <span v-html="basic.link.icon"></span>
                        </a>
                    </p>
                </div>
            </div>
            <div class="relative mx-auto max-w-7xl pt-16">
                <div class="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
                    <div
                        class="group relative flex justify-end items-center rounded-xl bg-gray-900/5 ring-1 ring-inset ring-gray-900/10 lg:-m-4 lg:rounded-2xl"
                    >
                        <video
                            class="rounded-xl cursor-pointer"
                            width="100%"
                            height="100%"
                            autoplay
                            loop
                            muted
                            playsinline
                        >
                            <source :src="generateVideoPath(content.video_src)" type="video/mp4" />
                            <span class="block font-serif text-base font-normal text-gray-500">{{
                                content.video_tag
                            }}</span>
                        </video>
                        <div
                            class="absolute cursor-pointer bottom-4 md:bottom-8 left-4 md:left-8 flex items-center p-3 bg-black rounded-full"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                aria-hidden="true"
                                class="h-4 md:h-8 text-white"
                            >
                                <path
                                    d="M9.547 3.062A.75.75 0 0110 3.75v12.5a.75.75 0 01-1.264.546L4.703 13H3.167a.75.75 0 01-.7-.48A6.985 6.985 0 012 10c0-.887.165-1.737.468-2.52a.75.75 0 01.7-.48h1.535l4.033-3.796a.75.75 0 01.811-.142zM13.28 7.22a.75.75 0 10-1.06 1.06L13.94 10l-1.72 1.72a.75.75 0 001.06 1.06L15 11.06l1.72 1.72a.75.75 0 101.06-1.06L16.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L15 8.94l-1.72-1.72z"
                                ></path></svg
                            ><span
                                class="text-black text-sm leading-none transition-opacity duration-300 ease-in-out"
                            ></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mx-auto mt-16 max-w-7xl sm:mt-20 md:mt-24">
                <div
                    class="mx-auto grid max-w-2xl grid-cols-1 gap-x-6 gap-y-10 text-base leading-7 text-gray-600 sm:grid-cols-2 lg:mx-0 lg:max-w-none lg:grid-cols-3 lg:gap-x-8 lg:gap-y-16"
                >
                    <div v-for="item in content.introduction" class="relative pl-9">
                        <h3
                            class="block font-serif text-lg font-normal text-gray-700 inline font-semibold text-gray-900"
                        >
                            <span v-html="item.icon"></span>
                            {{ item.title }}
                        </h3>
                        <p class="block font-serif text-lg font-normal text-gray-700 inline">
                            {{ item.description }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentLongFormVoiceGenerationBlock } from '~/types'
import { generateVideoPath } from '~/utils/video'

const props = defineProps<iContentLongFormVoiceGenerationBlock>()
</script>

<template>
    <div>
        <div class="relative content">
            <div class="max-w-7xl mx-auto mb-12">
                <div class="mx-auto pb-6 md:pb-12">
                    <h2 class="block font-sans text-5xl font-bold text-black font-bold">
                        {{ basic.title }}
                    </h2>
                    <p class="block font-serif text-xl font-normal text-gray-700 mt-6">
                        {{ basic.description }}
                    </p>
                </div>
                <div class="relative pb-12 md:pb-20">
                    <div class="grid md:grid-cols-12 gap-6 group">
                        <div v-for="item in content.card" class="md:col-span-6" data-aos="fade-down">
                            <div class="relative h-full shadow-xl rounded-3xl z-20 overflow-hidden bg-custom">
                                <div class="flex flex-col">
                                    <div
                                        class="md:max-w-[480px] shrink-0 order-1 md:order-none p-6 pt-0 md:p-8 md:pr-0"
                                    >
                                        <div class="mb-5">
                                            <div>
                                                <h3
                                                    class="block font-sans text-2xl font-bold text-white tracking-tight"
                                                >
                                                    {{ item.title }}
                                                </h3>
                                                <p
                                                    class="block font-serif text-lg font-normal text-gray-500 mt-4 leading-8"
                                                >
                                                    {{ item.description }}
                                                </p>
                                            </div>
                                        </div>
                                        <div>
                                            <a
                                                class="whitespace-nowrap text-bold text-lg text-white hover:text-gray-200"
                                                :href="item.link.url"
                                                >{{ item.link.title }}
                                                <span v-html="item.link.icon"></span>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="relative w-full h-64 md:h-auto overflow-hidden">
                                        <img
                                            alt="Feature 01"
                                            loading="lazy"
                                            width="200"
                                            height="200"
                                            decoding="async"
                                            data-nimg="1"
                                            class="mx-auto mb-12"
                                            style="color: transparent"
                                            :src="generateImgPath(item.image)"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid md:grid-cols-3 gap-8 md:gap-12">
                    <div v-for="item in content.introduction" class="relative pl-9">
                        <h3
                            class="block font-serif text-lg font-normal text-gray-700 inline font-semibold text-gray-900"
                        >
                            <span v-html="item.icon"></span>
                            {{ item.title }}
                        </h3>
                        <p class="block font-serif text-lg font-normal text-gray-700 inline">
                            {{ item.description }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentAiVoiceLabBlock } from '~/types'
import { generateImgPath } from '~/utils/image'

const props = defineProps<iContentAiVoiceLabBlock>()
</script>

<style scoped>
.bg-custom {
    background-color: #312b41;
}
</style>

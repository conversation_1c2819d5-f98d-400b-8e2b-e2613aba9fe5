<template>
    <div>
        <div class="content my-36">
            <div class="max-w-7xl mx-auto">
                <div class="">
                    <h2 class="block font-sans text-5xl font-bold text-black mb-6">
                        {{ basic.title }}
                    </h2>
                    <span class="block font-serif text-xl font-normal text-gray-700">
                        {{ basic.description }}
                    </span>
                </div>
                <div
                    class="mx-auto mt-6 grid max-w-2xl auto-rows-fr grid-cols-1 gap-8 sm:mt-12 lg:mx-0 lg:max-w-none lg:grid-cols-3"
                >
                    <article
                        v-for="item in content"
                        :key="item.url"
                        class="relative shadow-md hover:shadow-lg isolate flex flex-col justify-end overflow-hidden rounded-2xl bg-gray-900 px-8 pb-8 pt-40 sm:pt-28 lg:pt-40"
                    >
                        <a :href="item.url">
                            <img
                                alt=""
                                loading="lazy"
                                width="774"
                                height="1050"
                                decoding="async"
                                data-nimg="1"
                                class="absolute inset-0 -z-10 h-full w-full object-cover"
                                style="color: transparent"
                                :src="generateImgPath(item.image)"
                            />
                            <div class="absolute inset-0 -z-10 bg-gradient-to-t from-gray-900 via-gray-900/40"></div>
                            <div class="absolute inset-0 -z-10 rounded-2xl ring-1 ring-inset ring-gray-900/10"></div>
                            <span class="block font-serif text-sm font-light text-gray-500 mr-8">
                                {{ item.date_at }}
                            </span>
                            <h3 class="block font-sans text-4xl font-bold text-white mb-3 mt-2">
                                {{ item.title }}
                            </h3>
                            <span class="block font-serif text-base font-normal text-gray-500">
                                {{ item.description }}
                            </span>
                        </a>
                    </article>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentPoweredCutResearchBlock } from '~/types'
import { generateImgPath } from '~/utils/image'

const props = defineProps<iContentPoweredCutResearchBlock>()
</script>

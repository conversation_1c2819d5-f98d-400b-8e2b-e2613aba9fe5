<template>
    <div>
        <div class="content">
            <div class="max-w-7xl mx-auto">
                <div class="flex flex-col align-stretch items-stretch mb-48">
                    <div class="sm:text-left mb-3 md:mb-6 lg:mb-12">
                        <h2 class="block font-sans text-5xl font-bold text-black mb-6">
                            {{ basic.title }}
                        </h2>
                        <span class="block font-serif text-xl font-normal text-gray-700 mb-3">
                            {{ basic.description }}
                            <a class="text-black" :href="basic.link.url">
                                {{ basic.link.title }}
                                <span v-html="basic.link.icon"></span>
                            </a>
                        </span>
                    </div>
                    <div
                        class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 sm:gap-12 2xl:grid-cols-5"
                    >
                        <template v-for="item in content">
                            <a :href="item.link.url">
                                <div tabindex="0">
                                    <div
                                        class="flex items-center justify-between h-16 p-3 rounded-md border border-3 border-gray-200 bg-white"
                                    >
                                        <div
                                            class="relative cursor-pointer flex items-center justify-center"
                                            style="height: 35px"
                                        >
                                            <div class="rounded-md max-w-xs object-cover">
                                                <img
                                                    class="rounded-md max-w-xs object-cover border border-gray-200"
                                                    alt="US flag"
                                                    :src="generateImgPath(item.image)"
                                                    style="
                                                        display: inline-block;
                                                        width: 100%;
                                                        height: 100%;
                                                        vertical-align: middle;
                                                        max-width: 50px;
                                                        max-height: 35px;
                                                    "
                                                />
                                            </div>
                                        </div>
                                        <div class="flex-1 ml-4">
                                            <span class="block font-serif text-base font-normal text-black text-sm">{{
                                                item.title
                                            }}</span>
                                        </div>
                                        <span v-html="item.link.icon"></span>
                                    </div>
                                </div>
                            </a>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentAiVoiceGeneratorLanguageBlock } from '~/types'
import { generateImgPath } from '~/utils/image'

const props = defineProps<iContentAiVoiceGeneratorLanguageBlock>()
</script>

<template>
    <div>
        <div class="relative isolate content my-36">
            <div class="mx-auto">
                <h2 class="block font-sans text-5xl font-bold text-black">{{ basic.title }}</h2>
                <div class="mt-6 space-y-6 divide-y divide-gray-200">
                    <div v-for="item in content" class="pt-6 [&amp;_p]:text-sm md:[&amp;_p]:text-md">
                        <details class="text-lg [&amp;_svg]:open:-rotate-180">
                            <summary
                                class="cursor-pointer flex w-full items-start justify-between text-left text-gray-400"
                            >
                                <h3 class="text-left w-full">
                                    <span class="font-medium text-gray-900">{{ item.title }}</span>
                                </h3>
                                <span class="ml-4">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="currentColor"
                                        aria-hidden="true"
                                        class="rotate-0 h-6 w-6 transform transition-all duration-300 text-gray-500 fill-current"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M12.53 16.28a.75.75 0 01-1.06 0l-7.5-7.5a.75.75 0 011.06-1.06L12 14.69l6.97-6.97a.75.75 0 111.06 1.06l-7.5 7.5z"
                                            clip-rule="evenodd"
                                        ></path></svg
                                ></span>
                            </summary>
                            <div class="mt-2 pr-12">
                                <p class="text-base text-gray-500 prose">
                                    {{ item.description }}
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentFaqBlock } from '~/types'

const props = defineProps<iContentFaqBlock>()
</script>

<style scoped>
.bg-audio {
    background-color: #312b41;
}
</style>

<template>
    <div>
        <div class="relative isolate my-15">
            <div class="mx-auto max-w-7xl">
                <div class="flex justify-between">
                    <div class="">
                        <h2 class="block font-sans text-4xl font-bold text-black">
                            {{ basic.title }}
                        </h2>
                        <span class="block font-serif text-lg font-normal text-gray-700 mt-5 max-w-3xl">
                            {{ basic.description }}
                        </span>
                    </div>
                    <a
                        title="Users love Text To Speech OpenAI on G2"
                        class="ml-6 hidden md:block"
                        href="https://www.g2.com/products/Text To Speech OpenAIio/reviews?utm_source=rewards-badge"
                    >
                        <img
                            alt="Users love Text To Speech OpenAI on G2"
                            loading="lazy"
                            width="100"
                            height="100"
                            decoding="async"
                            data-nimg="1"
                            style="color: transparent"
                            :src="generateImgPath(basic.image)"
                    /></a>
                </div>
                <ul role="list" class="mx-auto mt-6 grid grid-cols-1 gap-8 lg:grid-cols-3">
                    <li v-for="item in content">
                        <a class="block" :href="item.url"
                            ><figure
                                class="relative rounded-xl bg-white p-8 shadow-lg hover:shadow-xl ring-1 ring-slate-900/5"
                            >
                                <img
                                    alt="5 stars"
                                    loading="lazy"
                                    width="477"
                                    height="87"
                                    decoding="async"
                                    data-nimg="1"
                                    class="w-36 mb-3"
                                    style="color: transparent"
                                    :src="generateImgPath(item.image)"
                                />
                                <blockquote>
                                    <span
                                        class="block font-serif text-sm font-normal text-gray-700 text-lg tracking-tight text-slate-900 before:content-['“'] after:content-['”']"
                                    >
                                        {{ item.review }}
                                    </span>
                                </blockquote>
                                <figcaption class="mt-6 flex items-center">
                                    <div class="overflow-hidden rounded-full bg-slate-50">
                                        <img
                                            alt=""
                                            loading="lazy"
                                            width="48"
                                            height="48"
                                            decoding="async"
                                            data-nimg="1"
                                            class="h-12 w-12 object-cover"
                                            style="color: transparent"
                                            :src="generateImgPath(item.avatar)"
                                        />
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-base font-medium leading-6 tracking-tight text-slate-900">
                                            {{ item.name }}
                                        </div>
                                        <div class="mt-1 text-sm text-slate-600">{{ item.sub_title }}</div>
                                    </div>
                                </figcaption>
                            </figure></a
                        >
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentReviewBlock } from '~/types'
import { generateImgPath } from '~/utils/image'

const props = defineProps<iContentReviewBlock>()
</script>

<template>
    <div>
      <div>
        <div class="sm:text-left mb-12 md:mb-16 lg:mb-20">
          <h2 class="block font-sans text-5xl font-bold text-black mb-6">
            {{ basic.title }}
          </h2>
          <span class="block font-serif text-xl font-normal text-gray-700">
            {{ basic.description }}
          </span>
        </div>
        <div class="max-w-lg sm:mx-auto md:max-w-none">
          <div class="grid grid-cols-1 gap-y-16 md:grid-cols-2 md:gap-x-12 md:gap-y-16">
            <div
              v-for="contentRow in content"
              class="relative flex flex-col gap-6 sm:flex-row md:flex-col lg:flex-row"
            >
              <div class="sm:min-w-0 sm:flex-1 flex flex-col justify-between">
                <div>
                  <a href="/use-cases/videos"
                    ><h3 class="block font-sans text-2xl font-bold text-black mb-4">
                      {{ contentRow.title }}
                    </h3></a
                  >
                  <p class="block font-serif text-base font-normal text-gray-500 w-full">
                    {{ contentRow.description }}
                  </p>
                </div>
                <div class="flex items-center space-x-4 mt-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                    class="text-black w-16 h-16 hover:text-gray-700 cursor-pointer"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M2 10a8 8 0 1116 0 8 8 0 01-16 0zm6.39-2.908a.75.75 0 01.766.027l3.5 2.25a.75.75 0 010 1.262l-3.5 2.25A.75.75 0 018 12.25v-4.5a.75.75 0 01.39-.658z"
                      clip-rule="evenodd"
                    ></path>
                  </svg>
                  <div class="flex flex-col space-y-2">
                    <span
                      class="block font-serif text-sm font-normal text-gray-700 leading-none"
                    >
                      {{ contentRow.sample?.title }}
                    </span>
                    <h3
                      class="block font-serif text-base font-semibold text-gray-900 font-sans leading-none"
                    >
                      {{ contentRow.sample?.description }}
                    </h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import type { iContent } from "~/types";
  
  const props = defineProps<iContent>();
  </script>
  
<template>
    <div>
        <div class="py-24">
            <div
                class="relative isolate overflow-hidden bg-audio px-6 py-20 rounded-3xl sm:px-10 sm:py-24 lg:py-24 xl:px-24"
            >
                <div
                    class="mx-auto grid max-w-2xl grid-cols-1 gap-x-8 gap-y-16 sm:gap-y-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 lg:items-center lg:gap-y-0"
                >
                    <div class="lg:row-start-2 lg:max-w-md">
                        <h2 class="block font-sans text-5xl font-bold text-gray-700 tracking-tight text-white">
                            {{ basic.title }}
                        </h2>
                        <p class="mt-6 text-lg leading-8 text-gray-300">
                            {{ basic.description }}
                        </p>
                    </div>
                    <div class="relative min-w-full max-w-xl lg:row-span-4 lg:w-[48rem] lg:max-w-none">
                        <div class="absolute left-24 bottom-0 md:right-12 lg:bottom-24 lg:left-36">
                            <div class="pb-12 w-full mt-12 lg:mt-1">
                                <dl class="w-full gap-x-4 gap-y-10">
                                    <div v-for="item in content.audio_setting" class="border-b border-gray-700">
                                        <div class="relative overflow-hidden items-center">
                                            <dt>
                                                <div class="flex items-center space-x-3 m-3">
                                                    <div class="relative w-16 h-16">
                                                        <div
                                                            class="absolute inset-0 flex items-center justify-center z-10"
                                                        >
                                                            <span v-html="item.icon"></span>
                                                        </div>
                                                        <div
                                                            class="absolute inset-0 m-auto w-14 h-14 bg-transparent rounded-full border border-white"
                                                            style="
                                                                max-width: 80%;
                                                                max-height: 80%;
                                                                animation: pulse 4s 0s infinite ease-out both;
                                                            "
                                                        ></div>
                                                        <div
                                                            class="absolute inset-0 m-auto w-14 h-14 bg-transparent rounded-full border border-white"
                                                            style="
                                                                max-width: 80%;
                                                                max-height: 80%;
                                                                animation: pulse 4s 0.5s infinite ease-out both;
                                                            "
                                                        ></div>
                                                    </div>
                                                    <div class="flex-grow grid-cols-1">
                                                        <div class="flex items-center text-semiBold">
                                                            <span
                                                                class="block font-serif text-base font-semibold text-white font-sans leading-none"
                                                            >
                                                                {{ item.title }}
                                                            </span>
                                                            <span
                                                                v-for="tag in item.tags"
                                                                class="inline-flex items-center rounded-full py-0.5 text-sm font-medium whitespace-nowrap px-2 bg-blue-100 ml-2 justify-between flex content-start hidden md:block leading-none"
                                                                ><span class="text-blue-800 font-serif text-xs">{{
                                                                    tag
                                                                }}</span></span
                                                            >
                                                        </div>
                                                        <div class="text-sm text-gray-500 mt-2 hidden md:block">
                                                            {{ item.description }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </dt>
                                        </div>
                                    </div>
                                </dl>
                            </div>
                        </div>
                        <img
                            alt="Text to Speech screenshot"
                            loading="lazy"
                            width="2432"
                            height="1442"
                            decoding="async"
                            data-nimg="1"
                            class="relative -z-20 min-w-full max-w-xl rounded-xl shadow-xl ring-1 ring-white/10 lg:row-span-4 lg:w-[48rem] lg:max-w-none"
                            style="color: transparent"
                            :src="generateImgPath(content.image)"
                        />
                    </div>
                    <div class="max-w-xl lg:row-start-3 lg:mt-10 lg:max-w-md lg:border-t lg:border-white/10 lg:pt-10">
                        <div class="max-w-xl space-y-8 text-base leading-7 text-gray-300 lg:max-w-none">
                            <div v-for="item in content.content_introduction" class="relative">
                                <h3 class="inline-block font-semibold text-white">
                                    <span class="ml-9" v-html="item.icon"></span>
                                    {{ item.title }}
                                </h3>
                                <div class="inline">
                                    <p class="block font-serif text-base font-normal text-gray-500 ml-1">
                                        {{ item.description }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="md:flex gap-x-6 items-center mt-10 md:flex-row flex-col">
                            <a
                                class="whitespace-nowrap text-bold text-lg text-white hover:text-gray-200"
                                :href="content.link.url"
                                >{{ content.link.title }}
                                <span v-html="content.link.icon"></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { iContentAiTextToSpeechBlock } from '~/types'
import { generateImgPath } from '~/utils/image'

const props = defineProps<iContentAiTextToSpeechBlock>()
</script>

<style scoped>
.bg-audio {
    background-color: #312b41;
}
</style>

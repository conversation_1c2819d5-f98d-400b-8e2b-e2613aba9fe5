<template>
    <ul
        class="flex w-max mx-auto border border-gray-300 dark:border-gray-600 px-1 py-1 rounded-xl text-xs font-medium text-center text-gray-500 dark:text-gray-400"
    >
        <li class="mr-1">
            <TheTooltip>
                <a
                    class="flex flex-inline items-center cursor-pointer px-1.5 py-1.5 rounded-lg hover:text-gray-400 hover:bg-primary-100 dark:hover:bg-primary-800 dark:hover:text-white"
                    :class="{
                        'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
                            chatGPTVersion == 'gpt-3.5',
                    }"
                    @click="changeAIModel('gpt-3.5')"
                    aria-current="page"
                >
                    <svg
                        class="w-4 h-4 mr-1 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 21 20"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="m11.479 1.712 2.367 4.8a.532.532 0 0 0 .4.292l5.294.769a.534.534 0 0 1 .3.91l-3.83 3.735a.534.534 0 0 0-.154.473l.9 5.272a.535.535 0 0 1-.775.563l-4.734-2.49a.536.536 0 0 0-.5 0l-4.73 2.487a.534.534 0 0 1-.775-.563l.9-5.272a.534.534 0 0 0-.154-.473L2.158 8.48a.534.534 0 0 1 .3-.911l5.294-.77a.532.532 0 0 0 .4-.292l2.367-4.8a.534.534 0 0 1 .96.004Z"
                        />
                    </svg>

                    {{ $t('High Quality') }}
                </a>
                <template #tooltip>
                    <div class="flex flex-col space-y-2">
                        <div class="max-w-sm">
                            {{ $t('Our fastest model, great for most everyday tasks.') }}
                            <div class="mt-2">
                                <span>
                                    {{ $t('For text in English: ') }}
                                </span>
                                <span>
                                    {{ $t('1,000 words ~ 3,000 tokens') }}
                                </span>
                            </div>
                            <div class="mt-2 text-yellow-300">
                                {{
                                    !usableModelList.includes('gpt-3.5')
                                        ? $t('Update your plan to use this model.')
                                        : ''
                                }}
                            </div>
                        </div>
                    </div>
                </template>
            </TheTooltip>
        </li>
        <li>
            <TheTooltip>
                <a
                    @click="changeAIModel('gpt-4-turbo')"
                    :class="{
                        'text-white bg-primary-600 active hover:text-gray-200 hover:bg-primary-700':
                            chatGPTVersion == 'gpt-4-turbo',
                    }"
                    class="relative flex flex-inline items-center cursor-pointer px-1.5 py-1.5 rounded-lg hover:text-gray-400 hover:bg-gray-100 dark:hover:bg-primary-800 dark:hover:text-white"
                >
                    <svg
                        class="w-4 h-4 mr-1 group-hover:text-gray-500 dark:text-gray-50 dark:group-hover:text-gray-300"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 22 21"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-width="2"
                            d="M7.24 7.194a24.16 24.16 0 0 1 3.72-3.062m0 0c3.443-2.277 6.732-2.969 8.24-1.46 2.054 2.053.03 7.407-4.522 11.959-4.552 4.551-9.906 6.576-11.96 4.522C1.223 17.658 1.89 14.412 4.121 11m6.838-6.868c-3.443-2.277-6.732-2.969-8.24-1.46-2.054 2.053-.03 7.407 4.522 11.959m3.718-10.499a24.16 24.16 0 0 1 3.719 3.062M17.798 11c2.23 3.412 2.898 6.658 1.402 8.153-1.502 1.503-4.771.822-8.2-1.433m1-6.808a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"
                        />
                    </svg>
                    <div
                        v-if="chatGPTVersion != 'gpt-4-turbo'"
                        class="absolute inline-flex items-center justify-center h-5 px-1 text-[7px] rounded-md font-bold text-white bg-red-500 border-2 border-white -top-2 -end-4 dark:border-gray-900 animate__animated animate__tada animate__infinite animate__slower"
                    >
                        {{ $t('New !') }}
                    </div>
                    <span>{{ $t('HD Quality') }}</span>
                </a>
                <template #tooltip>
                    <div class="flex flex-col space-y-2">
                        <div class="max-w-sm">
                            {{
                                isGPT4Enabled
                                    ? $t(
                                          'GPT-4 Turbo brings several improvements, including a greatly increased context window and access to more up-to-date knowledge.'
                                      )
                                    : $t('coming soon')
                            }}
                            <br />
                            <div class="mt-2">
                                <span>
                                    {{ $t('For text in English: ') }}
                                </span>
                                <span>
                                    {{ $t('1,000 words ~ 45,000 tokens') }}
                                </span>
                            </div>
                            <div class="mt-2 text-yellow-300">
                                {{
                                    !usableModelList.includes('gpt-4-turbo')
                                        ? $t('Update your plan to use this model.')
                                        : ''
                                }}
                            </div>
                        </div>
                    </div>
                </template>
            </TheTooltip>
        </li>
    </ul>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useAppStore } from '~~/stores/app'
import TheTooltip from '~/base-components/TheTooltip.vue'
import { usePaymentsStore } from '~~/stores/payments'
const paymentStore = usePaymentsStore()

const { usableModelList } = storeToRefs(paymentStore)
const appStore = useAppStore()

const { chatGPTVersion } = storeToRefs(appStore)

const config = useRuntimeConfig()
const isGPT4Enabled = config.public.NUXT_MODEL_GPT_4

const changeAIModel = (model: string) => {
    if (usableModelList.value.includes(model)) {
        appStore.changeChatGPTVersion(model)
    } else {
        navigateTo('/pricing')
    }
}
</script>

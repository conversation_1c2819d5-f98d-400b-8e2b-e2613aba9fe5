<template>
    <!-- drawer component -->
    <div
        id="drawer-swipe"
        class="group flex flex-col items-start md:items-center fixed z-40 transition-transform left-0 right-0 translate-y-full duration-300"
        tabindex="-1"
        aria-labelledby="drawer-swipe-label"
    >
        <div
            class="relative overflow-x-hidden x-auto max-w-4xl w-full bg-white border shadow-xl border-gray-200 rounded-t-lg dark:border-gray-700 dark:bg-gray-900"
        >
            <div class="cursor-pointer hover:bg-gray-50 py-2 px-4 dark:hover:bg-gray-800" @click="drawer?.toggle()">
                <span
                    @click="drawer?.toggle()"
                    class="absolute w-8 h-1 -translate-x-1/2 bg-gray-300 rounded-lg top-3 left-1/2 dark:bg-gray-600"
                ></span>
                <h5
                    id="drawer-swipe-label"
                    class="inline-flex items-center text-base text-gray-500 dark:text-gray-400 font-medium"
                >
                    <UIcon name="i-bi-play-btn-fill" class="me-2 text-2xl" />
                    {{ $t('Audio Player') }}
                </h5>
            </div>
            <!-- <UButton
                class="absolute top-2 right-3"
                icon="i-heroicons-x-mark-solid"
                size="sm"
                color="red"
                square
                variant="soft"
                :ui="{ rounded: 'rounded-full' }"
            /> -->
            <BaseAudioPlayer class="max-w-full" :url="audioResult.link" :data="audioResult" :showScript="false" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { Drawer } from 'flowbite'

import { storeToRefs } from 'pinia'
const translateStore = useTranslateStore()
const { showAudioPlayer, audioResult, voiceObject, isPlaying, canShowAudioPlayerOnCurrentPage } = storeToRefs(translateStore)
const { t } = useI18n()


let drawer: Drawer
onMounted(() => {
    if (process.client) {
        const $targetEl = document.getElementById('drawer-swipe')
        if (!$targetEl) return
        const options = {
            placement: 'bottom',
            backdrop: true,
            bodyScrolling: false,
            backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-30',
            onHide: () => {
                showAudioPlayer.value = false
            },
            onShow: () => {
                showAudioPlayer.value = true
            },
        }

        drawer = new Drawer($targetEl, options)
    }
})

watch(
    () => showAudioPlayer.value,
    (value) => {
        if(!canShowAudioPlayerOnCurrentPage.value) return
        if (value) {
            drawer?.show()
        } else {
            drawer?.hide()
        }
    }
)

const audio = ref<HTMLAudioElement>(new Audio(audioResult.value.link))
const duration = ref(0)
const durationFormatted = computed(() => {
    const minutes = Math.floor(duration.value / 60)
    const seconds = Math.floor(duration.value % 60)
    const secondsFormatted = seconds < 10 ? `0${seconds}` : seconds
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes
    return `${minutesFormatted}:${secondsFormatted}`
})
const currentDuration = ref(0)
const currentDurationFormatted = computed(() => {
    const minutes = Math.floor(currentDuration.value / 60)
    const seconds = Math.floor(currentDuration.value % 60)
    const secondsFormatted = seconds < 10 ? `0${seconds}` : seconds
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes
    return `${minutesFormatted}:${secondsFormatted}`
})

const currentDurationPercentage = computed(() => {
    return Math.floor((currentDuration.value / duration.value) * 100)
})
const onPlay = () => {
    isPlaying.value = true
}

const onPause = () => {
    isPlaying.value = false
}

const toggleAudio = () => {
    if (audio.value?.paused) {
        audio.value.play()
    } else {
        audio.value.pause()
    }
}

const onLoaded = () => {
    currentDuration.value = 0
    duration.value = audio.value.duration
    audio.value?.play()
    audio.value.addEventListener('timeupdate', () => {
        currentDuration.value = audio.value.currentTime
    })
}
</script>

<style scoped>
.playing {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.3rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0.5rem;
    box-sizing: border-box;
}

.playing__bar {
    display: inline-block;
    background: white;
    width: 30%;
    height: 100%;
    animation: up-and-down 1.3s ease infinite alternate;
}

.playing__bar1 {
    height: 60%;
}

.playing__bar2 {
    height: 30%;
    animation-delay: -2.2s;
}

.playing__bar3 {
    height: 75%;
    animation-delay: -3.7s;
}

@keyframes up-and-down {
    10% {
        height: 30%;
    }

    30% {
        height: 100%;
    }

    60% {
        height: 50%;
    }

    80% {
        height: 75%;
    }

    100% {
        height: 60%;
    }
}
</style>

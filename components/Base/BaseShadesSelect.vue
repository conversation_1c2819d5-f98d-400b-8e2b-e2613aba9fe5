<script setup lang="ts">
const inputTextProStore = useInputTextProStore()
const { recentEmotions, loadings, vibe, emotions } = storeToRefs(inputTextProStore)
const dialogsStore = useDialogsStore()
const { t } = useI18n()
const isOpen = ref(false)
const audio = ref<HTMLAudioElement>(new Audio())
const onPlay = () => {
    isPlaying.value = true
}

const onEnded = () => {
    isPlaying.value = false
}

const onPlayAudio = (emotion: any) => {
    if (playingEmotion.value?.id === emotion.id) {
        audio.value.pause()
        isPlaying.value = false
        // playingEmotion.value = null
    } else {
        playingEmotion.value = emotion
        // audio.value.src = emotion.sample_audio
        nextTick(() => {
            audio.value.play()
            isPlaying.value = true
        })
    }
}

const isPlaying = ref(false)
const playingEmotion = ref()
const emotionListSorted = computed(() =>
    emotions.value
        .map((emotion) => ({
            id: emotion.emotion?.toLowerCase(),
            label: t(emotion.emotion),
            description: t(emotion.emotion_long),
            emojis: `${emotion.emotion}.svg`,
            name: emotion.emotion,
            sample_audio: emotion.sample_audio,
            isPlaying: isPlaying.value && playingEmotion.value?.emotion === emotion.emotion,
        }))
        .sort((a, b) => a.label.localeCompare(b.label))
)

const props = defineProps<{
    modelValue: any
}>()

const emits = defineEmits(['update:modelValue'])

const selected = computed({
    get: () => props.modelValue,
    set: (value) => {
        emits('update:modelValue', value)
    },
})

const selectedEmotion = computed(() => {
    if (loadings.value['fetchEmotions']) {
        return t('Loading...')
    }
    const selectedEmotion = emotionListSorted.value.find((emotion) => emotion.id === props.modelValue?.id)
    return selectedEmotion ? selectedEmotion.label : t('Emotion')
})

const onSelectEmotion = (emotion: any) => {
    if (vibe.value && props.modelValue?.id !== emotion.id) {
        dialogsStore.showConfirmDialog({
            title: t('Warning'),
            message: t(
                'You have selected a vibe. If you change the emotion, the vibe will be removed. Do you want to continue?'
            ),
            confirmButtonText: t('Yes'),
            cancelButtonText: t('No'),
            onConfirm: () => {
                isOpen.value = false
                emits('update:modelValue', emotion)
                inputTextProStore.updateRecentEmotions(emotion?.id)
                vibe.value = null
                return true
            },
            onCancel: () => {
                isOpen.value = true
                return true
            },
        })
    } else {
        isOpen.value = false

        if (props.modelValue?.id === emotion.id) {
            emits('update:modelValue', null)
        } else {
            emits('update:modelValue', emotion)
            inputTextProStore.updateRecentEmotions(emotion?.id)
        }
    }

    // copy name to clipboard
    // navigator.clipboard.writeText(emotion.name)
}

const emotionGroups = computed(() => {
    const groups = [
        {
            key: 'emotions',
            commands: emotionListSorted.value.filter((emotion) => !recentEmotions.value.includes(emotion.id)),
        },
    ] as any[]

    if (recentEmotions.value.length) {
        groups.unshift({
            key: 'recent',
            label: t('Recent used'),
            commands: recentEmotions.value.map((emotionId) => {
                const emotion = emotionListSorted.value.find((e) => e.id === emotionId)
                return emotion
            }),
        })
    }
    return groups
})

onMounted(() => {
    inputTextProStore.fetchEmotions()
})
</script>

<template>
    <div class="w-full">
        <UButton
            size="xs"
            color="white"
            variant="solid"
            :trailing="false"
            @click="isOpen = true"
            :ui="{
                rounded: 'rounded-full',
                icon: {
                    base: props.modelValue?.id ? 'text-primary-500' : 'text-gray-500',
                },
            }"
            class="group"
            :loading="loadings['fetchEmotions']"
            block
        >
            <span class="truncate">
                {{ selectedEmotion }}
            </span>
            <template #leading>
                <div class="w-3 h-3 flex items-center justify-center">
                    <UIcon
                        v-if="loadings['fetchEmotions']"
                        name="i-eos-icons-loading"
                        class="absolute w-4 h-4 text-gray-500"
                    />
                    <img
                        v-else-if="props.modelValue?.emojis && props.modelValue?.emojis !== 'null'"
                        :src="`/assets/emojis/${props.modelValue?.emojis}`"
                        class="absolute w-5 h-5 group-hover:scale-150 transition-all duration-200"
                    />
                    <div v-else class="absolute w-5 h-5 flex items-center justify-center">
                        <UIcon name="fluent:emoji-sparkle-32-regular" class="text-xl text-gray-500" />
                    </div>
                </div>
            </template>
        </UButton>
        <audio
            :key="playingEmotion?.id"
            :src="playingEmotion?.sample_audio"
            ref="audio"
            @play="onPlay"
            @ended="onEnded"
        />
        <UModal
            v-model="isOpen"
            :ui="{ container: 'items-start', height: 'max-h-[80vh] overflow-auto scrollbar-thin' }"
        >
            <UCommandPalette
                :model-value="selected"
                :fuse="{
                    resultLimit: 100,
                    fuseOptions: {
                        ignoreLocation: true,
                        includeMatches: true,
                        threshold: 0,
                        keys: ['id', 'description'],
                    },
                }"
                :groups="emotionGroups"
                :placeholder="$t('Search Emotions...')"
                icon="ic:outline-emoji-emotions"
                :autoselect="false"
                :autoclear="false"
                :empty-state="{
                    icon: 'ri:emotion-sad-line',
                    label: $t(`We couldn't find any emotions.`),
                    queryLabel: $t(`We couldn't find any emotions with that term.`),
                }"
                :ui="{
                    group: {
                        command: {
                            base: 'group',
                        },
                    },
                    container: 'scrollbar-thin',
                }"
            >
                <template v-for="group in emotionGroups" :key="group.key" v-slot:[`${group.key}-icon`]="{ command }">
                    <div class="relative w-7 h-7 flex items-center justify-center">
                        <span
                            v-if="isPlaying && playingEmotion.id === command.id"
                            class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-600 opacity-30"
                        ></span>
                        <img
                            @click="onSelectEmotion(command)"
                            v-if="command.emojis"
                            :src="`/assets/emojis/${command.emojis}`"
                            class="w-7 h-7 group-hover:scale-150 transition-all duration-200"
                            :class="{
                                innertape2: isPlaying && playingEmotion?.id === command.id,
                            }"
                        />
                        <div v-else class="w-7 h-7 flex items-center justify-center">
                            <UIcon name="fluent:emoji-sparkle-32-regular" class="text-xl text-gray-500" />
                        </div>
                    </div>
                </template>
                <template
                    v-for="group in emotionGroups"
                    :key="group.key"
                    v-slot:[`${group.key}-command`]="{ command, selected }"
                >
                    <div class="flex flex-row items-center gap-2" @click="onSelectEmotion(command)">
                        <div class="flex flex-col">
                            <div class="flex items-center gap-1">
                                <span class="text-sm font-semibold">{{ command.label }} </span>
                                <span v-if="selected" class="italic font-light text-xs text-orange-500">
                                    ({{ $t('Click again to unuse this emotion') }})
                                </span>
                            </div>
                            <span
                                class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 sm:group-hover:line-clamp-1"
                                >{{ command.description }}</span
                            >
                        </div>
                    </div>
                </template>
                <template
                    v-for="group in emotionGroups"
                    :key="group.key"
                    v-slot:[`${group.key}-inactive`]="{ command }"
                >
                    <div
                        class="flex sm:hidden flex-row items-center gap-1 sm:gap-2 sm:group-hover:flex sm:absolute right-0 px-0 sm:px-2"
                    >
                        <UButton
                            v-if="command.sample_audio"
                            size="2xs"
                            :icon="
                                isPlaying && playingEmotion.id === command.id
                                    ? 'i-ic-outline-pause-circle'
                                    : 'i-ic-outline-play-circle'
                            "
                            color="white"
                            variant="solid"
                            @click="onPlayAudio(command)"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Sample') }}
                            </span>
                        </UButton>

                        <UButton
                            size="2xs"
                            icon="material-symbols:check-circle"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    base: 'text-primary-500',
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onSelectEmotion(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Use') }}
                            </span>
                        </UButton>
                    </div>
                </template>

                <template v-for="group in emotionGroups" :key="group.key" v-slot:[`${group.key}-active`]="{ command }">
                    <div
                        class="flex sm:hidden flex-row items-center gap-1 sm:gap-2 sm:group-hover:flex sm:absolute right-0 px-0 sm:px-2"
                    >
                        <UButton
                            v-if="command.sample_audio"
                            size="2xs"
                            :icon="
                                isPlaying && playingEmotion.id === command.id
                                    ? 'i-ic-outline-pause-circle'
                                    : 'i-ic-outline-play-circle'
                            "
                            color="white"
                            variant="solid"
                            @click="onPlayAudio(command)"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Sample') }}
                            </span>
                        </UButton>

                        <UButton
                            size="2xs"
                            icon="material-symbols:check-circle"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    base: 'text-primary-500',
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onSelectEmotion(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Use') }}
                            </span>
                        </UButton>
                    </div>
                </template>
            </UCommandPalette>
            <div class="p-4 text-xs text-gray-500 dark:text-gray-400">
                ※
                {{
                    $t(
                        'Because we use AI to generate the audio, sometimes the audio may not be perfect as expected. We are working hard to improve the quality of the audio. Thank you for your understanding.'
                    )
                }}
            </div>
        </UModal>
    </div>
</template>

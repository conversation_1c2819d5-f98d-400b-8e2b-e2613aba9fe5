<script setup lang="ts">
const props = defineProps<{
    modelValue: any
}>()

const inputTextProStore = useInputTextProStore()
const { selectedPrompt, userCustomPrompts, loadings, errors } = storeToRefs(inputTextProStore)

const { t } = useI18n()
const isOpen = ref(false)
const promptName = ref(selectedPrompt.value?.label) as Ref<string | null>
const promptValue = ref(props.modelValue) as Ref<string>
const promptNameRef = ref()
const isSaveAsNewPrompt = ref(false)
const dialogsStore = useDialogsStore()
const toast = useToast()

const emits = defineEmits(['update:modelValue'])
const onSelectPrompt = (prompt: any) => {
    // check if button is clicked
    if (prompt === null) {
        return
    }
    selectedPrompt.value = prompt
    promptValue.value = prompt.value
    promptName.value = prompt.label
}

const userCustomPromptsGroups = computed(() => {
    const groups = [
        {
            key: 'prompts',
            label: t('Your saved prompts'),
            commands: userCustomPrompts.value?.map((prompt: any, index: number) => ({
                id: prompt?.id,
                label: prompt.name,
                value: prompt.prompt,
                ...prompt,
            })),
        },
    ] as any[]
    return groups
})

const onSaveCustomPrompt = () => {
    if (promptValue.value?.trim()) {
        isSaveAsNewPrompt.value = true
        if (!promptName.value) {
            promptName.value = t('Custom prompt {count}', { count: userCustomPrompts.value.length + 1 })
        }
        nextTick(() => {
            promptNameRef.value?.input?.focus()
        })
    }
}

const onConfirmSaveCustomPrompt = async () => {
    if (promptValue.value?.trim() && promptName.value) {
        const newPrompt = {
            label: promptName.value,
            value: promptValue.value,
        }
        const result = await inputTextProStore.addNewPrompt(newPrompt)

        if (result) {
            toast.add({
                id: 'save-prompt',
                color: 'primary',
                title: t('Success'),
                description: t('Saved prompt successfully'),
                timeout: 3000,
                icon: 'i-ooui:success',
            })
            isSaveAsNewPrompt.value = false
        } else {
            toast.add({
                id: 'save-prompt',
                color: 'red',
                title: t('Error'),
                description: t('Saved prompt failed') + ". " + t(errors.value.addNewPrompt?.data?.detail?.error_code || ''),
                timeout: 3000,
                icon: 'i-ooui:success',
            })
        }
    }
}

const onConfirmUpdateCustomPrompt = async () => {
    if (promptValue.value?.trim() && promptName.value) {
        const newPrompt = {
            id: selectedPrompt.value?.id,
            label: promptName.value,
            value: promptValue.value,
        }
        const result = await inputTextProStore.updatePrompt(newPrompt)

        if (result) {
            toast.add({
                id: 'save-prompt',
                color: 'primary',
                title: t('Success'),
                description: t('Updated prompt successfully'),
                timeout: 3000,
                icon: 'i-ooui:success',
            })
            isSaveAsNewPrompt.value = false
            selectedPrompt.value = newPrompt
        } else {
            toast.add({
                id: 'save-prompt',
                color: 'red',
                title: t('Error'),
                description: t('Updated prompt failed') + ". " + t(errors.value.updatePrompt?.data?.detail?.error_code || ''),
                timeout: 3000,
                icon: 'i-ooui:success',
            })
        }
    }
}

const onUsePrompt = (promt?: any) => {
    if (promt?.value) {
        onSelectPrompt(promt)
    }
    if (promptValue.value?.trim()) {
        emits('update:modelValue', promptValue.value)

        isOpen.value = false
    }
}

const promptInputed = computed(() => {
    return selectedPrompt.value?.label || props.modelValue || t('Prompt')
})

const onCancelPrompt = () => {
    isOpen.value = false
    promptValue.value = ''
    promptName.value = null
    selectedPrompt.value = null
    emits('update:modelValue', '')
}

const onDeletePrompt = (prompt: any) => {
    dialogsStore.showConfirmDialog({
        title: t('Delete'),
        message: t('Are you sure you want to delete this prompt?'),
        confirmButtonText: t('Delete'),
        cancelButtonText: t('Cancel'),
        onConfirm: async () => {
            isOpen.value = true

            const resul = await inputTextProStore.removePrompt(prompt)
            
            if (resul) {
                toast.add({
                    id: 'delete-prompt',
                    color: 'primary',
                    title: t('Success'),
                    description: t('Deleted prompt successfully'),
                    timeout: 3000,
                    icon: 'i-ooui:success',
                })
                // if prompt is selected, clear it
                if (selectedPrompt.value?.id === prompt?.id) {
                    selectedPrompt.value = null
                    promptValue.value = ''
                    promptName.value = null
                    emits('update:modelValue', '')
                }
            } else {
                toast.add({
                    id: 'delete-prompt',
                    color: 'red',
                    title: t('Error'),
                    description: t('Deleted prompt failed') + ". " + t(errors.value.removePrompt?.data?.detail?.error_code || ''),
                    timeout: 3000,
                    icon: 'i-ooui:success',
                })
            }
        },
        onCancel: () => {
            dialogsStore.onCloseConfirmDialog()
            isOpen.value = true
            return true
        },
    })
}

onMounted(() => {
    inputTextProStore.fetchPrompts()
})
</script>

<template>
    <div class="w-full">
        <UButton
            :icon="props.modelValue ? 'material-symbols:check-circle' : 'fluent:prompt-16-filled'"
            size="xs"
            color="white"
            variant="solid"
            :label="promptInputed"
            truncate
            class="max-w-36"
            :trailing="false"
            @click="isOpen = true"
            :ui="{
                rounded: 'rounded-full',
                icon: {
                    base: props.modelValue ? 'text-primary-500' : 'text-gray-500',
                },
            }"
            block
        />
        <UModal v-model="isOpen" :ui="{ container: 'items-start', height: 'max-h-[90vh] overflow-auto !scrollbar-thin' }">
            <div class="p-4 flex flex-col gap-4">
                <UFormGroup :label="$t('Custom prompt')">
                    <UTextarea
                        autoresize
                        v-model="promptValue"
                        :placeholder="$t('Enter your custom prompt here.')"
                        class="w-full"
                        :maxrows="7"
                        :maxLength="500"
                        :ui="{
                            base: 'scrollbar-thin',
                        }"
                    />

                    <template #hint>
                        <div class="text-xs">{{ promptValue.length }} / 500</div>
                    </template>
                </UFormGroup>
                <UFormGroup
                    v-if="promptName !== null"
                    :label="$t('Prompt name')"
                    :help="$t('This name will help you identify your prompt.')"
                    :ui="{
                        help: 'text-xs',
                    }"
                >
                    <UInput
                        ref="promptNameRef"
                        :placeholder="$t('Ex: Funny prompt')"
                        :ui="{
                            rounded: 'rounded-sm',
                        }"
                        v-model="promptName"
                        :maxLength="50"
                    >
                    </UInput>
                    <template #hint>
                        <div class="text-xs">{{ promptName?.length || 0 }} / 50</div>
                    </template>
                </UFormGroup>

                <div>
                    <template v-if="isSaveAsNewPrompt">
                        <div class="flex flex-row items-center justify-start gap-2">
                            <UButton
                                :label="$t('Discard')"
                                size="xs"
                                icon="material-symbols:cancel-outline-rounded"
                                color="gray"
                                variant="soft"
                                @click="isSaveAsNewPrompt = false"
                                :disabled="loadings.addNewPrompt"
                            />
                            <UButton
                                :disabled="!promptValue?.trim() || !promptName"
                                :label="$t('Ok, save it!')"
                                size="xs"
                                icon="bi:send-check"
                                color="primary"
                                @click="onConfirmSaveCustomPrompt"
                                :loading="loadings.addNewPrompt"
                            />
                        </div>
                    </template>
                    <template v-else>
                        <div class="flex flex-row items-center justify-between flex-wrap space-y-2">
                            <div class="flex flex-row items-center gap-2">
                                <UButton
                                    v-if="promptValue?.trim()"
                                    :label="$t('Save as new')"
                                    size="xs"
                                    icon="material-symbols:add"
                                    color="white"
                                    @click="onSaveCustomPrompt"
                                />
                       
                                <UButton
                                    v-if="selectedPrompt"
                                    :label="$t('Update')"
                                    size="xs"
                                    icon="mdi:content-save-check"
                                    color="white"
                                    @click="onConfirmUpdateCustomPrompt"
                                    :loading="loadings.updatePrompt"
                                />

                                <UButton
                                    v-if="selectedPrompt"
                                    :label="$t('Delete')"
                                    size="xs"
                                    color="white"
                                    icon="material-symbols:delete-outline"
                                    :ui="{
                                        icon: {
                                            base: 'text-red-500',
                                        },
                                    }"
                                    @click="onDeletePrompt(selectedPrompt)"
                                    :loading="loadings.removePrompt"
                                />
                            </div>
                            <div class="flex flex-row items-center gap-2 ml-auto">
                                <UButton
                                    :label="$t(`Don't use`)"
                                    size="xs"
                                    icon="material-symbols:cancel-outline"
                                    color="white"
                                    @click="onCancelPrompt"
                                />
                                <UButton
                                    v-if="promptValue?.trim()"
                                    :label="$t('Use')"
                                    size="xs"
                                    icon="material-symbols:check-circle"
                                    color="primary"
                                    @click="onUsePrompt"
                                />
                            </div>
                        </div>
                    </template>
                </div>
            </div>
            <UCommandPalette
                :model-value="selectedPrompt"
                :fuse="{
                    resultLimit: 100,
                    fuseOptions: {
                        ignoreLocation: true,
                        includeMatches: true,
                        threshold: 0,
                        keys: ['id', 'description'],
                    },
                }"
                nullable
                :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'gray', variant: 'link', padded: false }"
                :groups="userCustomPromptsGroups"
                :autoselect="false"
                :autoclear="false"
                :searchable="false"
                :empty-state="{
                    icon: 'carbon:prompt-session',
                    label: $t(`You don't have any saved prompts yet.`),
                    queryLabel: $t(`We couldn't find any prompts with that term.`),
                }"
                :ui="{
                    group: {
                        command: {
                            base: 'group',
                        },
                    },
                    container: 'scrollbar-thin',
                }"
                :loading="loadings.updatePrompt || loadings.removePrompt || loadings.addNewPrompt || loadings.fetchPrompts"
            >
                <template #prompts-command="{ command }">
                    <div
                        @click="onSelectPrompt(command)"
                        class="w-full flex-1 flex flex-row items-center justify-between gap-2"
                    >
                        <div class="flex flex-col hover:text-primary-500">
                            <div class="text-sm font-semibold line-clamp-1">{{ command.label }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 line-clamp-3">{{ command.value }}</div>
                        </div>
                    </div>
                </template>
                <template #prompts-inactive="{ command }">
                    <div class="hidden flex-row items-center gap-2 group-hover:flex">
                        <UButton
                            variant="solid"
                            :label="$t('Delete')"
                            size="2xs"
                            icon="material-symbols:delete-outline"
                            color="white"
                            :ui="{
                                icon: {
                                    base: 'text-red-300',
                                },
                            }"
                            @click="onDeletePrompt(command)"
                        />
                        <UButton
                            :label="$t('Edit')"
                            size="2xs"
                            icon="material-symbols:contract-edit-outline-sharp"
                            color="white"
                            variant="solid"
                            @click="onSelectPrompt(command)"
                        />

                        <UButton
                            :label="$t('Use')"
                            size="2xs"
                            icon="material-symbols:check-circle"
                            color="white"
                            variant="solid"
                            @click="onUsePrompt(command)"
                            :ui="{
                                icon: {
                                    base: 'text-primary-300',
                                },
                            }"
                        />
                    </div>
                </template>
            </UCommandPalette>
        </UModal>
    </div>
</template>

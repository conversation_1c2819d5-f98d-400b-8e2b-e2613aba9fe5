<template>
    <MyVoiceNewCard v-if="add_new_card" />
    <UCard
        :ui="{
            body: { padding: '!p-0 h-full' },
            shadow: 'shadow-sm hover:shadow-lg',
            ring: active ? 'ring-primary-500 dark:ring-primary-800' : 'ring-gray-200 dark:ring-gray-800',
        }"
        class="group"
    >
        <div class="flex flex-col h-full">
            <div
                class="relative flex flex-row justify-between p-2 gap-2 hover:cursor-pointer"
                @click="unAvailable ? null : $emit('select', props.id)"
                :class="{
                    'opacity-50': unAvailable,
                }"
            >
                <div class="relative group/des min-h-24 w-full">
                    <USkeleton v-if="loading" class="h-5 w-20" />
                    <div
                        v-else
                        class="text-sm font-semibold flex flex-row gap-2 items-center"
                        :class="{
                            'text-primary-500 dark:text-primary-400': active,
                        }"
                    >
                        {{ props.speaker_name }}

                        <UBadge
                            v-if="props.badge"
                            :ui="{
                                size: {
                                    xs: 'text-[10px] px-1 py-0',
                                },
                            }"
                            color="primary"
                            size="xs"
                            variant="subtle"
                        >
                            <div class="flex flex-row gap-1 items-center">
                                <UIcon
                                    name="fluent-color:premium-16"
                                    class="text-sm animate__animated animate__swing animate__infinite animate__slow"
                                />
                                {{ props.badge }}
                            </div>
                        </UBadge>
                    </div>
                    <div v-if="loading" class="flex flex-col gap-2 mt-2">
                        <USkeleton class="h-4 w-36" />
                        <USkeleton class="h-4 w-44" />
                    </div>
                    <div
                        v-else
                        class="text-xs line-clamp-2 p-1 box-border border border-transparent group-hover/des:line-clamp-none group-hover/des:absolute top-5 group-hover/des:z-30 group-hover/des:bg-white dark:group-hover/des:bg-gray-900 group-hover/des:w-full transition-all duration-150 break-words whitespace-break-spaces"
                    >
                        {{ props.description }}
                    </div>
                    <div class="pt-1 flex flex-row gap-1 group-hover/des:hidden">
                        <UBadge v-if="props.language" color="gray" variant="solid" size="xs">
                            {{ $t(props.language || '') }}
                        </UBadge>
                        <UBadge v-if="props.age" color="gray" variant="solid" size="xs" class="truncate">
                            {{ $t(props.age || '') }}
                        </UBadge>
                        <UBadge v-if="props.gender" color="gray" variant="solid" size="xs">
                            {{ $t(props.gender || '') }}
                        </UBadge>
                    </div>
                </div>
                <div class="flex flex-col items-end gap-2 py-1.2">
                    <USkeleton v-if="loading" class="h-3 w-16" />
                    <div v-else class="text-[10px] font-semibold">{{ props.id }}</div>

                    <UChip
                        :show="false"
                        size="md"
                        position="bottom-right"
                        class="transition-all duration-200 text-right"
                        inset
                        :ui="{ base: '-mx-1 rounded-none ring-0', background: '' }"
                        :class="{
                            'group-hover:scale-125': !isPlaying,
                        }"
                    >
                        <div class="relative h-8 w-8 rounded-full">
                            <span
                                v-if="isPlaying"
                                class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-600 opacity-30"
                            ></span>
                            <USkeleton v-if="loading" class="h-10 w-10" :ui="{ rounded: 'rounded-full' }" />
                            <UAvatar
                                v-else
                                :src="
                                    ['openai_voice', 'openai_voice_pro'].includes(props.type || '')
                                        ? `assets/images/avatars/${props.speaker_name?.toLowerCase()}.svg`
                                        : ''
                                "
                                :icon="getAccentByValue(props.accent || '')?.icon || 'i-ph-question-fill'"
                                size="sm"
                                :ui="{ icon: { size: { sm: 'w-6 h-6' } } }"
                                :class="{
                                    innertape2: isPlaying,
                                }"
                            />
                        </div>
                        <template #content>
                            <UIcon name="i-heroicons-check-circle-solid" class="text-primary-500 text-xl" />
                        </template>
                    </UChip>
                    <UBadge
                        v-if="props.training_status && isMyVoicePage"
                        class="text-xs"
                        :color="getVoiceTrainingStatusByValue(props.training_status as TrainingStatusEnum)?.color"
                        variant="solid"
                        size="xs"
                    >
                        {{
                            props.training_status
                                ? getVoiceTrainingStatusByValue(props.training_status as TrainingStatusEnum)?.label
                                : ''
                        }}
                    </UBadge>
                </div>
            </div>
            <div
                v-if="isMyVoicePage"
                :class="{
                    'opacity-50': unAvailable,
                }"
            >
                <div class="flex px-1 flex-row justify-between text-xs text-slate-4000 m-1 gap-2">
                    <div :key="locale">{{ createdAtFormat }}</div>
                    <UBadge color="gray" variant="solid" size="xs">{{
                        $t('credits', { value: props.used_credit || 0 })
                    }}</UBadge>
                </div>
            </div>
            <div
                v-if="showAccentSelect && (runtimeConfig.public.features.accent || isSuperUser)"
                class="border-t dark:border-gray-700 p-2"
            >
                <BaseAccentSelect
                    :value="accentStore"
                    :voice-type="props.type"
                    :language="props.accent"
                    @update:accent="onAccentChange"
                />
            </div>
            <div
                class="border-t dark:border-gray-700 grid text-center text-xs grid-cols-2 mt-auto"
                :class="{
                    'grid-cols-3': isMyVoicePage && !unAvailable,
                    '!grid-cols-1': unAvailable,
                }"
            >
                <template v-if="loading">
                    <div v-for="i in 2" class="w-full p-2 flex justify-center items-center">
                        <USkeleton class="h-4 w-2/3" />
                    </div>
                </template>
                <template v-else>
                    <template v-if="!unAvailable">
                        <div
                            @click="onTogglePlay"
                            class="border-r dark:border-gray-700 items-center flex justify-center gap-1 hover:bg-gray-50 dark:hover:bg-gray-800 py-2 cursor-pointer text-gray-500"
                        >
                            <UIcon
                                :name="isPlaying ? 'i-ic-outline-pause-circle' : 'i-ic-outline-play-circle'"
                                class="text-lg"
                            />
                            {{ $t('Sample') }}
                        </div>
                        <div
                            @click="
                                unAvailable
                                    ? null
                                    : user
                                    ? $emit('toggle-favorite', props.id, props.is_favorite)
                                    : router.push({ path: '/signin' })
                            "
                            class="border-r dark:border-r-gray-800 items-center flex justify-center gap-1 hover:bg-gray-50 dark:hover:bg-gray-800 py-2 cursor-pointer text-gray-500"
                        >
                            <UIcon v-if="updating" name="i-eos-icons-loading" class="text-lg" />

                            <UIcon
                                v-else-if="is_favorite"
                                name="i-material-symbols-favorite"
                                class="text-lg text-primary-500"
                            />
                            <UIcon v-else name="i-material-symbols-favorite-outline" class="text-lg" />
                            {{ updating ? $t('Updating...') : props.is_favorite ? $t('Favorite') : $t('Favorite') }}
                        </div>
                    </template>
                    <div
                        v-if="isMyVoicePage"
                        @click="onRemove"
                        class="dark:border-gray-700 items-center flex justify-center gap-1 hover:bg-gray-50 dark:hover:bg-gray-800 py-2 cursor-pointer text-gray-500"
                    >
                        <UIcon name="i-mdi-trash-can-outline" class="text-lg" />
                        {{ $t('Remove') }}
                    </div>
                </template>
            </div>
        </div>
        <!-- <audio
      preload="none"
      v-if="sample_audio_path"
      :src="sample_audio_path"
      ref="audio"
      @play="onPlay"
      @pause="onPause"
    /> -->
    </UCard>
</template>

<script setup lang="ts">
import type { TrainingStatusEnum } from '~/types'

import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'

import { useDialogsStore } from '~/stores/dialogs'
import BaseAccentSelect from '~/components/Base/BaseAccentSelect.vue'

dayjs.extend(utc)
dayjs.extend(relativeTime)

const runtimeConfig = useRuntimeConfig()
const appStore = useAppStore()
const dialogsStore = useDialogsStore()
const voiceLibraryStore = useVoiceLibraryStore()
const translateStore = useTranslateStore()
const { selectedVoiceLibrary, loadings } = storeToRefs(voiceLibraryStore)
const { removeVoiceById, fetchVoiceLibraryByType } = voiceLibraryStore
const { loading: loadingVoiceLibrary } = storeToRefs(appStore)
const { accent: accentStore } = storeToRefs(translateStore)
// const isPlaying = ref(false)
interface Voice {
    type?: string
    id?: string
    language?: string
    accent?: string
    speaker_name?: string
    gender?: string
    age?: string
    description?: string
    avatar?: { src: string }
    sample_audio_path?: string
    is_favorite?: boolean
    training_status?: string
    add_new_card?: boolean
    created_at?: string
    status?: number
    used_credit?: number
    badge?: string
}
interface Props extends Partial<Voice> {
    active?: boolean
    loading?: boolean
    updating?: boolean
    playing?: boolean
}
const props = defineProps<Props>()

const createdAtFormat = computed(() => {
    dayjs.locale(locale.value)
    return dayjs().to(dayjs.utc(props.created_at))
})
const unAvailable = computed(() => props.status !== 1 && isMyVoicePage.value)

const { t, locale } = useI18n()
const { getAccentByValue, getVoiceTrainingStatusByValue } = useVoiceLibrary(t)
const isPlaying = computed(() => props.playing && selectedVoiceLibrary.value?.id === props.id)
// const audio = ref<HTMLAudioElement>(new Audio(props.sample_audio_path))
// const onPlay = () => {
//     isPlaying.value = true
//     playingId.value = props.id || ''
// }

const authStore = useAuthStore()
const { user, isSuperUser } = storeToRefs(authStore)
const router = useRouter()
const toast = useToast()
const route = useRoute()
const isMyVoicePage = computed(() => props.type === 'user_voice')

// Remove voice
const onRemove = async () => {
    // if (unAvailable.value) return;
    dialogsStore.showConfirmDialog({
        title: t('Delete'),
        message: t('Are you sure you want to delete this voice?'),
        confirmButtonText: t('Delete'),
        cancelButtonText: t('Cancel'),
        onConfirm: async () => {
            loadingVoiceLibrary.value = true
            const remove = await removeVoiceById(props.id as string)
            if (remove) {
                await fetchVoiceLibraryByType(props.type as string, true)
                toast.add({
                    id: 'delete-voice',
                    color: 'primary',
                    title: t('Success'),
                    description: t('Deleted voice successfully'),
                    timeout: 6000,
                    icon: 'i-ooui:success',
                })
            } else {
                toast.add({
                    id: 'delete-voice',
                    color: 'primary',
                    title: t('Error'),
                    description: t('Deleted voice failed'),
                    timeout: 6000,
                    icon: 'i-ooui:success',
                })
            }
            loadingVoiceLibrary.value = false
        },
    })
}

// const onPause = () => {
//   isPlaying.value = false;
// };

const emits = defineEmits(['select', 'toggle-favorite', 'toggle-play'])

const onTogglePlay = () => {
    // if (isPlaying.value) {
    //     isPlaying.value = false
    //     audio.value.pause()
    // } else {
    //     isPlaying.value = true
    //     audio.value.play()
    // }
    if (unAvailable.value) return
    emits('toggle-play', props)
}

// Show accent select only for English system voices
const showAccentSelect = computed(() => {
    return props.type === 'system_voice' && props.active && route.name === 'index'
})

// Handle accent change
const onAccentChange = (_accent: string) => {
    accentStore.value = _accent
}

// watch(playingId, (id) => {
//   if (id !== props.id) {
//     isPlaying.value = false;
//     audio.value.pause();
//   }
// });
</script>

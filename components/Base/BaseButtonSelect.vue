<script setup lang="ts">
const props = defineProps<{
    options: any[]
    modelValue: string
}>()

const emit = defineEmits(['update:modelValue'])
const selected = computed({
    get: () => props.options?.findIndex((o) => o.value === props.modelValue) || 0,
    set: (value) => {
        emit('update:modelValue', props.options[value].value)
    },
})
</script>

<template>
    <UTabs
        :items="options"
        v-model="selected"
        :ui="{
            list: {
                marker: { base: 'border border-primary-300', background: '!bg-primary-300 !dark:bg-primary-700' },
                tab: { active: '!text-primary-600' },
            },
        }"
    >
        <template #default="{ item }">
            <div class="flex items-center gap-2 relative truncate">
                <UIcon :name="item.icon" class="w-5 h-5 flex-shrink-0" />

                <span class="truncate">{{ item.label }}</span>
            </div>
        </template>
    </UTabs>
</template>

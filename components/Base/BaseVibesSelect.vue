<script setup lang="ts">
const inputTextProStore = useInputTextProStore()
const { recentVibes, loadings, customVibes, vibes, vibe, errors, emotion } = storeToRefs(inputTextProStore)
const authStore = useAuthStore()
const { isLoggedIn } = storeToRefs(authStore)
const { t } = useI18n()
const isOpen = ref(false)
const mode = ref('form')
const toast = useToast()
const dialogsStore = useDialogsStore()
const vibesListSorted = computed(() =>
    vibes.value
        ?.map((vibe) => ({
            id: vibe.id,
            label: t(vibe.vibe),
            description: vibe.prompt,
            name: vibe.vibe,
            ...vibe,
        }))
        .sort((a, b) => a.label.localeCompare(b.label))
)

const props = defineProps<{
    modelValue: any
}>()

const emits = defineEmits(['update:modelValue'])

const selected = computed({
    get: () => props.modelValue,
    set: (value) => {
        emits('update:modelValue', value)
    },
})

const selectedVibe = computed(() => {
    if (loadings.value['fetchVibes']) {
        return t('Loading...')
    }
    const selectedVibe = [...vibesListSorted.value, ...customVibes.value].find(
        (vibe) => vibe.id === props.modelValue?.id
    )
    return selectedVibe ? t(selectedVibe.vibe || '') : t('Vibes')
})

const onSelectVibe = (vibe: any) => {
    // check if emotion is selected, show dialog confirm
    if (emotion.value && props.modelValue?.id !== vibe.id) {
        dialogsStore.showConfirmDialog({
            title: t('Warning'),
            message: t(
                'You have selected an emotion. If you change the vibe, the emotion will be removed. Do you want to continue?'
            ),
            confirmButtonText: t('Yes'),
            cancelButtonText: t('No'),
            onConfirm: () => {
                isOpen.value = false
                emits('update:modelValue', vibe)
                emotion.value = null
                inputTextProStore.updateRecentVibes(vibe?.id)
                return true
            },
            onCancel: () => {
                isOpen.value = true
                return true
            },
        })
    } else {
        isOpen.value = false
        if (props.modelValue?.id === vibe.id) {
            emits('update:modelValue', null)
        } else {
            emits('update:modelValue', vibe)
            inputTextProStore.updateRecentVibes(vibe?.id)
        }
    }

    // copy name to clipboard
    // navigator.clipboard.writeText(vibe.name)
}

const emotionGroups = computed(() => {
    const groups = [
        {
            key: 'vibes',
            label: t('Vibes'),
            commands: vibesListSorted.value.filter((vibe) => !recentVibes.value.includes(vibe.id)),
        },
    ] as any[]

    if (recentVibes.value.length) {
        groups.unshift({
            key: 'recent',
            label: t('Recent used'),
            commands: recentVibes.value
                .map((vibe_id) => {
                    const vibe = vibesListSorted.value.find((e) => e.id === vibe_id)
                    return vibe
                })
                .filter((vibe) => vibe),
        })
    }

    if (customVibes.value.length && isLoggedIn.value) {
        groups.push({
            key: 'custom',
            label: t('Custom Vibes'),
            commands: customVibes.value.map((vibe) => ({
                id: vibe.id,
                label: vibe.vibe,
                description: vibe.prompt,
                name: vibe.vibe,
                type: vibe.type,
                ...vibe,
            })),
        })
    }
    return groups
})

onMounted(() => {
    inputTextProStore.fetchVibes()
    if (isLoggedIn.value) {
        inputTextProStore.fetchCustomVibes()
    }
})

// watch isOpen
watch(
    () => isOpen.value,
    (newValue) => {
        if (newValue) {
            mode.value = 'list'
            if (isLoggedIn.value) {
                inputTextProStore.fetchCustomVibes()
            }
        }
    }
)

const onAddNew = () => {
    mode.value = 'form'
}

const formState = ref({
    id: null,
    name: '',
    prompt: '',
})

const onSubmit = async () => {
    if (!formState.value.name || !formState.value.prompt) {
        toast.add({
            id: 'create-vibe-error',
            title: t('Error'),
            description: t('Please fill in all fields.'),
            icon: 'material-symbols:error-outline',
            color: 'red',
        })
        return
    }
    if (formState.value.id) {
        // update existing vibe
        const res = await inputTextProStore.updateCustomVibe(formState.value)
        if (res) {
            formState.value = {
                id: null,
                name: '',
                prompt: '',
            }
            toast.add({
                id: 'update-vibe',
                title: t('Vibe updated'),
                description: t('Your vibe has been updated successfully.'),
                icon: 'material-symbols:check-circle',
                color: 'green',
            })
            mode.value = 'list'
        }
    } else {
        // create new vibe
        const res = await inputTextProStore.createCustomVibe(formState.value)
        if (res) {
            isOpen.value = false
            formState.value = {
                id: null,
                name: '',
                prompt: '',
            }
            toast.add({
                id: 'create-vibe',
                title: t('Vibe created'),
                description: t('Your vibe has been created successfully.'),
                icon: 'material-symbols:check-circle',
                color: 'green',
            })
        }
    }
}

const onDeleteCustomVibe = async (command: any) => {
    dialogsStore.showConfirmDialog({
        title: t('Delete'),
        message: t('Are you sure you want to delete this vibe?'),
        confirmButtonText: t('Delete'),
        cancelButtonText: t('Cancel'),
        onConfirm: async () => {
            isOpen.value = true

            const resul = await inputTextProStore.deleteCustomVibe(command)

            if (resul) {
                toast.add({
                    id: 'delete-vibe',
                    color: 'primary',
                    title: t('Success'),
                    description: t('Deleted vibe successfully'),
                    timeout: 3000,
                    icon: 'i-ooui:success',
                })
                // if vibe is selected, clear it
                if (vibe.value?.id === command?.id) {
                    emits('update:modelValue', '')
                }
            } else {
                toast.add({
                    id: 'delete-vibe',
                    color: 'red',
                    title: t('Error'),
                    description:
                        t('Deleted vibe failed') + '. ' + t(errors.value.removePrompt?.data?.detail?.error_code || ''),
                    timeout: 3000,
                    icon: 'i-ooui:success',
                })
            }
        },
        onCancel: () => {
            dialogsStore.onCloseConfirmDialog()
            isOpen.value = true
            return true
        },
    })
}

const onEditCustomVibe = (command: any) => {
    formState.value = {
        id: command.id,
        name: command.vibe,
        prompt: command.prompt,
    }
    mode.value = 'form'
}

const copiedId = ref<string | null>(null)

const copyVibeId = async (id: string, event?: Event) => {
    if (event) {
        event.stopPropagation()
    }
    try {
        await navigator.clipboard.writeText(id)
        copiedId.value = id
        const toast = useToast()
        toast.add({
            title: t('Success'),
            description: t('Vibe ID copied to clipboard'),
            color: 'green',
            icon: 'i-heroicons-check-circle-20-solid',
            timeout: 2000,
        })
        setTimeout(() => {
            copiedId.value = null
        }, 2000)
    } catch (err) {
        console.error('Failed to copy vibe ID:', err)
    }
}
</script>

<template>
    <div class="w-full">
        <UButton
            size="xs"
            color="white"
            variant="solid"
            :trailing="false"
            @click="isOpen = true"
            :ui="{
                rounded: 'rounded-full',
                icon: {
                    base: props.modelValue?.id ? 'text-primary-500' : 'text-gray-500',
                },
            }"
            class="group"
            :loading="loadings['fetchVibes']"
            block
        >
            <span
                class="truncate"
                :class="{
                    'text-primary-500': props.modelValue?.id,
                }"
            >
                {{ selectedVibe }}
            </span>
            <template #leading>
                <div class="w-3 h-3 flex items-center justify-center">
                    <UIcon
                        v-if="loadings['fetchVibes']"
                        name="i-eos-icons-loading"
                        class="absolute w-4 h-4 text-gray-500"
                    />
                    <div v-else-if="props.modelValue" class="absolute w-5 h-5 flex items-center justify-center">
                        <UIcon name="game-icons:drama-masks" class="text-xl text-primary-500" />
                    </div>
                    <div v-else class="absolute w-5 h-5 flex items-center justify-center">
                        <UIcon name="lucide:drama" class="text-xl text-gray-500" />
                    </div>
                </div>
            </template>
        </UButton>
        <UModal
            v-model="isOpen"
            :ui="{
                width: 'sm:max-w-3xl',
                container: 'items-start',
                height: 'max-h-[80vh] overflow-auto scrollbar-thin',
            }"
        >
            <UButton
                v-if="mode === 'list' && isLoggedIn"
                icon="ic:baseline-plus"
                size="sm"
                color="white"
                :label="$t('Add new Vibe')"
                :trailing="false"
                class="w-fit absolute right-4 top-2 z-20"
                @click="onAddNew"
            />
            <UCommandPalette
                v-if="mode === 'list'"
                :model-value="selected"
                :fuse="{
                    resultLimit: 100,
                    fuseOptions: {
                        ignoreLocation: true,
                        includeMatches: true,
                        threshold: 0,
                        keys: ['id', 'description'],
                    },
                }"
                :groups="emotionGroups"
                :placeholder="$t('Search vibes...')"
                icon="lucide:drama"
                :autoselect="false"
                :autoclear="false"
                :empty-state="{
                    icon: 'ri:vibe-sad-line',
                    label: $t(`We couldn't find any vibes.`),
                    queryLabel: $t(`We couldn't find any vibes with that term.`),
                }"
                :ui="{
                    group: {
                        command: {
                            base: 'group relative',
                        },
                    },
                    container: 'scrollbar-thin',
                }"
            >
                <template
                    v-for="group in emotionGroups"
                    :key="group.key"
                    v-slot:[`${group.key}-command`]="{ command, selected }"
                >
                    <div class="flex flex-row items-center gap-2" @click="onSelectVibe(command)">
                        <div class="flex flex-col w-full">
                            <div class="flex items-center gap-1">
                                <span class="text-sm font-semibold">{{ command.label }} </span>
                                <span v-if="selected" class="italic font-light text-xs text-orange-500">
                                    ({{ $t('Click again to unuse this vibe') }})
                                </span>
                            </div>
                            <div class="flex justify-between items-center w-full">
                                <span class="text-xs text-gray-500 dark:text-gray-400 whitespace-break-spaces">{{
                                    command.description
                                }}</span>
                            </div>
                        </div>
                        <UTooltip :text="$t('Click to copy ID')" class="absolute top-2 right-2">
                            <UButton
                                size="2xs"
                                color="gray"
                                variant="soft"
                                class="animate__animated"
                                :class="copiedId === command.id ? 'animate__flash' : ''"
                                @click.stop="copyVibeId(command.id, $event)"
                            >
                                <template #leading>
                                    <UIcon
                                        :name="
                                            copiedId === command.id
                                                ? 'i-tabler-copy-check-filled'
                                                : 'i-heroicons-clipboard'
                                        "
                                        class="text-xs"
                                        :class="copiedId === command.id ? 'text-green-500' : 'text-gray-500'"
                                    />
                                </template>
                                <span class="text-xs truncate">ID: {{ command.id }}</span>
                            </UButton>
                        </UTooltip>
                    </div>
                </template>
                <template
                    v-for="group in emotionGroups"
                    :key="group.key"
                    v-slot:[`${group.key}-inactive`]="{ command }"
                >
                    <div
                        class="flex sm:hidden flex-row items-center gap-1 sm:gap-2 sm:group-hover:flex sm:absolute right-0 px-0 sm:px-2"
                    >
                        <UButton
                            v-if="command.type === 'user_vibe'"
                            size="2xs"
                            icon="material-symbols:contract-edit-outline-sharp"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onEditCustomVibe(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Edit') }}
                            </span>
                        </UButton>
                        <UButton
                            v-if="command.type === 'user_vibe'"
                            size="2xs"
                            icon="material-symbols:delete-outline"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    base: 'text-red-500',
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onDeleteCustomVibe(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Delete') }}
                            </span>
                        </UButton>
                        <UButton
                            size="2xs"
                            icon="material-symbols:check-circle"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    base: 'text-primary-500',
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onSelectVibe(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Use') }}
                            </span>
                        </UButton>
                    </div>
                </template>

                <template v-for="group in emotionGroups" :key="group.key" v-slot:[`${group.key}-active`]="{ command }">
                    <div
                        class="flex sm:hidden flex-row items-center gap-1 sm:gap-2 sm:group-hover:flex sm:absolute right-0 px-0 sm:px-2"
                    >
                        <UButton
                            v-if="command.type === 'user_vibe'"
                            size="2xs"
                            icon="material-symbols:contract-edit-outline-sharp"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onEditCustomVibe(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Edit') }}
                            </span>
                        </UButton>
                        <UButton
                            v-if="command.type === 'user_vibe'"
                            size="2xs"
                            icon="material-symbols:delete-outline"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    base: 'text-red-500',
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onDeleteCustomVibe(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Delete') }}
                            </span>
                        </UButton>
                        <UButton
                            size="2xs"
                            icon="material-symbols:check-circle"
                            color="white"
                            variant="solid"
                            square
                            :ui="{
                                square: {
                                    '2xs': 'p-1 sm:px-2 sm:py-1',
                                },
                                icon: {
                                    base: 'text-primary-500',
                                    size: {
                                        '2xs': 'w-6 h-6 sm:w-4 sm:h-4',
                                    },
                                },
                            }"
                            @click="onSelectVibe(command)"
                        >
                            <span class="hidden sm:block">
                                {{ $t('Use') }}
                            </span>
                        </UButton>
                    </div>
                </template>
            </UCommandPalette>
            <div v-else-if="mode === 'form'" class="p-4 flex flex-col gap-4 mt-2">
                <div class="text-lg font-semibold text-center">
                    {{ formState.id ? $t('Edit your custom vibe') : $t('Create a new custom vibe') }}
                </div>
                <UFormGroup
                    :label="$t('Vibe name')"
                    :help="$t('This name will help you identify your vibe.')"
                    :ui="{
                        help: 'text-xs',
                    }"
                >
                    <UInput
                        ref="promptNameRef"
                        :placeholder="$t('Ex: Santa Claus')"
                        :ui="{
                            rounded: 'rounded-sm',
                        }"
                        v-model="formState.name"
                        :maxLength="50"
                    >
                    </UInput>
                    <template #hint>
                        <div class="text-xs">{{ formState.name?.length || 0 }} / 50</div>
                    </template>
                </UFormGroup>
                <UFormGroup :label="$t('Vibe prompt')">
                    <UTextarea
                        autoresize
                        v-model="formState.prompt"
                        :placeholder="$t('Enter your vibe prompt here.')"
                        class="w-full"
                        :maxrows="15"
                        :maxLength="500"
                        :ui="{
                            base: 'scrollbar-thin',
                        }"
                    />

                    <template #hint>
                        <div class="text-xs">{{ formState.prompt?.length }} / 500</div>
                    </template>
                </UFormGroup>
                <div class="flex flex-row items-center justify-center gap-2">
                    <UButton
                        :label="$t('Discard')"
                        size="sm"
                        icon="material-symbols:cancel-outline-rounded"
                        color="gray"
                        variant="soft"
                        @click="mode = 'list'"
                        :disabled="loadings.createCustomVibe || loadings.updateCustomVibe"
                    />
                    <UButton
                        :label="$t('Ok, save it!')"
                        size="sm"
                        icon="bi:send-check"
                        color="primary"
                        :loading="loadings.createCustomVibe || loadings.updateCustomVibe"
                        @click="onSubmit"
                    />
                </div>
            </div>
            <!-- <div class="p-4 text-xs text-gray-500 dark:text-gray-400">
                ※
                {{
                    $t(
                        'Because we use AI to generate the audio, sometimes the audio may not be perfect as expected. We are working hard to improve the quality of the audio. Thank you for your understanding.'
                    )
                }}
            </div> -->
        </UModal>
    </div>
</template>

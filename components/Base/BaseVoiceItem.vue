<template>
    <div
        class="group pr-6 pl-4 py-2 border-t dark:border-t-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
        @click="emits('select', value)"
        :class="{
            'bg-gray-100/80 hover:bg-gray-100 dark:bg-gray-800 border-l-2 border-l-primary-600 rounded-l-lg':
                active,
            '': active,
        }"
    >
        <div class="flex items-start gap-2.5">
            <div class="flex flex-col gap-1 flex-1">
                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                    <span class="text-sm font-semibold text-gray-900 dark:text-white">
                        {{ $t(text) }}
                    </span>
                </div>
                <span class="text-xs font-normal text-gray-500 dark:text-gray-400">
                    {{ $t(description) }}
                </span>
                <div class="flex flex-col w-full leading-1.5 py-2 rounded-e-xl rounded-es-xl">
                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                        <UButton
                            :icon="isPlaying ? 'i-solar-pause-bold' : 'i-solar-play-bold'"
                            size="xs"
                            :color="isPlaying ? 'primary' : 'gray'"
                            square
                            variant="soft"
                            @click="toggleAudio"
                        />
                        <svg
                            :key="`${locale}_${props.value}`"
                            class="w-full items-center"
                            :class="[`h-[${containerHeight}px]`]"
                            aria-hidden="true"
                            viewBox="0 0 500 40"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <rect
                                v-for="rect in audioVisualizer"
                                v-bind="rect"
                                class="cursor-pointer fill-gray-300 dark:fill-gray-600"
                                :class="{
                                    '!dark:fill-primary-500 !fill-primary-600': currentDuration > rect.value,
                                }"
                                @click="onFowwardTo(rect.value / 10)"
                            />
                        </svg>
                    </div>
                </div>
            </div>
            <UChip
                :show="active"
                size="md"
                position="bottom-right"
                class="transition-all duration-200"
                :class="{
                    'group-hover:scale-125 ': !isPlaying,
                }"
                inset
                :ui="{ base: '-mx-1 rounded-none ring-0', background: '' }"
            >
                <div
                    class="relative h-11 w-11 rounded-full"
                    :class="{
                        ' border-2 border-primary-500': active,
                    }"
                >
                    <span
                        v-if="isPlaying"
                        class="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-600 opacity-30"
                    ></span>
                    <UAvatar :src="`/assets/images/avatars/${value}.svg`" :alt="text" size="md" />
                </div>
                <template #content>
                    <UIcon name="i-heroicons-check-circle-solid" class="text-primary-500 text-xl" />
                </template>
            </UChip>
        </div>
        <audio :src="sound" ref="audio" @play="onPlay" @pause="onPause" @loadeddata="onLoaded" />
    </div>
</template>

<script setup lang="ts">
import en_alloy from '~/assets/audio/en/alloy.mp3'
import vi_alloy from '~/assets/audio/vi/alloy.mp3'


const props = defineProps<{
    text: string
    value: string
    description: string
    active: boolean
}>()
const emits = defineEmits(['select'])
const audioSource = {
    en_alloy: 'https://cdn.openai.com/API/docs/audio/alloy.wav',
    en_echo: 'https://cdn.openai.com/API/docs/audio/echo.wav',
    en_fable: 'https://cdn.openai.com/API/docs/audio/fable.wav',
    en_onyx: 'https://cdn.openai.com/API/docs/audio/onyx.wav',
    en_nova: 'https://cdn.openai.com/API/docs/audio/nova.wav',
    en_shimmer: 'https://cdn.openai.com/API/docs/audio/shimmer.wav',
    vi_alloy: vi_alloy,
} as { [key: string]: string }

const { locale } = useI18n()

const sound = computed(() => {
    return audioSource[`en_${props.value}`]
})

const audio = ref<HTMLAudioElement>(new Audio(sound.value))
const isPlaying = ref(false)

const currentDuration = ref(0)
const onPlay = () => {
    isPlaying.value = true
    audio.value.addEventListener('timeupdate', () => {
        currentDuration.value = audio.value.currentTime * 10
    })
}

const onPause = () => {
    isPlaying.value = false
    audio.value.removeEventListener('timeupdate', () => {
        currentDuration.value = audio.value.currentTime * 10
    })
}

const toggleAudio = () => {
    if (audio.value?.paused) {
        audio.value.play()
    } else {
        audio.value.pause()
    }
}

// <rect x="7" y="15.5" width="3" height="9" rx="1.5" fill="#6B7280" class="dark:fill-white" />
// create audio visualizer with svg depending on the audio file, with x, y, width, height, rx, fill, and class
const random = (min: number, max: number) => Math.floor(Math.random() * (max - min + 1) + min)
const containerHeight = 40
const audioVisualizer = ref([]) as Ref<
    | {
          x: number
          y: number
          width: number
          height: number
          rx: number
          class: string
          value: number
      }[]
    | []
>

const onLoaded = () => {
    currentDuration.value = 0
    const rect = []
    const lengthAudio = audio.value?.duration * 10
    for (let i = 0; i < lengthAudio; i++) {
        const height = random(0, containerHeight)
        const y = Math.round((containerHeight - height) / 2)
        rect.push({
            value: i,
            x: i * 7,
            y: y,
            width: 4,
            height: height,
            rx: 3,
            class: 'dark:fill-white',
        })
    }
    audioVisualizer.value = rect
}

const onFowwardTo = (time: number) => {
    audio.value.currentTime = time
    // audio.value.play()
}
</script>

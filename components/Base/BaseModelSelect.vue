<script setup lang="ts">
const { t } = useI18n()

const modelList = computed(() => [
    {
        value: 'audio_stable',
        icon: 'ri:voice-ai-fill',
        label: t('Audio Stable'),
        description: t('Your audio will be processed with the latest stable model.'),
    },
    {
        value: 'audio_beta',
        icon: 'streamline:interface-id-voice-scan-identification-secure-id-soundwave-sound-voice-brackets-security',
        label: t('Audio Beta'),
        description: t('Your audio will be processed with the latest beta model.'),
    },
])

const props = defineProps<{
    modelValue: string
}>()

const emits = defineEmits(['update:modelValue'])

const selected = computed({
    get: () => props.modelValue,
    set: (value) => {
        emits('update:modelValue', value)
    },
})
</script>

<template>
    <USelectMenu
        :ui="{
            rounded: 'rounded-full',
            icon: {
                base: 'text-primary-500 dark:text-primary-400',
                leading: {
                    wrapper: '!text-gray-500',
                },
            },
            wrapper: 'w-full',
        }"
        :ui-menu="{
            width: 'w-screen max-w-64',
        }"
        :placeholder="$t('Model')"
        v-model="selected"
        :trailing="false"
        :options="modelList"
        valueAttribute="value"
        size="xs"
        :leading-icon="props.modelValue ? 'material-symbols:check-circle' : 'carbon:ibm-engineering-workflow-mgmt'"
        :popper="{ placement: 'bottom-start' }"
    >
        <template #option="{ option: model }">
            <div class="flex items-start flex-row gap-3">
                <UIcon :name="model.icon" class="text-2xl" />
                <div class="flex flex-col">
                    <div class="font-semibold">{{ model.label }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ model.description }}</div>
                </div>
            </div>
        </template>
    </USelectMenu>
</template>

<template>
    <div>
        <UFormGroup
            :label="$t('Accent')"
            class="mb-3"
            :ui="{
                label: {
                    base: 'text-xs',
                },
            }"
        >
            <USelectMenu
                size="xs"
                v-model="selectedAccent"
                :options="voiceCountries"
                :placeholder="$t('Select Accent')"
                :popper="{
                    placement: 'bottom-start',
                }"
                value-attribute="value"
                label-attribute="label"
            >
                <template #label>
                    <span v-if="selectedAccent" class="truncate capitalize">
                        {{ getCountryByValue(selectedAccent)?.label }}
                    </span>
                    <span v-else>
                        {{ $t('Select Accent') }}
                    </span>
                </template>
                <template #option="{ option: country }">
                    <div class="flex items-start flex-row gap-3">
                        <UIcon :name="country.icon" class="text-2xl" />
                        <div class="flex flex-col">
                            <div class="font-semibold">{{ country.label }}</div>
                        </div>
                    </div>
                </template>
            </USelectMenu>
            <template #hint>
                <!-- create remove button to remove selected -->
                <UButton
                    v-if="selectedAccent"
                    size="xs"
                    color="red"
                    icon="i-heroicons-x-mark-20-solid"
                    variant="ghost"
                    :label="$t('Remove accent')"
                    @click="selectedAccent = ''"
                />
            </template>
        </UFormGroup>
        <UAlert
            v-if="showWarning"
            :title="$t('Warning')"
            color="yellow"
            variant="subtle"
            icon="i-heroicons-exclamation-triangle"
            class="mt-2"
        >
            <template #description>
                {{
                    $t(
                        'Accent only works well with English text. If you use other languages and it does not work as expected, please unselect the accent.'
                    )
                }}
            </template>
        </UAlert>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useVoiceLibrary } from '~/composables/useVoiceLibrary'
import { useTranslateStore } from '~/stores/translate'

const props = defineProps<{
    voiceType?: string
    language?: string
    value: string
}>()

const emit = defineEmits(['update:accent'])

const { t } = useI18n()
const { voiceCountries, getCountryByValue } = useVoiceLibrary(t)
const translateStore = useTranslateStore()

const selectedAccent = ref(props.value)

const showWarning = computed(() => {
    return selectedAccent.value
})

watch(selectedAccent, (newValue) => {
    if (newValue) {
        showWarning.value = true
        const country = getCountryByValue(newValue)
        emit('update:accent', country?.input || '')
    } else {
        showWarning.value = false
        emit('update:accent', '')
    }
})
</script>

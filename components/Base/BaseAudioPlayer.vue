<template>
  <UCard class="mx-auto max-w-lg w-full bg-white/75 dark:bg-gray-900/80 backdrop-blur">
    <div class="flex flex-row items-center">
      <div class="flex-1 flex flex-col pr-8 mb-2">
        <div class="text-lg font-semibold">{{ data?.speaker_name || $t(voiceObject?.speaker_name || "") }}</div>
        <div v-if="data?.created_at" class="text-xs font-light">
          {{ $t("Created at {date}", { date: formatDatetime(data?.created_at) }) }}
        </div>
      </div>
      <div class="relative h-10 w-20">
        <div
          class="absolute flex flex-row items-center justify-center bottom-4 ring-1 ring-gray-200 dark:ring-gray-800 right-0 h-24 w-24 bg-white/75 backdrop-blur dark:bg-gray-900/80 rounded-full"
        >
          <span
            v-if="isPlaying"
            class="animate-ping absolute inline-flex h-full w-full rounded-full bg-primary-200 dark:bg-primary-900 opacity-20"
          ></span>
          <UAvatar
            :src="voiceObject.avatar.src"
            :icon="
              getAccentByValue(voiceObject?.accent || '')?.icon ||
              'i-iconoir-voice-circle'
            "
            size="3xl"
            :class="{
              innertape2: isPlaying,
            }"
            :ui="{ icon: { size: { '3xl': 'w-10 h-10' } } }"
          />
        </div>
      </div>
    </div>

    <div v-if="url" class="flex flex-row items-center gap-2">
      <div
        v-if="loadingAudio"
        class="flex flex-1 md:flex-row gap-2 flex-col justify-start items-start text-sm"
      >
        <UIcon name="i-eos-icons:loading" class="md:text-2xl text-4xl" />

        <div>
          {{ $t("Loading audio...") }}
        </div>
      </div>
      <audio
        v-show="!loadingAudio"
        :key="url"
        controls
        class="flex-1 rounded-full bg-primary-300"
        @play="onPlay"
        @pause="onPause"
        @loadeddata="onLoaded"
        :controlsList="canAccess(AccessEnum.IS_DOWNLOAD) ? '' : 'nodownload'"
      >
        <source :src="url" type="audio/mpeg" />
        Your browser does not support the audio element.
      </audio>
      <a v-if="canAccess(AccessEnum.IS_DOWNLOAD)" @click="onDownload" class="cursor-pointer">
        <UIcon name="i-material-symbols:download" class="text-2xl" />
      </a>
    </div>
    <div
      v-else
      class="my-2 pb-3 flex md:flex-row gap-2 flex-col justify-center items-center text-sm"
    >
      <UIcon name="i-eos-icons:loading" class="md:text-2xl text-4xl" />

      <i18n-t
        keypath="We're processing the audio. Please wait a moment. Or you can {0} this page and come back later. ({1})"
        tag="p"
      >
        <a
          @click="onBookmark"
          class="font-semibold underline cursor-pointer text-primary-500"
          >{{ $t("bookmark") }}</a
        >
        <span>
          {{ diffFromNowFormatted }}
        </span>
      </i18n-t>
    </div>
    <ins
      v-if="showAds"
      class="adsbygoogle h-auto w-full mt-4"
      style="display: block"
      data-ad-format="fluid"
      data-ad-layout-key="-ed+6k-30-ac+ty"
      data-ad-client="ca-pub-****************"
      data-ad-slot="4248556873"
    ></ins>
    <slot name="description" />
    <UDivider />
    <div v-if="showScript" class="mt-4 text-sm whitespace-break-spaces break-words">
      <div class="font-semibold mb-2">{{ $t("Input Text") }}:</div>
      {{ data?.tts_input }}
    </div>
  </UCard>
</template>

<script setup lang="ts">
import { saveAs } from "file-saver";
import { AccessEnum } from '~/types'

const props = defineProps({
  url: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  showScript: {
    type: Boolean,
    default: true,
  },
});
const translateStore = useTranslateStore();
const authStore = useAuthStore();
const { isPlaying, voiceObject } = storeToRefs(translateStore);
const { canAccess, showAds } = storeToRefs(authStore);
const { checkAccessBeforeAction } = useAuthStore()
const onPlay = () => {
  isPlaying.value = true;
};

const onPause = () => {
  isPlaying.value = false;
};

const onDownload = () => {
  checkAccessBeforeAction(AccessEnum.IS_DOWNLOAD, () => {
    saveAs(props.url, `${props?.data?.uuid}.mp3`);
  })
};
const { t } = useI18n();
const onBookmark = () => {
  // book mark by browser
  if (process.client) {
    if (window.sidebar && window.sidebar.addPanel) {
      // Mozilla Firefox Bookmark
      window.sidebar.addPanel(document.title, window.location.href, "");
    } else if (window.external && "AddFavorite" in window.external) {
      // IE Favorite
      window.external.AddFavorite(location.href, document.title);
    } else if (window.opera && window.print) {
      // Opera Hotlist
      this.title = document.title;
      return true;
    } else {
      // webkit - safari/chrome
      alert(
        t("Press {key}+D to bookmark this page.", {
          key: navigator.platform.match("Mac") ? "⌘" : "Ctrl",
        })
      );
    }
  }
};

const diffFromNow = ref(0);
setInterval(() => {
  diffFromNow.value = +diffDatetime(props?.data?.created_at);
}, 1000);

const diffFromNowFormatted = computed(() => {
  // format diffFromNow (seconds) to HH:MM:SS
  const hours = Math.floor(diffFromNow.value / 3600);
  const minutes = Math.floor((diffFromNow.value % 3600) / 60);
  const seconds = Math.floor(diffFromNow.value % 60);

  // check if hours, minutes, seconds are less than 10, add 0 before
  const hoursFormatted = hours < 10 ? `0${hours}` : hours;
  const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes;
  const secondsFormatted = seconds < 10 ? `0${seconds}` : seconds;

  if (hours > 0) {
    return `${hoursFormatted}:${minutesFormatted}:${secondsFormatted}`;
  } else {
    return `${minutesFormatted}:${secondsFormatted}`;
  }
});

const loadingAudio = ref(false);
const onLoaded = () => {
  loadingAudio.value = false;
};

onMounted(() => {
  loadingAudio.value = true;

  // check if device is IOS
  if (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) {
    loadingAudio.value = false;
  }
});

const { getAccentByValue } = useVoiceLibrary(t);
</script>

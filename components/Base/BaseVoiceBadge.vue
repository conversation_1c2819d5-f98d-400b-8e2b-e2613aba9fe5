<template>
  <UBadge
    color="white"
    class="h-7"
    variant="solid"
    :ui="{ rounded: 'rounded-full', size: { sm: 'pl-1 pr-2' } }"
    size="sm"
  >
    <div class="flex flex-row items-center gap-1">
      <div>
        <UAvatar
          :src="voice?.avatar?.src"
          :icon="getAccentByValue(voice?.accent || '')?.icon || 'i-iconoir-voice-circle'"
          size="xs"
          :ui="{ icon: { size: { xs: 'w-4 h-4' } }, size: { xs: 'h-4 w-4'} }"
          :class="{
            'text-xl': voice_id,
            'text-lg text-gray-400': !voice_id,
          }"
        />
      </div>
      <div
        :class="{
          'text-gray-400': !voice_id,
        }"
      >
        {{ voice?.speaker_name || $t('Select voice') }}
      </div>
      <div
        class="text-xs text-gray-400"
        v-if="props.setting?.duration && props.setting?.duration > 0"
      >
        ({{ displayDuration(props.setting?.duration) }})
      </div>
      <!-- <div
        class="text-xs text-gray-400"
        v-else-if="props.voice_id && props.setting?.duration"
      >
        ({{ $t("auto") }})
      </div>  -->
    </div>
  </UBadge>
</template>

<script setup lang="ts">
import { ModeSelectDurationEnum } from '~/types';

const props = defineProps({
  voice_id: String,
  setting: Object,
  voice: Object,
});
const voiceLibraryStore = useVoiceLibraryStore();
const storyStore = useStoryStore()
const { getVoiceLibraryById } = storeToRefs(voiceLibraryStore);
const { t } = useI18n();
const { getAccentByValue } = useVoiceLibrary(t);

const { modeSelectDuration } = storeToRefs(storyStore)

// show duration
const displayDuration = (duration: number) => {
  if (modeSelectDuration.value === ModeSelectDurationEnum.AUTO) {
    return t('auto')
  }
  return `${duration || 0}s`
}
</script>

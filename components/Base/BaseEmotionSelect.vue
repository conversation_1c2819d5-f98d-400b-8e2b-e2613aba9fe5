<script setup lang="ts">
import IconDefault from '~/assets/icons/icon-default.svg'
import IconAngry from '~/assets/icons/icon-angry.svg'
import IconCrying from '~/assets/icons/icon-crying.svg'
import IconExcited from '~/assets/icons/icon-excited.svg'
import IconFrustrated from '~/assets/icons/icon-frustrated.svg'
import IconHappy from '~/assets/icons/icon-happy.svg'
import IconInsane from '~/assets/icons/icon-insane.svg'
import IconLaughing from '~/assets/icons/icon-laughing.svg'
import IconSad from '~/assets/icons/icon-sad.svg'

const { t } = useI18n()

const open = ref(true)

const emotionList = computed(() => [
    {
        value: 'happy',
        label: t('Happy'),
        icon: IconHappy,
        animate: 'animate__tada',
    },
    {
        value: 'excited',
        label: t('Excited'),
        icon: IconExcited,
        animate: 'animate__heartBeat',
    },
    {
        value: 'laughing',
        label: t('Laughing'),
        icon: IconLaughing,
        animate: 'animate__tada',
    },
    {
        value: 'sad',
        label: t('Sad'),
        icon: IconSad,
        animate: 'animate__heartBeat',
    },
    {
        value: 'crying',
        label: t('Crying'),
        icon: IconCrying,
        animate: 'animate__tada',
    },
    {
        value: 'frustrated',
        label: t('Frustrated'),
        icon: IconFrustrated,
        animate: 'animate__jello',
    },
    {
        value: 'angry',
        label: t('Angry'),
        icon: IconAngry,
        animate: 'animate__heartBeat',
    },
    {
        value: 'insane',
        label: t('Insane'),
        icon: IconInsane,
        animate: 'animate__rubberBand',
    },
])

const props = defineProps<{
    modelValue: string
}>()

const emits = defineEmits(['update:modelValue'])

const defaultEmotion = computed(() => ({
    value: '',
    label: t('Emotion'),
    icon: IconDefault,
}))

const selectedEmotion = computed(() => {
    const selectedEmotion = emotionList.value.find((emotion) => emotion.value === props.modelValue)
    return selectedEmotion || defaultEmotion.value
})

const onSelectEmotion = (emotion: { value: string }) => {
    open.value = false
    hoverIndex.value = null
    if (props.modelValue !== emotion.value) {
        emits('update:modelValue', emotion.value)
    } else {
        emits('update:modelValue', '')
    }
}

let hoverIndex = ref<number | null>(null)

const computeStyle = (index: number) => {
    if (hoverIndex.value === null) {
        return { transform: 'scale(1)', transition: 'transform 0.3s ease' }
    }

    const distance = Math.abs(hoverIndex.value - index) // Khoảng cách giữa item
    const scale = Math.max(1.7 - distance * 0.4, 0.8) // Gấp đôi với item được hover, giảm dần với khoảng cách
    return {
        transform: `scale(${scale})`,
        transition: 'transform 0.3s ease',
    }
}
</script>

<template>
    <UPopover v-model:open="open">
        <UButton
            size="2xs"
            color="white"
            :label="selectedEmotion.label"
            trailing-icon="i-heroicons-chevron-down-20-solid"
        >
            <template #leading>
                <component :is="selectedEmotion.icon" class="text-xl" />
            </template>
        </UButton>

        <template #panel>
            <div class="p-4 flex flex-row flex-wrap gap-5">
                <UTooltip
                    v-for="(emotion, index) in emotionList"
                    :key="emotion.value"
                    :text="emotion.label"
                    :popper="{ placement: 'top', offsetDistance: 16 }"
                    :ui="{
                        background: 'dark:bg-white bg-gray-900',
                        color: 'dark:text-gray-900 text-white',
                    }"
                >
                    <div
                        @mouseover="hoverIndex = index"
                        @mouseleave="hoverIndex = null"
                        @click="onSelectEmotion(emotion)"
                        :style="computeStyle(index)"
                        class="relative pb-1 px-1 transition-transform duration-500 cursor-pointer"
                        :class="{
                            'border-b-2  dark:border-primary-500': modelValue === emotion.value,
                        }"
                    >
                        <div class="flex flex-row items-center gap-2">
                            <component
                                :is="emotion.icon"
                                class="text-3xl animate__animated"
                                :class="{
                                    [emotion.animate]: hoverIndex === index,
                                }"
                            />
                        </div>
                    </div>
                </UTooltip>
            </div>
        </template>
    </UPopover>
</template>

<template>
    <div
        @click="isModalOpen = true"
        class="flex flex-row items-center pl-6 lg:pl-2 gap-2 lg:gap-0 lg:justify-center bg-white dark:bg-gray-900 lg:border-2 border-gray-200 dark:border-gray-700 rounded-md lg:rounded-none p-1 px-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
    >
        <UIcon name="hugeicons:ai-setting" class="text-xl lg:text-2xl" />
        <div class="block lg:hidden text-xs">
            <span>{{ $t('Audio setting') }}</span>
        </div>
    </div>

    <!-- Audio Settings Modal -->
    <UModal
        v-model="isModalOpen"
        :ui="{
            width: 'sm:max-w-md',
        }"
    >
        <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
            <template #header>
                <div class="flex items-center justify-between">
                    <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                        {{ $t('Audio Settings') }}
                    </h3>
                    <UButton
                        color="gray"
                        variant="ghost"
                        icon="i-heroicons-x-mark-20-solid"
                        class="-my-1"
                        @click="isModalOpen = false"
                    />
                </div>
            </template>

            <div class="space-y-6 p-4">
                <!-- Quality Setting -->
                <UFormGroup
                    v-if="route.name !== 'emotion-text'"
                    :label="$t('Quality')"
                    required
                >
                    <USelect
                        icon="i-material-symbols-high-quality-outline-rounded"
                        color="white"
                        size="md"
                        variant="outline"
                        :options="modelOptionsFormat"
                        :placeholder="$t('Select quality')"
                        v-model="model"
                        class="w-full"
                    />
                    <template #hint>
                        <a
                            class="flex text-xs dark:text-primary-400 text-primary-500 underline cursor-pointer flex-row items-center gap-1"
                            href="/pricing#quality"
                            target="_blank"
                        >
                            <UIcon name="i-ri-information-fill" />
                            {{ $t('What is this quality?') }}
                        </a>
                    </template>
                </UFormGroup>

                <!-- Output Format Setting -->
                <UFormGroup :label="$t('Output Format')" required>
                    <USelect
                        icon="ic:round-audio-file"
                        color="white"
                        size="md"
                        variant="outline"
                        :options="outputFormatOptionsFormat"
                        :placeholder="$t('Select format')"
                        v-model="outputFormat"
                        class="w-full"
                    />
                </UFormGroup>

                <!-- Output Channel Setting -->
                <UFormGroup
                    v-if="canUseOutputChannel"
                    :label="$t('Output Channel')"
                    required
                >
                    <USelect
                        icon="i-material-symbols-speaker-outline"
                        color="white"
                        size="md"
                        variant="outline"
                        :options="outputChannelOptionsFormat"
                        :placeholder="$t('Select channel')"
                        v-model="outputChannel"
                        class="w-full"
                    />
                </UFormGroup>
            </div>
        </UCard>
    </UModal>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'

const { t } = useI18n()
const route = useRoute()
const config = useRuntimeConfig()
const translateStore = useTranslateStore()
const authStore = useAuthStore()

const { usableModels, isSuperUser } = storeToRefs(authStore)
const {
    modelOptions,
    model,
    outputFormatOptions,
    outputFormat,
    outputChannelOptions,
    outputChannel
} = storeToRefs(translateStore)

const isModalOpen = ref(false)

// Feature flag logic: Allow access if feature flag is enabled OR user is superuser
const features = config.public.features
const canUseOutputChannel = computed(() => {
    return (features as any).outputChannel || isSuperUser.value
})

const modelOptionsFormat = computed(() => {
    return modelOptions.value.map((item) => {
        return {
            value: item.value,
            label: t(item.value),
            disabled: !usableModels.value.includes(item.value),
        }
    })
})

const outputFormatOptionsFormat = computed(() => {
    return outputFormatOptions.value.map((item) => {
        return {
            value: item.value,
            label: t(item.label),
            icon: item.icon,
        }
    })
})

const outputChannelOptionsFormat = computed(() => {
    return outputChannelOptions.value.map((item) => {
        return {
            value: item.value,
            label: t(item.label),
            icon: item.icon,
        }
    })
})
</script>
<template>
  <select
    :disabled="isUploading || showCustomPrompt"
    v-model="translateOptions.translateWritingStyle"
    class="text-xs font-medium border border-gray-300 dark:border-gray-600 bg-gray-300 dark:bg-gray-600 rounded-lg block w-full p-2.5 focus:ring-primary-500 dark:focus:ring-primary-500 dark:focus:border-primary-500"
    :class="
      isUploading || showCustomPrompt
        ? 'bg-gray-300 dark:bg-gray-600 text-gray-400 dark:text-gray-400 cursor-not-allowed'
        : 'text-gray-900 dark:text-white  hover:bg-gray-400 dark:hover:bg-gray-700'
    "
  >
    <option selected value="">{{ $t("Select writing style") }}</option>
    <option v-for="{ value, displayText } in options" :key="value" :value="value">
      {{ $t(displayText) }}
    </option>
  </select>
</template>

<script lang="ts" setup>
import _ from "lodash";
const translateStore = useTranslateStore();
const { translateOptions, isUploading, showCustomPrompt } = storeToRefs(translateStore);

const { t, locale } = useI18n({ useScope: "global" });

const options = _.orderBy(
  [
    { value: "Academic", text: "Academic" },
    { value: "Analytical", text: "Analytical" },
    { value: "Argumentative", text: "Argumentative" },
    { value: "Conversational", text: "Conversational" },
    { value: "Creative", text: "Creative" },
    { value: "Critical", text: "Critical" },
    { value: "Descriptive", text: "Descriptive" },
    { value: "Epigrammatic", text: "Epigrammatic" },
    { value: "Epistolary", text: "Epistolary" },
    { value: "Expository", text: "Expository" },
    { value: "Informative", text: "Informative" },
    { value: "Instructive", text: "Instructive" },
    { value: "Journalistic", text: "Journalistic" },
    { value: "Metaphorical", text: "Metaphorical" },
    { value: "Narrative", text: "Narrative" },
    { value: "Persuasive", text: "Persuasive" },
    { value: "Poetic", text: "Poetic" },
    { value: "Satirical", text: "Satirical" },
    { value: "Technical", text: "Technical" },
  ].map((item) => {
    return {
      ...item,
      displayText: t(item.text),
    };
  }),
  "displayText"
);
</script>

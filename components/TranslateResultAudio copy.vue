<template>
    <!-- drawer component -->
    <div
        id="drawer-swipe"
        class="group flex flex-col items-start md:items-center fixed z-40 transition-transform left-0 right-0 translate-y-full duration-300"
        tabindex="-1"
        aria-labelledby="drawer-swipe-label"
    >
        <div
            class="relative x-auto max-w-4xl w-full overflow-y-auto bg-white border shadow-xl border-gray-200 rounded-t-lg dark:border-gray-700 dark:bg-gray-800"
        >
            <div class="cursor-pointer hover:bg-gray-50 py-2 px-4 dark:hover:bg-gray-700" @click="drawer?.toggle()">
                <span
                    @click="drawer?.toggle()"
                    class="absolute w-8 h-1 -translate-x-1/2 bg-gray-300 rounded-lg top-3 left-1/2 dark:bg-gray-600"
                ></span>
                <h5
                    id="drawer-swipe-label"
                    class="inline-flex items-center text-base text-gray-500 dark:text-gray-400 font-medium"
                >
                    <UIcon name="i-bi-play-btn-fill" class="me-2 text-2xl" />
                    {{ $t('Audio Player') }}
                </h5>
            </div>
            <!-- <UButton
                class="absolute top-2 right-3"
                icon="i-heroicons-x-mark-solid"
                size="sm"
                color="red"
                square
                variant="soft"
                :ui="{ rounded: 'rounded-full' }"
            /> -->
            <div
                class="z-50 grid w-full h-28 grid-cols-1 px-8 bg-white border-t border-gray-200 md:grid-cols-12 dark:bg-gray-700 dark:border-gray-600"
            >
                <div class="md:col-span-3 items-center justify-center hidden me-auto md:flex space-x-2">
                    <UAvatar
                        :src="`/assets/images/avatars/${audioResultVoice?.value}.svg`"
                        :alt="audioResultVoice?.text"
                        size="md"
                    />
                    <span class="text-md font-semibold text-gray-500 dark:text-gray-400">
                        {{ audioResultVoice?.text }}
                    </span>
                </div>
                <div class="md:col-span-6 flex items-center w-full">
                    <div class="w-full">
                        <div class="flex items-center justify-center mx-auto mb-1">
                            <button
                                data-tooltip-target="tooltip-shuffle"
                                type="button"
                                class="p-2.5 group rounded-full hover:bg-gray-100 me-1 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                            >
                                <svg
                                    class="w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 20 18"
                                >
                                    <path
                                        stroke="currentColor"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M11.484 6.166 13 4h6m0 0-3-3m3 3-3 3M1 14h5l1.577-2.253M1 4h5l7 10h6m0 0-3 3m3-3-3-3"
                                    />
                                </svg>
                                <span class="sr-only">Shuffle video</span>
                            </button>
                            <div
                                id="tooltip-shuffle"
                                role="tooltip"
                                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                            >
                                Shuffle video
                                <div class="tooltip-arrow" data-popper-arrow></div>
                            </div>
                            <button
                                data-tooltip-target="tooltip-previous"
                                type="button"
                                class="p-2.5 group rounded-full hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                            >
                                <svg
                                    class="rtl:rotate-180 w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="currentColor"
                                    viewBox="0 0 12 16"
                                >
                                    <path
                                        d="M10.819.4a1.974 1.974 0 0 0-2.147.33l-6.5 5.773A2.014 2.014 0 0 0 2 6.7V1a1 1 0 0 0-2 0v14a1 1 0 1 0 2 0V9.3c.***************.177.194l6.5 5.773a1.982 1.982 0 0 0 2.147.33A1.977 1.977 0 0 0 12 13.773V2.227A1.977 1.977 0 0 0 10.819.4Z"
                                    />
                                </svg>
                                <span class="sr-only">Previous video</span>
                            </button>
                            <div
                                id="tooltip-previous"
                                role="tooltip"
                                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                            >
                                Previous video
                                <div class="tooltip-arrow" data-popper-arrow></div>
                            </div>
                            <UButton
                                data-tooltip-target="tooltip-pause"
                                @click="toggleAudio"
                                :icon="isPlaying ? 'i-heroicons-pause-solid' : 'i-heroicons-play-solid'"
                                size="lg"
                                :color="isPlaying ? 'primary' : 'gray'"
                                square
                                variant="solid"
                                :ui="{
                                    rounded: 'rounded-full',
                                }"
                            />
                            <div
                                id="tooltip-pause"
                                role="tooltip"
                                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                            >
                                {{ isPlaying ? 'Pause' : 'Play' }}
                                <div class="tooltip-arrow" data-popper-arrow></div>
                            </div>
                            <button
                                data-tooltip-target="tooltip-next"
                                type="button"
                                class="p-2.5 group rounded-full hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                            >
                                <svg
                                    class="rtl:rotate-180 w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="currentColor"
                                    viewBox="0 0 12 16"
                                >
                                    <path
                                        d="M11 0a1 1 0 0 0-1 1v5.7a2.028 2.028 0 0 0-.177-.194L3.33.732A2 2 0 0 0 0 2.227v11.546A1.977 1.977 0 0 0 1.181 15.6a1.982 1.982 0 0 0 2.147-.33l6.5-5.773A1.88 1.88 0 0 0 10 9.3V15a1 1 0 1 0 2 0V1a1 1 0 0 0-1-1Z"
                                    />
                                </svg>
                                <span class="sr-only">Next video</span>
                            </button>
                            <div
                                id="tooltip-next"
                                role="tooltip"
                                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                            >
                                Next video
                                <div class="tooltip-arrow" data-popper-arrow></div>
                            </div>
                            <button
                                data-tooltip-target="tooltip-restart"
                                type="button"
                                class="p-2.5 group rounded-full hover:bg-gray-100 me-1 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                            >
                                <svg
                                    class="w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                                    aria-hidden="true"
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 18 20"
                                >
                                    <path
                                        stroke="currentColor"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M16 1v5h-5M2 19v-5h5m10-4a8 8 0 0 1-14.947 3.97M1 10a8 8 0 0 1 14.947-3.97"
                                    />
                                </svg>
                                <span class="sr-only">Restart video</span>
                            </button>
                            <div
                                id="tooltip-restart"
                                role="tooltip"
                                class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                            >
                                Restart video
                                <div class="tooltip-arrow" data-popper-arrow></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between space-x-2 rtl:space-x-reverse">
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {{ currentDurationFormatted }}
                            </span>
                            <div class="w-full bg-gray-200 rounded-full h-1.5 dark:bg-gray-800">
                                <div
                                    class="bg-primary-600 h-1.5 rounded-full transition-all duration-100"
                                    :style="`width: ${currentDurationPercentage}%`"
                                ></div>
                            </div>
                            <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                {{ durationFormatted }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="md:col-span-3 items-center justify-center hidden ms-auto md:flex">
                    <button
                        data-tooltip-target="tooltip-playlist"
                        type="button"
                        class="p-2.5 group rounded-full hover:bg-gray-100 me-1 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                    >
                        <svg
                            class="w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 18 16"
                        >
                            <path
                                d="M14.316.051A1 1 0 0 0 13 1v8.473A4.49 4.49 0 0 0 11 9c-2.206 0-4 1.525-4 3.4s1.794 3.4 4 3.4 4-1.526 4-3.4a2.945 2.945 0 0 0-.067-.566c.041-.107.064-.22.067-.334V2.763A2.974 2.974 0 0 1 16 5a1 1 0 0 0 2 0C18 1.322 14.467.1 14.316.051ZM10 3H1a1 1 0 0 1 0-2h9a1 1 0 1 1 0 2Z"
                            />
                            <path d="M10 7H1a1 1 0 0 1 0-2h9a1 1 0 1 1 0 2Zm-5 4H1a1 1 0 0 1 0-2h4a1 1 0 1 1 0 2Z" />
                        </svg>
                        <span class="sr-only">View playlist</span>
                    </button>
                    <div
                        id="tooltip-playlist"
                        role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                    >
                        View playlist
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                    <button
                        data-tooltip-target="tooltip-captions"
                        type="button"
                        class="p-2.5 group rounded-full hover:bg-gray-100 me-1 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                    >
                        <svg
                            class="w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 20 16"
                        >
                            <path
                                d="M18 0H2a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2ZM7.648 9.636c.25 0 .498-.064.717-.186a1 1 0 1 1 .979 1.745 3.475 3.475 0 1 1 .185-5.955 1 1 0 1 1-1.082 1.681 1.475 1.475 0 1 0-.799 2.715Zm6.186 0c.252 0 .5-.063.72-.187a1 1 0 1 1 .974 1.746 3.475 3.475 0 1 1 .188-5.955 1 1 0 0 1-1.084 1.681 1.475 1.475 0 1 0-.8 2.715h.002Z"
                            />
                        </svg>
                        <span class="sr-only">Captions</span>
                    </button>
                    <div
                        id="tooltip-captions"
                        role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                    >
                        Toggle captions
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                    <button
                        data-tooltip-target="tooltip-expand"
                        type="button"
                        class="p-2.5 group rounded-full hover:bg-gray-100 me-1 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                    >
                        <svg
                            class="w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 18 18"
                        >
                            <path
                                d="M18 .989a1.016 1.016 0 0 0-.056-.277c-.011-.034-.009-.073-.023-.1a.786.786 0 0 0-.066-.1.979.979 0 0 0-.156-.224l-.007-.01a.873.873 0 0 0-.116-.073.985.985 0 0 0-.2-.128.959.959 0 0 0-.231-.047A.925.925 0 0 0 17 0h-4a1 1 0 1 0 0 2h1.664l-3.388 3.552a1 1 0 0 0 1.448 1.381L16 3.5V5a1 1 0 0 0 2 0V.989ZM17 12a1 1 0 0 0-1 1v1.586l-3.293-3.293a1 1 0 0 0-1.414 1.414L14.586 16H13a1 1 0 0 0 0 2h4a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1ZM3.414 2H5a1 1 0 0 0 0-2H1a1 1 0 0 0-1 1v4a1 1 0 0 0 2 0V3.414l3.536 3.535A1 1 0 0 0 6.95 5.535L3.414 2Zm2.139 9.276L2 14.665V13a1 1 0 1 0-2 0v4c.**************.**************.022.16.048.235a.954.954 0 0 0 .*********** 0 0 0 .073.117l.01.007A.983.983 0 0 0 1 18h4a1 1 0 0 0 0-2H3.5l3.436-3.276a1 1 0 0 0-1.38-1.448h-.003Z"
                            />
                        </svg>
                        <span class="sr-only">Expand</span>
                    </button>
                    <div
                        id="tooltip-expand"
                        role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                    >
                        Full screen
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                    <button
                        data-tooltip-target="tooltip-volume"
                        type="button"
                        class="p-2.5 group rounded-full hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-600 dark:hover:bg-gray-600"
                    >
                        <svg
                            class="w-4 h-4 text-gray-500 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 20 18"
                        >
                            <path
                                d="M10.836.357a1.978 1.978 0 0 0-2.138.3L3.63 5H2a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h1.63l5.07 4.344a1.985 1.985 0 0 0 2.142.299A1.98 1.98 0 0 0 12 15.826V2.174A1.98 1.98 0 0 0 10.836.357Zm2.728 4.695a1.001 1.001 0 0 0-.29 1.385 4.887 4.887 0 0 1 0 5.126 1 1 0 0 0 1.674 1.095A6.645 6.645 0 0 0 16 9a6.65 6.65 0 0 0-1.052-3.658 1 1 0 0 0-1.384-.29Zm4.441-2.904a1 1 0 0 0-1.664 1.11A10.429 10.429 0 0 1 18 9a10.465 10.465 0 0 1-1.614 5.675 1 1 0 1 0 1.674 1.095A12.325 12.325 0 0 0 20 9a12.457 12.457 0 0 0-1.995-6.852Z"
                            />
                        </svg>
                        <span class="sr-only">Adjust volume</span>
                    </button>
                    <div
                        id="tooltip-volume"
                        role="tooltip"
                        class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip dark:bg-gray-700"
                    >
                        Adjust volume
                        <div class="tooltip-arrow" data-popper-arrow></div>
                    </div>
                </div>
            </div>
            <audio :src="audioResult.link" ref="audio" @play="onPlay" @pause="onPause" @loadeddata="onLoaded">
                Your browser does not support the audio element.
            </audio>
        </div>
    </div>
</template>

<script setup lang="ts">
import { Drawer } from 'flowbite'

import { storeToRefs } from 'pinia'
const translateStore = useTranslateStore()
const { showAudioPlayer, audioResult, audioResultVoice, isPlaying } = storeToRefs(translateStore)
const { t } = useI18n()

let drawer: Drawer
onMounted(() => {
    if (process.client) {
        const $targetEl = document.getElementById('drawer-swipe')
        const options = {
            placement: 'bottom',
            backdrop: true,
            bodyScrolling: false,
            backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-30',
            onHide: () => {
                showAudioPlayer.value = false
            },
            onShow: () => {
                showAudioPlayer.value = true
            },
        }

        drawer = new Drawer($targetEl, options)
    }
})

watch(
    () => showAudioPlayer.value,
    (value) => {
        if (value) {
            drawer?.show()
        } else {
            drawer?.hide()
        }
    }
)

const audio = ref<HTMLAudioElement>(new Audio(audioResult.value.link))
const duration = ref(0)
const durationFormatted = computed(() => {
    const minutes = Math.floor(duration.value / 60)
    const seconds = Math.floor(duration.value % 60)
    const secondsFormatted = seconds < 10 ? `0${seconds}` : seconds
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes
    return `${minutesFormatted}:${secondsFormatted}`
})
const currentDuration = ref(0)
const currentDurationFormatted = computed(() => {
    const minutes = Math.floor(currentDuration.value / 60)
    const seconds = Math.floor(currentDuration.value % 60)
    const secondsFormatted = seconds < 10 ? `0${seconds}` : seconds
    const minutesFormatted = minutes < 10 ? `0${minutes}` : minutes
    return `${minutesFormatted}:${secondsFormatted}`
})

const currentDurationPercentage = computed(() => {
    return Math.floor((currentDuration.value / duration.value) * 100)
})
const onPlay = () => {
    isPlaying.value = true
}

const onPause = () => {
    isPlaying.value = false
}

const toggleAudio = () => {
    if (audio.value?.paused) {
        audio.value.play()
    } else {
        audio.value.pause()
    }
}

const onLoaded = () => {
    currentDuration.value = 0
    duration.value = audio.value.duration
    audio.value?.play()
    audio.value.addEventListener('timeupdate', () => {
        currentDuration.value = audio.value.currentTime
    })
}
</script>

<style scoped>
.playing {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.3rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0.5rem;
    box-sizing: border-box;
}

.playing__bar {
    display: inline-block;
    background: white;
    width: 30%;
    height: 100%;
    animation: up-and-down 1.3s ease infinite alternate;
}

.playing__bar1 {
    height: 60%;
}

.playing__bar2 {
    height: 30%;
    animation-delay: -2.2s;
}

.playing__bar3 {
    height: 75%;
    animation-delay: -3.7s;
}

@keyframes up-and-down {
    10% {
        height: 30%;
    }

    30% {
        height: 100%;
    }

    60% {
        height: 50%;
    }

    80% {
        height: 75%;
    }

    100% {
        height: 60%;
    }
}
</style>

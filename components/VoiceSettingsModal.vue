<template>
    <div class="py-4 h-full overflow-auto md:max-h-[calc(100vh-300px)] flex flex-col space-y-4 scrollbar-thin">
        <UFormGroup label="Quality" :ui="{ label: { wrapper: 'px-4' } }" required>
            <div class="px-3">
                <UPricingToggle
                    :left="$t(modelOptions[0].label)"
                    :right="$t(modelOptions[1].label)"
                    :model-value="model === modelOptions[1].value"
                    @update:model-value="model = $event ? modelOptions[1].value : modelOptions[0].value"
                    class="w-full"
                />
            </div>
        </UFormGroup>
        <UFormGroup label="Voice" :ui="{ label: { wrapper: 'px-4' } }" required>
            <div v-for="voice in voiceOptions">
                <BaseVoiceItem
                    v-bind="voice"
                    :active="voiceSelected === voice.value"
                    @select="voiceSelected = $event"
                />
            </div>
        </UFormGroup>
    </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
const translateStore = useTranslateStore()
const { voiceOptions, voiceSelected, modelOptions, model } = storeToRefs(translateStore)
</script>

<script setup lang="ts">
const { t } = useI18n()

const props = defineProps<{
    item: any
}>()

const dialogsStore = useDialogsStore()
const historyStore = useHistoryStore()

const items = computed(() => {
    return [
        [
            {
                label: t('Clone'),
                icon: 'material-symbols:cyclone',
                click: () => {
                    historyStore.cloneHistory(props.item)
                },
                disabled: props.item?.status == 1,
            },
        ],
        [
            // {
            //     label: t('Stop processing'),
            //     icon: 'gravity-ui:circle-stop',
            //     click: () => {
            //         dialogsStore.showConfirmDialog({
            //             title: t('Delete'),
            //             message: t('Are you sure you want to delete this history?'),
            //             confirmButtonText: t('Delete'),
            //             cancelButtonText: t('Cancel'),
            //             onConfirm: async () => {
            //                 await historyStore.deleteHistory(props.item?.id, props.item?.uuid)
            //                 await historyStore.filterHistories()
            //             },
            //         })
            //     },
            //     show: props.item?.status == 1,
            // },
            {
                label: t('Delete'),
                icon: 'i-heroicons-trash-20-solid',
                click: () => {
                    dialogsStore.showConfirmDialog({
                        title: t('Delete'),
                        message: t('Are you sure you want to delete this history?'),
                        confirmButtonText: t('Delete'),
                        cancelButtonText: t('Cancel'),
                        onConfirm: async () => {
                            await historyStore.deleteHistory(props.item?.id, props.item?.uuid)
                            await historyStore.filterHistories()
                        },
                    })
                },
                show: true
            },
        ].filter((item) => item.show !== false),
    ]
})
</script>

<template>
    <UDropdown :items="items" :popper="{ placement: 'bottom-start' }">
        <UButton color="primary" variant="soft" trailing-icon="iconamoon:menu-kebab-vertical-fill" />
    </UDropdown>
</template>

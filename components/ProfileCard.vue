<template>
    <div class="w-full mx-auto scrollbar-thin">
        <div v-if="user" class="flex flex-col items-center pb-10 scrollbar-thin">
            <div class="flex flex-row justify-between items-center w-full pb-4">
                <div class="text-xl text-start w-full pl-2">
                    {{ $t('Profile') }}
                </div>
                <UButton
                    v-if="showCloseButton"
                    icon="i-material-symbols:close"
                    size="sm"
                    color="gray"
                    square
                    variant="solid"
                    @click="isOpen = false"
                />
            </div>
            <div class="flex flex-row items-end justify-between w-full px-1.5">
                <div class="flex flex-row gap-1 items-center">
                    <UAvatar
                        icon="i-solar-user-bold-duotone"
                        size="md"
                        :ui="{
                            rounded: 'rounded-md',
                        }"
                    />
                    <div class="flex flex-col">
                        <h5 class="text-xl font-medium text-gray-900 dark:text-white truncate max-w-full">
                            {{ user.full_name }}
                        </h5>
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ user.email }}</span>
                    </div>
                </div>
                <UButton
                    v-if="features.userSettings"
                    icon="i-weui-setting-outlined"
                    size="xs"
                    color="white"
                    variant="solid"
                    :label="$t('Settings')"
                    :trailing="false"
                    @click="onRedirect('/profile/settings')"
                />
            </div>
            <UDivider type="dashed" class="mt-4" />

            <ul class="space-y-2 text-sm font-base w-full mt-2 mb-2">
                <li>
                    <a
                        href="#"
                        class="group flex items-center p-2 text-gray-900 rounded-lg dark:text-white justify-between"
                    >
                        <div class="relative flex">
                            <svg
                                class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                                aria-hidden="true"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    stroke="currentColor"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="1.5"
                                    d="m7 10 2 2 4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                            </svg>
                            <span class="flex-1 ml-3 whitespace-nowrap">{{ $t('Current plan') }}</span>
                        </div>
                        <span
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-yellow-100 rounded-full dark:bg-yellow-700 dark:text-gray-300"
                        >
                            {{ $t(user?.current_plan || '') }}
                        </span>
                    </a>
                </li>
                <li>
                    <a href="#" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white">
                        <svg
                            class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 21 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="m11.479 1.712 2.367 4.8a.532.532 0 0 0 .4.292l5.294.769a.534.534 0 0 1 .3.91l-3.83 3.735a.534.534 0 0 0-.154.473l.9 5.272a.535.535 0 0 1-.775.563l-4.734-2.49a.536.536 0 0 0-.5 0l-4.73 2.487a.534.534 0 0 1-.775-.563l.9-5.272a.534.534 0 0 0-.154-.473L2.158 8.48a.534.534 0 0 1 .3-.911l5.294-.77a.532.532 0 0 0 .4-.292l2.367-4.8a.534.534 0 0 1 .96.004Z"
                            />
                        </svg>
                        <span class="flex-1 ml-3 whitespace-nowrap">
                            <div>
                                {{ $t('Available credits') }}
                            </div>
                            <!-- <div
                v-if="isUsingFreePlan"
                class="font-light text-yellow-500 dark:text-yellow-200 text-xs"
              >
                {{ $t("Unlimited Create speech from text") }}
              </div> -->
                        </span>
                        <UIcon v-if="isSyncingCredit" name="i-eos-icons-loading" class="w-4 h-4" />
                        <span
                            v-else
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-blue-100 rounded-full dark:bg-blue-700 dark:text-gray-300"
                        >
                            {{ formatUserTokens }}
                        </span>
                    </a>
                </li>
                <li>
                    <a href="#" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white">
                        <svg
                            class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 20 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M11 8H2a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1h7m2.5-11V4.5a3.5 3.5 0 1 0-7 0V8m10 5.217V14.5l.9.9m3.6-.9a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Z"
                            />
                        </svg>

                        <div class="flex flex-row items-center gap-1 flex-1 ml-3 whitespace-nowrap">
                            <div>
                                {{ $t('Locked credits') }}
                            </div>
                            <UPopover
                                class="h-full"
                                mode="hover"
                                :popper="{ placement: 'top' }"
                                :ui="{ wrapper: 'items-center justify-center flex' }"
                            >
                                <UIcon name="i-ri-question-fill" class="me-1" />

                                <template #panel>
                                    <div class="p-2 text-xs">
                                        {{
                                            $t(
                                                'We lock your credits to perform the task you requested. If the task fails or is canceled, the credits will be returned to your account.'
                                            )
                                        }}
                                    </div>
                                </template>
                            </UPopover>
                        </div>
                        <UIcon v-if="isSyncingCredit" name="i-eos-icons-loading" class="w-4 h-4" />
                        <span
                            v-else
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-pink-100 rounded-full dark:bg-pink-700 dark:text-gray-300"
                        >
                            {{ formatUserLockedTokens }}
                        </span>
                    </a>
                </li>
                <!-- <li>
                    <a href="#" class="flex items-center p-2 text-gray-900 rounded-lg dark:text-white">
                        <svg
                            class="flex-shrink-0 w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 20 20"
                        >
                            <path
                                stroke="currentColor"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1.5"
                                d="M5 1v3m5-3v3m5-3v3M1 7h18M5 11h10M2 3h16a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z"
                            />
                        </svg>
                        <span class="flex-1 ml-3 whitespace-nowrap">{{ $t('Expiration date') }}</span>
                        <span
                            class="inline-flex items-center justify-center px-2 ml-3 text-sm font-medium text-gray-800 bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300"
                        >
                            {{ expirationFormat }}
                        </span>
                    </a>
                </li> -->
            </ul>
            <div class="w-full grid grid-cols-2 mt-4 gap-4 md:mt-6">
                <a
                    @click="onRedirect('/profile/buy-credits')"
                    class="cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    :class="{
                        'col-span-2': !enableAds,
                        [ui.active]: route.name === 'profile-buy-credits',
                    }"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 19 20"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M6 15a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm0 0h8m-8 0-1-4m9 4a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm1-4H5m0 0L3 4m0 0h5.501M3 4l-.792-3H1m11 3h6m-3 3V1"
                        />
                    </svg>
                    {{ $t('Buy credits') }}
                </a>
                <a
                    v-if="enableAds"
                    @click="onRedirect('/profile/remove-ads')"
                    class="cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    :class="{
                        [ui.active]: route.name === 'profile-remove-ads',
                    }"
                >
                    <UIcon name="i-mdi:ads-off" class="w-4 h-4 mr-2 -ml-1" />
                    {{ $t('Remove Ads!') }}
                </a>
                <a
                    @click="onRedirect('/profile/payment-history')"
                    class="col-span-2 cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    :class="{
                        [ui.active]: route.name === 'profile-payment-history',
                    }"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 16 20"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M6 1v4a1 1 0 0 1-1 1H1m8-2h3M9 7h3m-4 3v6m-4-3h8m3-11v16a.969.969 0 0 1-.932 1H1.934A.97.97 0 0 1 1 18V5.828a2 2 0 0 1 .586-1.414l2.828-2.828A2 2 0 0 1 5.829 1h8.239A.969.969 0 0 1 15 2ZM4 10h8v6H4v-6Z"
                        />
                    </svg>

                    {{ $t('Payment history') }}
                </a>
                <a
                    @click="onRedirect('/profile/')"
                    class="col-span-2 cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    :class="{
                        [ui.active]: route.name === 'profile',
                    }"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 20 16"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M3.656 12.115a3 3 0 0 1 5.682-.015M13 5h3m-3 3h3m-3 3h3M2 1h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1Zm6.5 4.5a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"
                        />
                    </svg>
                    {{ $t('Update account') }}
                </a>
                <a
                    v-if="features.userAPI"
                    @click="onRedirect('/profile/integration')"
                    class="col-span-2 cursor-pointer inline-flex justify-between items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    :class="{
                        [ui.active]: (route.name as string).includes('profile-integration'),
                    }"
                >
                    <div class="flex flex-row items-center">
                        <UIcon name="i-tabler:plug-connected" class="w-4 h-4 mr-2 -ml-1" />
                        {{ $t('Service Integration') }}
                    </div>
                    <UBadge
                        color="primary"
                        :ui="{ rounded: 'rounded-full' }"
                        class="animate__animated animate__tada animate__repeat-2"
                    >
                        {{ $t('New') }}
                    </UBadge>
                </a>
                <a
                    @click="onRedirect('/profile/gift-card')"
                    class="col-span-2 cursor-pointer inline-flex justify-between items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    :class="{
                      [ui.active]: (route.name as string).includes('profile-gift-card'),
                  }"
                >
                    <div class="flex flex-row items-center">
                        <UIcon name="i-fluent-gift-card-multiple-20-regular" class="w-5 h-5 mr-1.5 -ml-1" />
                        <div class="flex flex-col justify-start text-left -space-y-1">
                            <div>
                                {{ $t('Gift Card') }}
                            </div>
                            <div class="text-xs font-light">
                                {{ $t('Redeem a gift card to get more credits') }}
                            </div>
                        </div>
                    </div>
                </a>
                <a
                    v-if="features.referral || isSuperUser"
                    @click="showPopupReferral = true"
                    class="col-span-2 cursor-pointer inline-flex justify-between items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <div class="flex flex-row items-center">
                        <UIcon name="i-mynaui-share" class="w-5 h-5 mr-1.5 -ml-1" />
                        <div class="flex flex-col justify-start text-left -space-y-1">
                            <div>
                                {{ $t('Refer a friend') }}
                            </div>
                            <div class="text-xs font-light">
                                {{ $t('Refer friends and get up to 5% bonus') }}
                            </div>
                        </div>
                    </div>
                    <UBadge
                        color="primary"
                        :ui="{ rounded: 'rounded-full' }"
                        class="animate__animated animate__tada animate__repeat-2"
                    >
                        {{ $t('New') }}
                    </UBadge>
                </a>
                <a
                    @click="onLogout"
                    class="col-span-2 cursor-pointer inline-flex items-center px-4 py-2 text-sm font-medium text-center focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                >
                    <svg
                        class="w-4 h-4 mr-2 -ml-1"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 16 16"
                    >
                        <path
                            stroke="currentColor"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1.5"
                            d="M4 8h11m0 0-4-4m4 4-4 4m-5 3H3a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h3"
                        />
                    </svg>
                    {{ $t('Sign out') }}
                </a>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import { useModal } from 'vue-final-modal'
import { usePaymentsStore } from '~/stores/payments'
import CancelSubscriptionConfirmation from '~/base-components/CancelSubscriptionConfirmation.vue'
const paymentsStore = usePaymentsStore()
const config = useRuntimeConfig()
const isBeta = config.public.isBeta
const enableAds = config.public.enableAds
const features = config.public.features
dayjs.locale(window.localStorage.getItem('locale') || 'en')
dayjs.extend(utc)
dayjs.extend(relativeTime)
const authStore = useAuthStore()
const appStore = useAppStore()
const { showPopupReferral } = storeToRefs(appStore)
const { user, isOpen, isUsingFreePlan, isSyncingCredit, isSuperUser } = storeToRefs(authStore)
const router = useRouter()
const route = useRoute()
const formatUserTokens = computed(() => {
    return new Intl.NumberFormat().format(user.value?.tokens || 0)
})

const props = defineProps({
    showCloseButton: {
        type: Boolean,
        default: false,
    },
})

const { open, close } = useModal({
    component: CancelSubscriptionConfirmation,
    attrs: {
        async onConfirm() {
            const result = await paymentsStore.cancelSubscription({
                subscription_id: user.value?.user_plan?.subscription_id,
            })
            await authStore.syncUserTokenInfo()
            if (result) {
                appStore.setShowUserDrawer(false)
                router.push({ path: '/profile/cancel-successful' })
            }
            close()
        },
        onCancel() {
            close()
        },
    },
})

const formatUserLockedTokens = computed(() => {
    return new Intl.NumberFormat().format(user.value?.user_credit?.locked_credit || 0)
})

// const expirationFormat = computed(() => dayjs().to(dayjs().add(69, "day")));
const expirationFormat = computed(() =>
    user.value?.user_plan?.expire_at
        ? dayjs.unix(dayjs.utc(user.value?.user_plan?.expire_at).unix()).format('YYYY-MM-DD HH:mm')
        : ''
)
const onRedirect = (to: string) => {
    isOpen.value = false
    navigateTo(to)
    appStore.setShowUserDrawer(false)
}

const onLogout = () => {
    isOpen.value = false
    authStore.logout()
    appStore.setShowUserDrawer(false)
}

const ui = {
    active: 'border-l-4 border-primary-500 text-primary-700 dark:text-white dark:bg-gray-700"',
}

onMounted(() => {
    authStore.syncUserTokenInfo()
})
</script>

<script setup lang="ts">
const { t } = useI18n()

const openImageVideoService = () => {
    window.open('https://geminigen.ai', '_blank')
}
</script>

<template>
    <!-- Desktop version -->
    <UButton
        icon="i-material-symbols-image-outline"
        size="sm"
        color="emerald"
        variant="solid"
        :trailing="false"
        @click="openImageVideoService"
        :label="$t('Image/Video')"
        class="hidden sm:flex animate-pulse hover:animate-none transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-semibold rounded-full"
    />

    <!-- Mobile version -->
    <UButton
        icon="i-material-symbols-image-outline"
        size="sm"
        color="emerald"
        variant="solid"
        :trailing="false"
        @click="openImageVideoService"
        class="flex sm:hidden animate-pulse hover:animate-none transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white rounded-full"
        padded
    />
</template>

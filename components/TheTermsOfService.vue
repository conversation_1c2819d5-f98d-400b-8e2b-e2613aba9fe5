<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppStore } from '~/stores/app'
import { onMounted, watch } from 'vue'
import { Modal } from 'flowbite'
import type { ModalOptions, ModalInterface } from 'flowbite'

const appStore = useAppStore()

const { showTermsOfServiceModal, acceptedTermsOfService } = storeToRefs(appStore)

let modal: ModalInterface
onMounted(() => {
    if (process.client) {
        const $modalElement: HTMLElement = document.querySelector('#modalTermsOfService')
        const modalOptions: ModalOptions = {
            placement: 'center-center',
            backdrop: 'dynamic',
            backdropClasses: 'bg-gray-900 bg-opacity-50 dark:bg-opacity-80 fixed inset-0 z-40',
            closable: false,
            onHide: () => {
                appStore.setShowTermsOfServiceModal(false)
            },
            onShow: () => {
                appStore.setShowTermsOfServiceModal(true)
            },
        }

        modal = new Modal($modalElement, modalOptions)
    }
})

watch(showTermsOfServiceModal, (value) => {
    if (value) {
        modal.show()
    } else {
        modal.hide()
    }
})

const terms = computed(() => {
    return useTerms()
})
</script>
<template>
    <!-- Main modal -->
    <div
        id="modalTermsOfService"
        tabindex="-1"
        aria-hidden="true"
        class="scrollbar-thin fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full"
    >
        <div class="relative w-full max-w-2xl max-h-full">
            <!-- Modal content -->
            <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
                <!-- Modal header -->
                <div class="flex items-start justify-between p-4 border-b rounded-t dark:border-gray-600">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        {{ $t('Terms of Service') }}
                    </h3>
                </div>
                <!-- Modal body -->
                <ul class="p-6 space-y-3 text-sm leading-relaxed text-gray-500 dark:text-gray-400">
                    <li v-for="term in terms">
                        <strong>{{ term.title }}: </strong>
                        <span>
                            {{ term.content }}
                        </span>
                    </li>
                </ul>
                <!-- <div>
                    <div
                        class="p-6 space-y-3 text-sm leading-relaxed text-gray-500 dark:text-gray-400"
                        v-html="$t('term-of-service-content')"
                    ></div>
                </div> -->
                <!-- Modal footer -->
                <div class="flex items-center p-6 space-x-2 border-t border-gray-200 rounded-b dark:border-gray-600">
                    <button
                        @click="
                            () => {
                                appStore.setShowTermsOfServiceModal(false)
                                acceptedTermsOfService = true
                            }
                        "
                        type="button"
                        class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                    >
                        {{ $t('I accept') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Icon } from '@iconify/vue'
const props = defineProps({
    dark: {
        type: Boolean,
        default: () => false,
    },
    static: {
        type: <PERSON><PERSON><PERSON>,
        default: () => false,
    },
})
</script>

<template>
    <div class="flex flex-row items-center space-x-4">
        <div class="grow">
            <base-sound-wave :static="static"/>
        </div>
        <a
            href="#"
            class="group text-2xl relative flex space-x-2 !font-semibold items-center text-primary-700 dark:text-primary-400 transition-all duration-300"
        >
            <div class="">
                {{ $t('Text To Speech OpenAI') }}
            </div>
            <!-- <div class="absolute -top-3 -right-10 z-50">
                <Icon icon="ri:chat-voice-fill" class="text-gray-800 group-hover:text-primary-400 text-4xl" />
            </div> -->
        </a>
    </div>
</template>

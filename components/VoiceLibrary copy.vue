<template>
  <UCard class="" :ui="{ body: { padding: '!p-0' }, base: 'overflow-visible' }">
    <div class="w-full">
      <div
        class="flex flex-row items-center gap-4 justify-between pl-4 pr-4 z-10 relative bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800"
      >
        <VoiceLibraryTypes class="flex-1 w-full mt-2" />
        <div v-if="showCloseButton" class="flex flex-row items-center gap-2">
          <!-- <UInput
            icon="i-heroicons-magnifying-glass-20-solid"
            size="xs"
            color="white"
            :trailing="false"
            :placeholder="$t('Search...')"
            class="w-full"
            v-model="filter.keyword"
          /> -->
          <!-- <UButton
            :icon="showFilter ? 'i-mingcute-up-fill' : 'i-ion-filter'"
            size="xs"
            color="white"
            variant="solid"
            :label="showFilter ? $t('Hide') : $t('Filter')"
            :trailing="false"
            @click="showFilter = !showFilter"
          /> -->
          <UButton
            :icon="'i-material-symbols-close'"
            size="xs"
            color="white"
            variant="solid"
            :trailing="false"
            @click="emits('close')"
          />
        </div>
      </div>
      <div
        class="transition-all duration-200 px-4 py-2 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900 flex flex-row items-center justify-between"
        :class="{
          '-mt-0': showFilter,
          '-mt-12 -z-10': !showFilter,
        }"
      >
        <div class="flex flex-row items-center gap-2">
          <UInput
            icon="i-heroicons-magnifying-glass-20-solid"
            size="xs"
            color="white"
            :trailing="false"
            :placeholder="$t('Search...')"
            class="w-full"
            v-model="filter.keyword"
          />
          <USelectMenu
            size="xs"
            v-model="filter.genders"
            :options="voiceGenders"
            multiple
            :placeholder="$t('Gender')"
            :ui-menu="{
              width: 'w-screen max-w-32',
            }"
          >
            <template #label>
              <span>
                {{ $t("Gender") }}
              </span>
            </template>
          </USelectMenu>
          <USelectMenu
            size="xs"
            v-model="filter.ages"
            :options="voiceAges"
            multiple
            :placeholder="$t('Age')"
            :ui-menu="{
              width: 'w-screen max-w-28',
            }"
          >
            <template #label>
              <span>
                {{ $t("Age") }}
              </span>
            </template>
          </USelectMenu>
          <!-- <USelectMenu
            size="xs"
            v-model="filter.languages"
            :options="voiceLanguages"
            multiple
            :placeholder="$t('Language')"
            searchable
          >
            <template #label>
              <span>
                {{ $t("Language") }}
              </span>
            </template>
          </USelectMenu> -->
          <USelectMenu
            size="xs"
            searchable
            v-model="filter.accents"
            :options="voiceAccents"
            multiple
            :placeholder="$t('Accent')"
            :ui-menu="{
              width: 'w-screen max-w-40',
            }"
          >
            <template #label>
              <span>
                {{ $t("Accent") }}
              </span>
            </template>
          </USelectMenu>
        </div>
        <div class="flex flex-wrap">
          <UButton
            icon="i-lucide-filter-x"
            size="xs"
            color="gray"
            variant="solid"
            :label="$t('Clear All')"
            :trailing="false"
            @click="voiceLibraryStore.clearFilter"
          />
        </div>
      </div>
      <div
        class="px-4 py-1 text-xs border-b border-gray-200 dark:border-gray-800 shadow-sm bg-gray-50 dark:bg-gray-900"
      >
        <div v-if="hasFilter">
          <div class="truncate text-gray-500 font-light">{{ filterDescription }}</div>
        </div>
        <div v-else class="font-semibold">{{ $t("All") }}</div>
      </div>
      <RecycleScroller
        ref="storiesElm"
        :items="filteredVoiceLibraries"
        :gridItems="true"
        :min-item-size="147"
        class="py-4 pb-36 md:pb-10 w-full overflow-auto md:h-[calc(100vh-339px)] scrollbar-thin grid grid-cols-1 gap-4 px-4"
        :class="{
          'md:grid-cols-2': !props.full,
          'md:grid-cols-4 sm:grid-col-3': props.full,
        }"
      >
        <template v-slot="{ item, index, active }">
          <DynamicScrollerItem
            :item="item"
            :active="active"
            :data-index="index"
            :data-id="item.id"
          >
            <BaseVoiceCard
              :data-id="item.id"
              v-bind="item"
              :active="
                activeStory?.voice_id === item.id || props.activeVoiceId === item.id
              "
              @select="(id) => emits('select', id)"
              @toggle-favorite="voiceLibraryStore.toggleFavorite"
              :updating="loadings['toggleFavorite'][item.id]"
            />
          </DynamicScrollerItem>
        </template>
      </RecycleScroller>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";

import { storeToRefs } from "pinia";
const storyStore = useStoryStore();
const voiceLibraryStore = useVoiceLibraryStore();
import draggable from "vuedraggable";
const { activeStory, voiceLibraryDragArea } = storeToRefs(storyStore);
const {
  filteredVoiceLibraries,
  filter,
  hasFilter,
  filterDescription,
  loadings,
} = storeToRefs(voiceLibraryStore);
const showFilter = ref(true);
const { t } = useI18n();
const props = defineProps({
  showCloseButton: {
    type: Boolean,
    default: false,
  },
  full: {
    type: Boolean,
    default: false,
  },
  activeVoiceId: {
    type: String,
    default: "",
  },
});
const emits = defineEmits(["close", "select"]);

const {
  voiceTypes,
  voiceGenders,
  voiceAges,
  voiceLanguages,
  voiceAccents,
} = useVoiceLibrary(t);

watch(
  () => activeStory.value,
  (active) => {
    if (active) {
      // nextTick(() => {
      //   const voiceElm = voiceLibraryDragArea.value.$el.querySelector(
      //     `[data-id="${active.voice_id}"]`
      //   );
      //   console.log("🚀 ~ nextTick ~ active.voice_id:", active.voice_id);
      //   console.log("🚀 ~ nextTick ~ voiceElm:", voiceElm);
      //   if (voiceElm) {
      //     voiceElm.scrollIntoView({
      //       behavior: "smooth",
      //       block: "center",
      //       inline: "center",
      //     });
      //   }
      // });
    }
  }
);

onMounted(() => {
  // voiceLibraryStore.fetchVoiceLibrary();
  voiceTypes.forEach((type) => {
    voiceLibraryStore.fetchVoiceLibraryByType(type.value as string, false);
  });
  voiceLibraryStore.filterVoiceLibraries();
});

const authStore = useAuthStore();
const { user } = storeToRefs(authStore);
const router = useRouter();
</script>

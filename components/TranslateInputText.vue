<template>
    <div class="flex flex-col h-full pb-9">
        <div :class="{ grow: isLoggedIn, 'flex flex-1 flex-col justify-between': !isLoggedIn }">
            <p v-if="translateError" class="text-red-500 p-4 text-sm">
                <div class="flex flex-row gap-2 items-center">
                   <div>
                    {{ $t('Error') }}: {{ $t(translateError) }}
                   </div>
                <UButton
                icon="i-carbon-close-outline"
                size="xs"
                color="gray"
                square
                :padded="false"
                variant="link"
                @click="translateError = ''"
              />
                </div>
                <div v-if="translateError === 'NOT_VERIFY_ACCOUNT'" class="text-left mt-2">
                    <UButton
                    icon="i-ic-baseline-plus"
                    size="xs"
                    color="gray"
                    variant="solid"
                    :label="$t('Resend verify email')"
                    :trailing="false"
                    @click="authStore.resendVerifyEmail()"
                  />
                </div>
                <div v-if="['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(translateError)" class="text-left mt-2">
                    <UButton
                    icon="i-ic-baseline-plus"
                    size="xs"
                    color="gray"
                    variant="solid"
                    :label="$t('Buy more credits')"
                    :trailing="false"
                    @click="navigateTo('/profile/buy-credits')"
                  />
                </div>
               
            </p>
            <textarea
                v-else
                id="text-translate-input"
                ref="translateInput"
                class="scrollbar-thin w-full px-3 md:pl-4 md:pr-2 resize-none overflow-y-auto h-full min-h-[210px] text-sm text-gray-900 bg-white border-0 dark:bg-gray-900 focus:ring-0 dark:text-white dark:placeholder-gray-500 placeholder-gray-300"
                required
                v-model="inputText"
                :maxlength="maxLengthText"
                :placeholder="$t('Enter the text you want to convert to speech here.')"
            >
            </textarea>
            <div v-if="inputText?.length >= 200 && !isLoggedIn" class="pb-4 px-4 text-sm text-primary-500 flex flex-row gap-2 items-center">
                <i18n-t tag="p" keypath="{0} to create speech with more than 500 characters">
                    <a
                        @click="navigateTo('/signin')"
                        class="font-medium text-primary-700 underline dark:text-primary-400 cursor-pointer"
                    >
                        {{ $t('Sign in') }}
                    </a>
                </i18n-t>
            </div>
        </div>
    </div>
    <div
        v-if="inputText && !translateError"
        class="absolute bottom-0 pb-3 pt-1 px-3 bg-white/65 dark:bg-slate-900 rounded-s-md right-3 text-right text-xs font-thin dark:text-gray-300 flex flex-row space-x-4 items-center"
    >
        <div>{{ inputText?.length }} / {{ maxLengthText }}</div>
        <div @click="translateStore.clearInputAndResultText()">
            <UButton :padded="false" color="red" variant="link" :label="$t('Clear text')" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { watch, ref, onMounted, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'

import _ from 'lodash'
import SuggestPrompts from './SuggestPrompts.vue'
const authStore = useAuthStore()
const { maxLengthText, isLoggedIn } = storeToRefs(authStore)
const translateStore = useTranslateStore()
const { t } = useI18n()
const { inputText, translateError, showCustomPrompt, translateOptions, loadings } = storeToRefs(translateStore)
const translateInput = ref(null)
const customPromptInput = ref(null)

onMounted(() => {
    translateInput.value.focus()
})

watch(showCustomPrompt, (value) => {
    if (value) {
        nextTick(() => {
            customPromptInput.value.focus()
        })
        translateOptions.value = {
            translateDomain: '',
            translateTone: '',
            translateWritingStyle: '',
        }
    } else {
        translateInput.value?.focus()
    }
})
const links = [
    {
        label: t('Input Text'),
        icon: 'i-solar-text-square-bold',
    },
]
//watch inputText
// watch(inputText, (value) => {
//     translateStore.clearResultText()
// })
</script>

<script setup lang="ts">
const myVoiceStore = useMyVoiceStore();
const { showPolicyModal, isAgreed } = storeToRefs(myVoiceStore);


const policies = computed(() => {
  return useMyVoicePolicy();
});
</script>

<template>
  <UModal
    :ui="{
      width: 'sm:max-w-2xl',
    }"
    v-model="showPolicyModal"
    prevent-close
  >
    <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
            {{ $t("My Voice Policy") }}
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            class="-my-1"
            @click="showPolicyModal = false"
          />
        </div>
      </template>
      <ol
        class="px-6 list-disc space-y-3 text-base leading-relaxed text-gray-500 dark:text-gray-400"
      >
        <li v-for="policy in policies">
          <strong>{{ policy.title }}: </strong>
          <span>
            {{ policy.content }}
          </span>
        </li>
      </ol>
      <template #footer>
        <div class="flex flex-row gap-2 justify-end">
          <!-- Icon next -->
          <UButton
            @click="showPolicyModal = false; isAgreed = true"
            :trailing="true"
            :ui="{
              inline: 'md:justify-between justify-center',
              icon: {
                size: {
                  xl: 'w-14 h-14 md:w-6 md:h-6',
                },
              },
            }"
            color="primary"
            class="px-6"
          >
            {{ $t("I Agree") }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

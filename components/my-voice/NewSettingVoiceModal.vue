<script setup lang="ts">
import { useMyVoiceStore } from '@/stores/my-voice'
import { cloneDeep } from 'lodash'
import { AccessEnum, VoiceTypeEnum, type MyVoice } from '~/types'
import { object, string, mixed, type InferType } from 'yup'
import type { FormError, FormSubmitEvent } from '#ui/types'
import { useDropZone } from '@vueuse/core'
const countUp = ref(0)
const { t, locale } = useI18n()
const myVoiceStore = useMyVoiceStore()
const fileError = ref('') as Ref<string | null>
const {
    showNewSettingVoiceModal,
    newMyVoice,
    loadings,
    errorMessages,
    showPolicyModal,
    isAgreed,
    isInstantVoiceCloning,
} = storeToRefs(myVoiceStore)
const { setInputFileForNewMyVoice, setMyVoice, generateMyVoice, clearInputFileForNewMyVoice } = myVoiceStore
const { voiceAccents } = useVoiceLibrary(t)
const voiceGenders = computed(() => {
    return useVoiceLibrary(t).voiceGenders
})

const voiceAges = computed(() => {
    return useVoiceLibrary(t).voiceAges
})
const isUploading = ref(false)
const voiceLibraryStore = useVoiceLibraryStore()
const { checkAccessBeforeAction } = useAuthStore()

const adBlockStore = useAdBlockStore()
const { continueActionAfterCheckAdBlock } = adBlockStore

type Schema = InferType<typeof schema>

const toast = useToast()
const stateNewMyVoice = ref(cloneDeep(newMyVoice.value))

const validate = (state: any): FormError[] => {
    console.log('🚀 ~ validate ~ state:', state)
    const errors = []
    if (!state.speaker_name) errors.push({ path: 'speaker_name', message: t('Name is required') })
    if (state.speaker_name.length > 50)
        errors.push({ path: 'speaker_name', message: t('Name must be at most 50 characters') })
    if (!state.inputFile) errors.push({ path: 'inputFile', message: t('Upload file is required') })
    if (isInstantVoiceCloning.value && state.file_duration < 30)
        errors.push({ path: 'inputFile', message: t('Audio duration must be at least 30 seconds') })
    if (!isInstantVoiceCloning.value && state.file_duration < 300)
        errors.push({ path: 'inputFile', message: t('Audio duration must be at least 5 minutes') })

    return errors
}

const dragOverHandler = (e: Event) => {
    e.preventDefault()
}

const dropHandler = (e: Event) => {
    const files = (e.target as HTMLInputElement).files
    if (files && files.length > 0) {
        const file = files[0]
        validateFile(file)
        stateNewMyVoice.value.inputFile = file
    }
}

const inputRef = ref()

const dropZoneRef = ref<HTMLElement>()

const { isOverDropZone } = useDropZone(dropZoneRef, {
    onDrop,
    dataTypes: ['audio/mp3', 'audio/mpeg'],
})

function onDrop(files: File[] | null) {
    if (files && files.length > 0) {
        validateFile(files[0])
        setInputFileForNewMyVoice(files[0])
        stateNewMyVoice.value.inputFile = files[0]
    }
}

const handleFileUpload = () => {
    const files = inputRef.value?.files
    if (files && files.length > 0) {
        validateFile(files[0])
        setInputFileForNewMyVoice(files[0])
        stateNewMyVoice.value.inputFile = files[0]
    }
}

const onResetInputFile = () => {
    clearInputFileForNewMyVoice()
    stateNewMyVoice.value.inputFile = null
    inputRef.value = null
    countUp.value += 1
    fileError.value = null
}
const maxFileSize = 150
const validateFile = (file: File) => {
    const fileSize = file.size / 1024 / 1024

    if (fileSize > maxFileSize) {
        fileError.value = t('File size must be less than {size}MB', {
            size: maxFileSize,
        })
        return
    }
    var reader = new FileReader()
    const filePath = URL.createObjectURL(file)
    var audio = new Audio(filePath)
    reader.onload = function (e) {
        audio.src = e.target.result
        audio.addEventListener(
            'loadedmetadata',
            function () {
                if (!!audio) {
                    stateNewMyVoice.value.file_duration = audio.duration
                    if (isInstantVoiceCloning.value && audio.duration < 30) {
                        fileError.value = t('Audio duration must be at least 30 seconds')
                    } else if (!isInstantVoiceCloning.value && audio.duration < 300) {
                        fileError.value = t('Audio duration must be at least 5 minutes')
                    } else if (audio.duration > 3600) {
                        fileError.value = t('Audio duration must be at most 1 hour')
                    } else {
                        fileError.value = null
                    }
                }
            },
            false
        )
    }
    reader.readAsDataURL(file)
}

// on submit form
const onGenerateVoice = async (event: FormSubmitEvent<Schema>) => {
    const canCreateSpeech = await continueActionAfterCheckAdBlock()
    if (!canCreateSpeech) {
        return
    }
    checkAccessBeforeAction(AccessEnum.IS_CREATE_VOICE, async () => {
        console.log('onGenerateVoice')
        console.log(event.data)
        setMyVoice(stateNewMyVoice.value)
        const isSuccess = await generateMyVoice()
        if (isSuccess) {
            stateNewMyVoice.value = {} as MyVoice
            toast.add({
                id: 'create-my-voice',
                color: 'primary',
                title: t('Success'),
                description: t('Your voice is training. Please wait about 90 minutes.'),
                timeout: 6000,
                icon: 'i-ooui:success',
            })
            await voiceLibraryStore.fetchVoiceLibraryByType(VoiceTypeEnum.USER_VOICE, true)
        } else {
            console.log('error')
            // TODO: Handle error
        }
    })
}

const form = ref()

onUnmounted(() => {
    console.log('🚀 ~ onUnmounted ~ onUnmounted:')
    onResetInputFile()
})

watch(
    () => showNewSettingVoiceModal.value,
    (value) => {
        if (!value) {
            onResetInputFile()
        }
    }
)
</script>

<template>
    <UModal
        v-model="showNewSettingVoiceModal"
        prevent-close
        :ui="{
            width: 'sm:max-w-xl',
        }"
    >
        <UForm
            :key="locale + 'form'"
            ref="form"
            :validate="validate"
            :state="stateNewMyVoice"
            @submit="onGenerateVoice"
        >
            <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
                <template #header>
                    <div class="flex items-center justify-between">
                        <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                            {{ $t('Generate voice') }}
                        </h3>
                        <UButton
                            color="gray"
                            variant="ghost"
                            icon="i-heroicons-x-mark-20-solid"
                            class="-my-1"
                            @click="showNewSettingVoiceModal = false"
                        />
                    </div>
                </template>
                <div class="space-y-4">
                    <UFormGroup size="lg" :label="$t('Name')" class="mb-3" required name="speaker_name">
                        <div class="relative">
                            <UInput
                                :placeholder="$t('Enter name here')"
                                v-model="stateNewMyVoice.speaker_name"
                                :padded="true"
                                :ui="{
                                    rounded: 'rounded-md',
                                }"
                                :maxlength="50"
                            />
                            <div
                                v-if="stateNewMyVoice.speaker_name"
                                class="absolute bottom-[10px] right-3 text-right text-xs font-thin dark:text-gray-300 flex flex-row space-x-4 items-center"
                            >
                                <div>{{ stateNewMyVoice.speaker_name.length }} / {{ 50 }}</div>
                            </div>
                        </div>
                    </UFormGroup>
                    <UFormGroup size="lg" :label="$t('Description')" class="mb-3" name="description">
                        <div class="relative">
                            <UTextarea
                                :placeholder="$t('Enter description here')"
                                v-model="stateNewMyVoice.description"
                                :padded="true"
                                :ui="{
                                    rounded: 'rounded-md',
                                }"
                                :maxlength="500"
                            />
                            <div
                                v-if="stateNewMyVoice.description"
                                class="absolute bottom-[10px] right-3 text-right text-xs font-thin dark:text-gray-300 flex flex-row space-x-4 items-center"
                            >
                                <div>{{ stateNewMyVoice.description.length }} / {{ 500 }}</div>
                            </div>
                        </div>
                    </UFormGroup>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <UFormGroup size="lg" :label="$t('Gender')" class="mb-3" name="gender">
                            <USelectMenu
                                size="lg"
                                :key="locale + stateNewMyVoice.gender"
                                class="w-full inline-flex md:flex-none"
                                v-model="stateNewMyVoice.gender"
                                :options="voiceGenders"
                                :placeholder="$t('Gender')"
                                :popper="{
                                    placement: 'bottom-start',
                                }"
                                value-attribute="value"
                                label-attribute="label"
                            >
                                <template #label>
                                    <span v-if="stateNewMyVoice.gender" class="truncate capitalize">
                                        {{ $t(stateNewMyVoice.gender) }}
                                    </span>
                                    <span v-else>
                                        {{ $t('Gender') }}
                                    </span>
                                </template>
                            </USelectMenu>
                        </UFormGroup>
                        <UFormGroup size="lg" :label="$t('Age')" class="mb-3" name="age">
                            <USelectMenu
                                size="lg"
                                class="w-full inline-flex md:flex-none"
                                v-model="stateNewMyVoice.age"
                                :key="locale + stateNewMyVoice.age"
                                :options="voiceAges"
                                :placeholder="$t('Age')"
                                :popper="{
                                    placement: 'bottom-start',
                                }"
                                value-attribute="value"
                                label-attribute="label"
                            >
                                <template #label>
                                    <span v-if="stateNewMyVoice.age" class="truncate capitalize">{{
                                        $t(stateNewMyVoice.age)
                                    }}</span>
                                    <span v-else>
                                        {{ $t('Age') }}
                                    </span>
                                </template>
                            </USelectMenu>
                        </UFormGroup>
                        <UFormGroup size="lg" :label="$t('Accent')" class="mb-3">
                            <USelectMenu
                                size="lg"
                                searchable
                                class="w-full inline-flex md:flex-none"
                                v-model="stateNewMyVoice.accent"
                                :options="voiceAccents"
                                :placeholder="$t('Accent')"
                                :popper="{
                                    placement: 'bottom-start',
                                }"
                                value-attribute="value"
                                label-attribute="label"
                            >
                                <template #label>
                                    <span v-if="stateNewMyVoice.accent" class="truncate capitalize">{{
                                        stateNewMyVoice.accent
                                    }}</span>
                                    <span v-else>
                                        {{ $t('Accent') }}
                                    </span>
                                </template>
                            </USelectMenu>
                        </UFormGroup>
                    </div>
                    <UFormGroup size="md" :label="$t('Upload')" class="mb-3" required name="inputFile" :error="fileError || ''">
                        <UAlert :title="$t('Note')" color="yellow" variant="subtle">
                            <template #description>
                                <div>
                                    -
                                    {{
                                        isInstantVoiceCloning
                                            ? $t(
                                                  'The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.'
                                              )
                                            : $t(
                                                  'The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.'
                                              )
                                    }}
                                </div>
                                <div class="mt-2">
                                    -
                                    {{
                                        isInstantVoiceCloning
                                            ? $t('It will cost {credits} credits each time you create a voice.', {
                                                  credits: '0',
                                              })
                                            : $t('It will cost {credits} credits each time you create a voice.', {
                                                  credits: '5,000',
                                              })
                                    }}
                                </div>
                            </template>
                        </UAlert>
                        <br />
                        <div
                            v-show="newMyVoice.inputFile"
                            class="absolute right-1 cursor-pointer inline-flex justify-center p-2 text-gray-500 rounded-full"
                            @click="onResetInputFile"
                        >
                            <svg
                                class="w-6 h-6"
                                aria-hidden="true"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                        </div>
                        <label
                            ref="dropZoneRef"
                            id="dropzone-file-input"
                            @drop.prevent="dropHandler"
                            @dragover="dragOverHandler"
                            for="dropzone-file-voice"
                            class="flex flex-col items-center rounded-lg justify-center h-40 w-full cursor-pointer border-2 border-dashed dark:border-gray-800"
                        >
                            <div class="relative" v-if="newMyVoice.inputFile">
                                <a
                                    href="#"
                                    class="relative flex md:min-w-[330px] flex-inline space-x-3 max-w-sm md:max-w-sm px-4 py-2 bg-gray-100 rounded-lg hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700"
                                >
                                    <div>
                                        <svg
                                            class="w-10 h-10 mb-3 text-gray-400"
                                            aria-hidden="true"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="1.5"
                                            viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            ></path>
                                        </svg>
                                    </div>
                                    <div class="truncate">
                                        <p class="font-normal text-gray-700 dark:text-gray-400 truncate">
                                            {{ newMyVoice.inputFile?.name }}
                                        </p>
                                        <p class="text-sm font-thin text-gray-700 dark:text-gray-400">
                                            {{ $fileSizeFormat(newMyVoice.inputFile?.size || 0) }}
                                        </p>
                                    </div>
                                </a>
                            </div>
                            <div v-else class="flex flex-col items-center justify-center pt-5 pb-6">
                                <svg
                                    aria-hidden="true"
                                    class="w-10 h-10 mb-3 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                                    ></path>
                                </svg>
                                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                                    <span class="font-semibold">
                                        {{ $t('Click to upload') }}
                                    </span>
                                    <br />
                                    {{ $t('or drag and drop your file here') }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    <span class="font-semibold"> {{ $t('.mp3, .wav, .flac') }} </span>
                                </p>
                                <p class="text-xs text-center text-gray-500 dark:text-gray-400 mt-1">
                                    {{ $t('And the maximum file size is {size}MB.', {
                                        size: maxFileSize,
                                    }) }}
                                </p>
                            </div>
                            <input
                                :key="countUp"
                                id="dropzone-file-voice"
                                type="file"
                                class="hidden"
                                @change="handleFileUpload"
                                :disabled="isUploading"
                                :accept="`.mp3,.wav,.flac`"
                                ref="inputRef"
                            />
                        </label>
                        <template #error="{ error }">
                            <span
                                :class="[
                                    fileError
                                        ? 'text-red-500 dark:text-red-400'
                                        : 'text-primary-500 dark:text-primary-400',
                                ]"
                            >
                                {{ fileError }}
                            </span>
                        </template>
                    </UFormGroup>
                    <UFormGroup size="lg" class="mb-3" required name="isAgreed">
                        <div class="flex flex-row items-center">
                            <UCheckbox v-model="isAgreed">
                                <template #label>
                                    <span>{{ $t('I agree to the') }}</span>
                                </template>
                            </UCheckbox>
                            <span @click="showPolicyModal = true" class="ml-1 text-primary underline cursor-pointer">
                                {{ t('privacy policy') }}
                            </span>
                        </div>
                    </UFormGroup>
                    <br />
                    <UAlert
                        v-if="errorMessages.uploadFile"
                        :title="$t('Error')"
                        :description="$t(errorMessages.uploadFile)"
                        color="red"
                        variant="subtle"
                    />
                    <UAlert
                        v-if="errorMessages.generateMyVoice"
                        :title="$t('Error')"
                        :description="$t(errorMessages.generateMyVoice)"
                        color="red"
                        variant="subtle"
                    />
                </div>

                <template #footer>
                    <div class="flex flex-row gap-2 justify-end">
                        <UButton color="white" @click="showNewSettingVoiceModal = false" class="px-6">
                            {{ t('Cancel') }}
                        </UButton>
                        <!-- Icon next -->
                        <UButton
                            :disabled="!isAgreed || fileError || !stateNewMyVoice.inputFile"
                            icon="i-heroicons-arrow-right"
                            :trailing="true"
                            :ui="{
                                inline: 'md:justify-between justify-center',
                                icon: {
                                    size: {
                                        xl: 'w-14 h-14 md:w-6 md:h-6',
                                    },
                                },
                            }"
                            color="primary"
                            class="px-6"
                            type="submit"
                            :loading="loadings.btnGenerateMyVoice"
                        >
                            {{ t('Generate') }}
                        </UButton>
                    </div>
                </template>
            </UCard>
        </UForm>
    </UModal>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash'

definePageMeta({
    layout: false,
})
const audio = ref<HTMLAudioElement>(new Audio())
const storyStore = useStoryStore()
const voiceLibraryStore = useVoiceLibraryStore()
const { myVoiceLibraries, loadings, selectedVoiceLibrary } = storeToRefs(voiceLibraryStore)

const { activeStory } = storeToRefs(storyStore)

const isPlaying = ref(false)

const emits = defineEmits(['close', 'select'])

const togglePlay = (voice: any) => {
    if (voice?.id !== selectedVoiceLibrary.value?.id) {
        selectedVoiceLibrary.value = cloneDeep(voice)
        // update audio src
        audio.value.src = voice.sample_audio_path
        // isPlaying.value = true;
        nextTick(() => {
            audio.value.play()
        })
    } else {
        if (isPlaying.value) {
            isPlaying.value = false
            audio.value.pause()
        } else {
            isPlaying.value = true
            audio.value.play()
        }
    }
}

const onPlay = () => {
    isPlaying.value = true
}
</script>

<template>
    <template v-if="myVoiceLibraries?.length === 0">
        <MyVoiceNewCard />
    </template>
    <template v-else>
        <template v-for="voiceLibrary in myVoiceLibraries" :key="voiceLibrary.id">
            <BaseVoiceCard
                :add_new_card="myVoiceLibraries?.indexOf(voiceLibrary) === 0"
                :data-id="voiceLibrary.id"
                v-bind="voiceLibrary"
                :active="activeStory?.voice_id === voiceLibrary.id"
                @select="(id) => emits('select', id)"
                @toggle-favorite="voiceLibraryStore.toggleFavorite"
                :updating="loadings['toggleFavorite'][voiceLibrary.id]"
                :playing="isPlaying"
                @toggle-play="togglePlay"
                :loading="loadings['fetchVoiceLibraryByType']?.['user_voice']"
            />
        </template>
        <audio :src="selectedVoiceLibrary?.sample_audio_path" ref="audio" @play="onPlay" />
    </template>
</template>

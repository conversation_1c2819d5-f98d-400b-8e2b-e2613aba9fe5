<script setup lang="ts">
import { useMyVoiceStore } from '@/stores/my-voice'
import { AccessEnum } from '~/types';
const myVoiceStore = useMyVoiceStore()
const { showSelectVoiceTypeModal } = storeToRefs(myVoiceStore)
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const { checkAccessBeforeAction } = useAuthStore()

const onCreateNewVoice = () => {
    if (user.value) {
        checkAccessBeforeAction(AccessEnum.IS_CREATE_VOICE, () => {
            showSelectVoiceTypeModal.value = true
        })
    } else {
        navigateTo('/signin')
    }
}
</script>

<template>
    <UCard
        @click="onCreateNewVoice"
        class="hover:cursor-pointer hover:shadow-md fixed-height"
    >
        <div class="flex flex-col justify-center items-center py-3 gap-2 text-gray-400">
            <UIcon name="i-simple-line-icons-plus" class="text-5xl text-gray-400" />
            <div class="">{{ $t('Create a new voice') }}</div>
        </div>
    </UCard>
    <SelectVoiceTypeModal />
    <NewSettingVoiceModal />
    <PolicyModal />
</template>

<style scoped>
    .fixed-height {
        height: 175px;
    }
</style>

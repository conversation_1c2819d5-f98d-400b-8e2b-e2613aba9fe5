<script setup lang="ts">
import { useMyVoiceStore } from '@/stores/my-voice'
import { MyVoiceType } from '~/types'
const myVoiceStore = useMyVoiceStore()
const { showSelectVoiceTypeModal } = storeToRefs(myVoiceStore)
const { setVoiceTypeForNewMyVoice } = myVoiceStore

</script>

<template>
    <UModal v-model="showSelectVoiceTypeModal" prevent-close>
        <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
            <template #header>
                <div class="flex items-center justify-between">
                    <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">{{ $t('Type of voice to create') }}</h3>
                    <UButton
                        color="gray"
                        variant="ghost"
                        icon="i-heroicons-x-mark-20-solid"
                        class="-my-1"
                        @click="showSelectVoiceTypeModal = false"
                    />
                </div>
            </template>
            <div class="space-y-4">
                <button
                    class="flex items-center btn btn-white btn-xl btn-normal flex flex-col items-center border border-gray-200  rounded-md md:flex-row md:max-w-xl transition-colors w-full text-left justify-start p-0 dark:border-gray-700 hover:dark:border-gray-600 dark:text-white cursor-pointer shadow hover:ring-gray-900 hover:ring-1"
                    type="button"
                    tabindex="0"
                    @click="setVoiceTypeForNewMyVoice(MyVoiceType.INSTANT_VOICE_CLONING)"
                >
                    <div class="flex flex-col justify-between px-4 py-3 leading-normal w-full">
                        <span class="block font-serif text-lg font-semibold text-gray-900"
                            ><div class="flex items-center dark:text-white" data-testid="voicelab-voice-cloning-button">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                    aria-hidden="true"
                                    class="h-5 w-5 mr-2 mb-1 -ml-0.5"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
                                    ></path></svg
                                >
                                {{ $t('Instant Voice Cloning') }}
                            </div></span
                        ><span class="block font-serif text-sm font-normal text-gray-700"
                            ><div class="mt-2 dark:text-white">
                                {{ $t('Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise.') }}
                            </div>
                        </span>
                    </div>
                </button>
                <button
                    class="flex items-center btn btn-white btn-xl btn-normal flex flex-col items-center border border-gray-200 rounded-md md:flex-row md:max-w-xl transition-colors w-full text-left justify-start p-0 dark:border-gray-700 hover:dark:border-gray-600 dark:text-white cursor-pointer shadow hover:ring-gray-900 hover:ring-1"
                    type="button"
                    tabindex="0"
                    @click="setVoiceTypeForNewMyVoice(MyVoiceType.PROFESSIONAL_VOICE_CLONING)"
                >
                    <div class="flex flex-col justify-between px-4 py-3 leading-normal w-full">
                        <span class="block font-serif text-lg font-semibold text-gray-900"
                            ><div class="flex flex-col">
                                <div class="flex items-center dark:text-white">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                        aria-hidden="true"
                                        class="h-5 w-5 mr-2 mb-0.5 -ml-0.5 text-amber-400"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M16.403 12.652a3 3 0 000-5.304 3 3 0 00-3.75-3.751 3 3 0 00-5.305 0 3 3 0 00-3.751 3.75 3 3 0 000 5.305 3 3 0 003.75 3.751 3 3 0 005.305 0 3 3 0 003.751-3.75zm-2.546-4.46a.75.75 0 00-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                                            clip-rule="evenodd"
                                        ></path></svg
                                    >{{ $t('Professional Voice Cloning') }}
                                </div>
                            </div></span
                        ><span class="block font-serif text-sm font-normal text-gray-700"
                            ><div class="mt-2 dark:text-white">{{ $t('Create the most realistic digital replica of your voice.') }}</div></span
                        >
                    </div>
                </button>
            </div>
        </UCard>
    </UModal>
</template>

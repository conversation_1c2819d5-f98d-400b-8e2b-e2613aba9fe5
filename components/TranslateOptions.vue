<template>
    <div>
        <div class="grid grid-cols-2 md:grid-cols-2 gap-2 p-2 pb-2 mx-auto rounded-e-md" role="group">
            <TranslateSelectTone />
            <TranslateSelectSpeed />
        </div>
        <div
            v-if="isUsingFreePlan"
            class="mb-8 md:mb-0 flex flex-row space-x-2 py-2 px-3 text-sm text-yellow-800 bg-yellow-100 dark:bg-gray-800 dark:text-yellow-300 rounded-b-lg"
        >
            <svg
                class="w-4 h-4 text-yellow-800 dark:text-yellow-300"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="currentColor"
                viewBox="0 0 18 19"
            >
                <path
                    d="M15 1.943v12.114a1 1 0 0 1-1.581.814L8 11V5l5.419-3.871A1 1 0 0 1 15 1.943ZM7 4H2a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2v5a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2V4ZM4 17v-5h1v5H4ZM16 5.183v5.634a2.984 2.984 0 0 0 0-5.634Z"
                />
            </svg>
            <div class="cursor-pointer hover:underline" @click="navigateTo('/pricing')">
                {{ $t('Upgrade your plan to use GPT-4 for more accurate translations!') }}
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import TranslateSelectWritingStyle from '~/components/TranslateSelectWritingStyle.vue'
import TranslateSelectDomain from '~/components/TranslateSelectDomain.vue'
import TranslateSelectTone from '~/components/TranslateSelectTone.vue'
import { useAuthStore } from '~/stores/auth'
import { storeToRefs } from 'pinia'

const { isUsingFreePlan } = storeToRefs(useAuthStore())
</script>

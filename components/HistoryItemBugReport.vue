<template>
    <div class="mr-0">
        <button
            :id="'popoverButton_' + uuid"
            type="button"
            class="cursor-pointer inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:outline-none focus:ring-gray-200 focus:text-blue-700 dark:bg-gray-800 dark:text-primary-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700"
        >
            {{ $t('Report a bug') }}
        </button>
        <div
            data-popover
            :id="'popoverReportBug_' + uuid"
            role="tooltip"
            class="absolute z-10 invisible inline-block w-64 text-sm text-gray-500 transition-opacity duration-300 bg-white border border-gray-200 rounded-lg shadow-sm opacity-0 dark:text-gray-400 dark:border-gray-600 dark:bg-gray-800"
        >
            <div
                class="px-3 py-2 bg-gray-100 border-b border-gray-200 rounded-t-lg dark:border-gray-600 dark:bg-gray-700"
            >
                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $t('Report a bug') }}</h3>
            </div>
            <div class="grid h-full max-w-lg grid-cols-2 mx-auto px-2 py-6">
                <a
                    :href="`mailto:<EMAIL>?subject=Bug report&body=・UUID: ${uuid}`"
                    class="cursor-pointer inline-flex flex-col items-center justify-center font-medium px-5 hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <svg
                        class="w-5 h-5 mb-1 text-gray-500 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-500"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 16"
                    >
                        <path
                            d="m10.036 8.278 9.258-7.79A1.979 1.979 0 0 0 18 0H2A1.987 1.987 0 0 0 .641.541l9.395 7.737Z"
                        />
                        <path
                            d="M11.241 9.817c-.36.275-.801.425-1.255.427-.428 0-.845-.138-1.187-.395L0 2.6V14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2.5l-8.759 7.317Z"
                        />
                    </svg>
                    <span
                        class="text-sm text-gray-500 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-500"
                        >{{ $t('Via email') }}</span
                    >
                </a>
                <button
                    @click="openReportBugModal"
                    type="button"
                    class="inline-flex flex-col items-center justify-center font-medium px-5 hover:bg-gray-50 dark:hover:bg-gray-800 group"
                >
                    <svg
                        class="w-6 h-6 mb-1 text-gray-500 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-500"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 18"
                    >
                        <path
                            d="M12.687 14.408a3.01 3.01 0 0 1-1.533.821l-3.566.713a3 3 0 0 1-3.53-3.53l.713-3.566a3.01 3.01 0 0 1 .821-1.533L10.905 2H2.167A2.169 2.169 0 0 0 0 4.167v11.666A2.169 2.169 0 0 0 2.167 18h11.666A2.169 2.169 0 0 0 16 15.833V11.1l-3.313 3.308Zm5.53-9.065.546-.546a2.518 2.518 0 0 0 0-3.56 2.576 2.576 0 0 0-3.559 0l-.547.547 3.56 3.56Z"
                        />
                        <path
                            d="M13.243 3.2 7.359 9.081a.5.5 0 0 0-.136.256L6.51 12.9a.5.5 0 0 0 .59.59l3.566-.713a.5.5 0 0 0 .255-.136L16.8 6.757 13.243 3.2Z"
                        />
                    </svg>
                    <span
                        class="text-sm text-gray-500 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-500"
                        >{{ $t('Via form') }}</span
                    >
                </button>
            </div>
            <div data-popper-arrow></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useHistoryStore } from '~/stores/history'
import BugReportModal from '~/base-components/BugReportModal.vue'
import { ModalsContainer, useModal } from 'vue-final-modal'
import { Popover } from 'flowbite'
import type { PopoverOptions, PopoverInterface } from 'flowbite'

const historyStore = useHistoryStore()

const props = defineProps<{
    uuid: string
    rating: string
    flat: boolean
}>()

const emit = defineEmits(['onRated'])

const { open, close, options } = useModal({
    component: BugReportModal,
    attrs: {
        onClose() {
            close()
        },
        onSubmit() {
            close()
        },
        uuid: props.uuid,
    },
})

const openReportBugModal = () => {
    options.attrs.uuid = props.uuid
    open()
}

const popover = ref<PopoverInterface | null>(null)
onMounted(() => {
    // set the popover content element
    const $targetEl: HTMLElement = document.getElementById('popoverReportBug_' + props.uuid)

    // set the element that trigger the popover using hover or click
    const $triggerEl: HTMLElement = document.getElementById('popoverButton_' + props.uuid)

    // options with default values
    const options: PopoverOptions = {
        placement: 'bottom',
        triggerType: 'hover',
        offset: 10,
        onHide: () => {
            console.log('popover is shown')
        },
        onShow: () => {
            console.log('popover is hidden')
        },
        onToggle: () => {
            console.log('popover is toggled')
        },
    }

    // create the popover instance
    popover.value = new Popover($targetEl, $triggerEl, options)
})
</script>

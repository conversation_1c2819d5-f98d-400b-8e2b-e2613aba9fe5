<template>
    <li class="mb-10 ml-8">
        <span
            class="absolute flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full -left-4 ring-8 ring-gray-50 dark:ring-gray-900 dark:bg-blue-900"
        >
            <UIcon name="i-fluent:people-chat-24-regular" class="text-xl text-blue-500 dark:text-blue-300" />
        </span>
        <div
            class="px-4 pt-2 pb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600"
            :class="{
                'group hover:shadow-xl duration-200': selectable,
            }"
        >
            <div class="items-center justify-between mb-5 sm:flex">
                <time
                    class="group cursor-pointer flex flex-inline space-x-2 items-center text-xs font-normal text-gray-400 sm:order-last sm:mb-0 justify-between sm:justify-start"
                >
                    <div class="group-hover:hidden">{{ createdAtFormat }}</div>
                    <div class="hidden group-hover:block">{{ createdAtRawFormat }}</div>
                    <HistoryItemMenu v-if="!mini" :item="props" />
                    <UButton
                        v-if="selectable"
                        icon="material-symbols:playlist-add-check-circle-outline"
                        size="sm"
                        color="primary"
                        variant="solid"
                        :label="$t('Select')"
                        :trailing="false"
                        class="sm:hidden group-hover:flex"
                        @click="emit('select', props)"
                    />
                </time>
                <div class="flex flex-row items-center text-sm font-normal text-gray-500 lex dark:text-gray-300">
                    <!-- <a
            class="flex flex-row items-center font-base text-gray-900 dark:text-white mr-2"
          >
            <UAvatar
              :src="`/assets/images/avatars/${voice}.svg`"
              :alt="voice"
              size="xs"
            />
          </a>
          <span
            class="bg-primary-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-primary-600 dark:text-gray-300"
          >
            {{ $t(voice) }}</span
          > -->
                    <span
                        class="bg-yellow-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-600 dark:text-gray-300"
                    >
                        {{ $t('credits', { value: props.used_credit }) }}</span
                    >
                    <span
                        class="bg-blue-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-blue-600 dark:text-gray-300"
                    >
                        {{ $t(model_name || 'tts-1') }}</span
                    >
                    <span
                        v-if="output_format"
                        class="bg-green-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-green-600 dark:text-gray-300"
                    >
                        <UIcon name="ic:round-audio-file" class="text-xs" />
                        {{ output_format?.toUpperCase() }}
                    </span>
                    <span
                        v-if="output_channel"
                        class="bg-orange-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-orange-600 dark:text-gray-300"
                    >
                        <UIcon :name="output_channel === 'mono' ? 'i-material-symbols-speaker-outline' : 'i-material-symbols-speaker-group-outline'" class="text-xs" />
                        {{ $t(output_channel === 'mono' ? 'Mono' : 'Stereo') }}
                    </span>
                    <span
                        v-if="props.custom_prompt"
                        class="bg-teal-500 text-gray-100 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-teal-300 dark:text-gray-700"
                    >
                        {{ $t('Custom prompt') }}</span
                    >
                </div>
            </div>

            <div class="flex flex-col space-y-3">
                <a
                    class="cursor-pointer p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                >
                    <div :class="isShowFull ? 'break-words whitespace-break-spaces' : 'truncate'">
                        {{ props.name || $t('Noname story') }}
                    </div>
                </a>
                <div class="flex justify-center text-primary-500 dark:text-primary-300">
                    <template v-if="!media_url">
                        <UAlert
                            v-if="props.status === 3"
                            :description="$t('Media is not found')"
                            :title="$t('Media load failed')"
                            color="yellow"
                            variant="subtle"
                        />
                        <UIcon
                            v-else-if="props.status === 4"
                            name="mdi:cancel-network"
                            class="text-4xl text-gray-500"
                        />
                        <UIcon v-else name="i-line-md:downloading-loop" class="text-4xl" />
                    </template>
                    <svg
                        v-else
                        class="w-5 h-5"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                </div>
                <template v-if="!media_url && props.status === 3">
                    <!-- Empty template -->
                </template>
                <a
                    v-else
                    class="flex flex-row items-center gap-2 p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                >
                    <div v-if="props.status === 4">
                        {{ $t('The process has been stopped.') }}
                    </div>
                    <HistoryItemProcessing v-else-if="!media_url" :item="props" />
                    <div
                        v-else-if="loadingAudio"
                        class="flex flex-1 md:flex-row gap-2 flex-col justify-start items-start text-sm"
                    >
                        <UIcon name="i-eos-icons:loading" class="md:text-2xl text-4xl" />

                        <div>
                            {{ $t('Loading audio...') }}
                        </div>
                    </div>
                    <div v-show="media_url && !loadingAudio" class="flex flex-col w-full gap-2">
                        <div class="font-semibold">
                            {{ $t('Full story audio file') }}
                        </div>
                        <div class="flex flex-row gap-4 items-center w-full">
                            <audio
                                v-show="media_url && !loadingAudio"
                                :key="media_url"
                                controls
                                class="flex-1 rounded-full bg-primary-300"
                                :controlsList="canAccess(AccessEnum.IS_DOWNLOAD) ? '' : 'nodownload'"
                                @loadeddata="onLoaded"
                            >
                                <source :src="media_url" type="audio/mpeg" />
                                Your browser does not support the audio element.
                            </audio>
                            <a
                                v-if="canAccess(AccessEnum.IS_DOWNLOAD) && media_url"
                                @click="downloadFile"
                                class="cursor-pointer"
                            >
                                <UIcon
                                    :name="isDownloading ? 'eos-icons:loading' : 'i-material-symbols:download'"
                                    class="text-2xl dark:text-gray-300"
                                />
                            </a>
                        </div>
                    </div>
                </a>

                <div v-if="media_url && !loadingAudio" class="pt-2">
                    <UAccordion :items="items" variant="solid" color="gray" v-model="openDetails">
                        <template #details-block>
                            <div
                                v-if="loadings?.fetchStoryDetail[uuid]"
                                class="text-gray-900 dark:text-white text-center"
                            >
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                    <UIcon name="i-eos-icons-loading" class="text-xl" />

                                    {{ $t('Loading...') }}
                                </p>
                            </div>
                            <div v-else>
                                <HistoryItemStoryDetail :details="props.details" :uuid="props.uuid" />
                            </div>
                        </template>
                    </UAccordion>
                </div>
            </div>
            <div class="shrink-0 mt-4 flex justify-between items-center">
                <UTooltip :text="$t('Click to copy')">
                    <UButton
                        size="xs"
                        color="gray"
                        variant="soft"
                        :label="isCopied ? $t('Copied to clipboard') : props.uuid"
                        :trailing="false"
                        :ui="{}"
                        @click="onCopyUUIDToClipboard"
                        class="animate__animated"
                        :class="isCopied ? 'animate__flash' : ''"
                    >
                        <template #leading>
                            <UIcon
                                :name="isCopied ? 'i-tabler-copy-check-filled' : 'i-f7-grid'"
                                class="text-xs"
                                :class="isCopied ? 'text-green-500' : 'text-gray-500'"
                            />
                        </template>
                    </UButton>
                </UTooltip>

                <div v-if="!mini" class="shrink-0 flex justify-end">
                    <HistoryItemRate @onRated="onRated" :uuid="props.uuid" :rating="props.rating" />
                </div>
            </div>
        </div>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import HistoryItemRate from './HistoryItemRate.vue'
import { AccessEnum } from '~/types'

dayjs.extend(utc)
dayjs.extend(relativeTime)

const props = defineProps<{
    uuid: string
    tts_input: string
    media_url: string
    custom_prompt: string
    created_at: string
    voice: string
    target_lang: string
    used_credit: number
    status: number
    model: string
    rating: string
    error_message: string
    status_percentage: number
    name?: string
    details?: any
    type: string
    id: number
    selectable?: boolean
    mini?: boolean
    isDownloading: boolean
    model_name: string
    output_format?: string
    output_channel?: string
}>()
const emit = defineEmits(['delete', 'rated', 'download', 'select'])
const historyStore = useHistoryStore()
const { loadings } = storeToRefs(historyStore)
const isShowFull = ref(false)
const toggleIsShowFull = (e: any) => {
    console.log('🚀 ~ toggleIsShowFull ~ e:', e.target.tagName)
    // if not click on the button, then toggle
    if (!['BUTTON', 'svg'].includes(e.target.tagName)) {
        isShowFull.value = true
    }
}
const createdAtFormat = computed(() => dayjs().to(dayjs.utc(props.created_at)))
const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))
const authStore = useAuthStore()
const { canAccess } = storeToRefs(authStore)
const { checkAccessBeforeAction } = useAuthStore()
const onRated = (rated: any) => {
    emit('rated', { ...rated, uuid: props.uuid })
}

const downloadFile = () => {
    checkAccessBeforeAction(AccessEnum.IS_DOWNLOAD, () => {
        if (props.status === 2) {
            emit('download')
        }
    })
}

const loadingAudio = ref(false)
const onLoaded = () => {
    loadingAudio.value = false
}

onMounted(() => {
    loadingAudio.value = true

    // check if device is IOS
    if (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) {
        loadingAudio.value = false
    }

    if (!props.details) {
        historyStore.fetchStoryDetail(props.uuid)
    }
})
const { t } = useI18n()
const openDetails = ref()
const items = computed(() => {
    return [
        {
            label: t('Details for each block'),
            icon: 'i-mingcute-more-3-line',
            defaultOpen: false,
            slot: 'details-block',
        },
    ]
})

const isCopied = ref(false)
const onCopyUUIDToClipboard = () => {
    navigator.clipboard.writeText(props.uuid)
    isCopied.value = true
    setTimeout(() => {
        isCopied.value = false
    }, 2000)
}
</script>

<script setup lang="ts">
import { formatTimeAgo } from '@vueuse/core'
import type { Notification } from '~/types'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
dayjs.extend(utc)
dayjs.extend(relativeTime)

const appStore = useAppStore()
const { isNotificationsSlideoverOpen } = storeToRefs(appStore)

const props = defineProps<{
    notifications: Notification[]
    showFetchNext: boolean
    isFetching: boolean
}>()
const { t } = useI18n()
const emits = defineEmits(['onNotificationDetail', 'fetchMore', 'markAllAsRead'])

const notificationText = (notification: Notification): string => {
    switch (notification.event_type) {
        case 'tts_history':
            // document
            if (notification?.type === 'tts-document') {
                if (notification.success || notification.status === 2) {
                    return 'Your document [{0}] has been converted to audio successfully!'
                } else {
                    return 'Your document [{0}] has failed to vocalize.'
                }
            }
            // story
            if (notification?.type === 'tts-story') {
                if (notification.success || notification.status === 2) {
                    return 'Your story [{0}] has been converted to audio successfully!'
                } else {
                    return 'Your story [{0}] has failed to vocalize.'
                }
            }

            // text
            if (notification.success || notification.status === 2) {
                return 'Your text [{0}] has been converted to audio successfully!'
            } else {
                return 'Your text [{0}] has failed to vocalize.'
            }

        case 'voice_training':
            if (notification.status === 2) {
                return 'Your voice training has been completed successfully!'
            } else {
                return 'Your voice training has failed!'
            }
        default:
            if (notification.success || notification.status === 2) {
                return 'Your text [{0}] has been converted to audio successfully!'
            } else {
                return 'Your text [{0}] has failed to vocalize.'
            }
    }
}

const notificationTarget = (notification: Notification): string => {
    console.log('🚀 ~ notificationTarget ~ notification:', notification)
    switch (notification.event_type) {
        case 'tts_history':
            // document
            if (notification?.type === 'tts-document') {
                return shortenString(notification.tts_input)
            }
            // story
            if (notification?.type === 'tts-story') {
                return shortenString(notification.name)
            }

            // text
            return shortenString(notification.tts_input || notification.input)

        case 'voice_training':
            return ''

        default:
            if (['tts-text', 'tts-text-emotion'].includes(notification?.type)) {
                return shortenString(notification.tts_input)
            }
    }
}

const notificationIcon = (notification: Notification): string => {
    if (!notification.success && notification.status !== 2) {
        return 'i-iconoir-voice-xmark'
    }
    switch (notification.event_type) {
        case 'tts_history':
            if (notification?.type === 'tts-document') {
                return 'i-mdi-file-document-outline'
            }
            if (notification?.type === 'tts-story') {
                return 'i-fluent:people-chat-24-regular'
            }
            return 'i-material-symbols-translate'
            return 'i-fluent-person-voice-16-filled'
        case 'voice_training':
            return 'i-ri-voice-ai-fill'
        default:
            return 'i-material-symbols-translate'
    }
}

const notificationTitle = (notification: Notification): string => {
    switch (notification.event_type) {
        case 'tts_history':
            if (notification.success || notification.status === 2) {
                return 'Vocalize success'
            } else {
                return 'Vocalize failed'
            }
        case 'voice_training':
            if (notification.status === 2) {
                return 'Voice training success'
            } else {
                return 'Voice training failed'
            }
        default:
            return ''
    }
}

const validNotifications = computed(() => {
    return props.notifications.filter((notification) =>
        ['tts_history', 'voice_training'].includes(notification.event_type)
    )
})
</script>

<template>
    <UDashboardSlideover v-model="isNotificationsSlideoverOpen" :title="$t('Notifications')">
        <NuxtLink
            v-for="notification in validNotifications"
            :key="notification.id"
            @click="emits('onNotificationDetail', notification)"
            class="p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer flex items-center gap-3 relative"
        >
            <UChip color="primary" :show="!notification.seen" inset>
                <UAvatar
                    :icon="notificationIcon(notification)"
                    size="md"
                    :class="{
                        'bg-primary-200': !notification.seen && (notification.success || notification.status === 2),
                        'bg-red-200 dark:bg-red-300':
                            !notification.seen && !notification.success && notification.status !== 2,
                        'bg-gray-200 dark:bg-gray-700': notification.seen,
                    }"
                    :ui="{
                        icon: {
                            base:
                                notification.success || notification.status === 2
                                    ? 'text-primary-500'
                                    : 'text-red-300 dark:text-red-500',
                        },
                    }"
                />
            </UChip>

            <div class="text-sm flex-1">
                <p class="flex items-center justify-between">
                    <span
                        class=""
                        :class="{
                            'font-semibold text-primary-500':
                                !notification.seen && (notification.success || notification.status === 2),
                            'font-semibold text-red-300 dark:text-red-800':
                                !notification.seen && !notification.success && notification.status !== 2,
                            'font-medium text-gray-900 dark:text-white': notification.seen,
                        }"
                        >{{ $t(notificationTitle(notification)) }}</span
                    >

                    <time
                        :datetime="notification.created_at"
                        class="text-gray-500 dark:text-gray-400 text-xs"
                        v-text="dayjs.utc(notification.created_at).fromNow()"
                    />
                </p>
                <i18n-t :keypath="notificationText(notification)" tag="p" class="text-gray-500 dark:text-gray-400">
                    <a class="font-semibold underline !break-all">{{ notificationTarget(notification) }}</a>
                </i18n-t>
                <p class="text-gray-500 dark:text-gray-400"></p>
            </div>
        </NuxtLink>
        <div v-if="!validNotifications.length" class="flex flex-col items-center justify-center h-full gap-3">
            <UIcon
                name="material-symbols:circle-notifications"
                class="mx-auto text-5xl text-gray-300 dark:text-gray-700"
            />
            <p class="text-center text-gray-500 dark:text-gray-400">
                {{ $t('No notifications') }}
            </p>
        </div>
        <UButton
            v-if="showFetchNext"
            :label="$t('See more notifications')"
            class="flex justify-center mx-auto my-2 px-5"
            size="xs"
            variant="solid"
            color="gray"
            @click="emits('fetchMore')"
            :loading="isFetching"
        />
        <!-- <template #footer>
      <UButton
        v-if="notifications?.length > 0"
        :label="$t('Mark all as read')"
        variant="ghost"
        color="gray"
        @click="emits('markAllAsRead')"
        icon="i-circum-read"
        block
      />
    </template> -->
    </UDashboardSlideover>
</template>

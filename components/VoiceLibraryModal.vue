<template>
  <UModal
    v-model="showVoiceLibrariesModal"
    :ui="{
      container: 'items-start',
      width: 'sm:max-w-7xl w-screen',
    }"
    prevent-close
  >
    <VoiceLibrary class="flex-1" full @select="(id) => $emit('select', id)" show-close-button @close="showVoiceLibrariesModal = false"/>
  </UModal>
</template>

<script setup lang="ts">
const storyStore = useStoryStore();
const { showVoiceLibrariesModal } = storeToRefs(storyStore);
</script>

<template>
    <li class="mb-10 ml-8">
        <span
            class="absolute flex items-center justify-center w-8 h-8 bg-primary-100 rounded-full -left-4 ring-8 ring-gray-50 dark:ring-gray-900 dark:bg-primary-900"
        >
            <svg
                class="w-5 h-5 text-primary-800 dark:text-primary-300"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M10.5 21l5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 016-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 01-3.827-5.802"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                ></path>
            </svg>
        </span>
        <div
            class="px-4 pt-2 pb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="items-center justify-between mb-5 sm:flex">
                <time
                    class="group cursor-pointer flex flex-inline space-x-2 items-center text-xs font-normal text-gray-400 sm:order-last sm:mb-0 justify-between sm:justify-start"
                >
                    <div class="group-hover:hidden">{{ createdAtFormat }}</div>
                    <div class="hidden group-hover:block">{{ createdAtRawFormat }}</div>
                    <HistoryItemMenu :item="props" />
                </time>
                <div
                    class="flex flex-row items-center text-sm font-normal text-gray-500 flex-wrap gap-2 dark:text-gray-300"
                >
                    <!-- <a class="flex flex-row items-center font-base text-gray-900 dark:text-white ">
                        <UAvatar :src="`/assets/images/avatars/${voice}.svg`" :alt="voice" size="xs" />
                    </a> -->
                    <span
                        v-if="voice_id"
                        class="bg-red-100 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-primary-600 dark:text-gray-300"
                    >
                        {{ voice_id }}</span
                    >
                    <span
                        class="bg-primary-100 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-primary-600 dark:text-gray-300 capitalize"
                    >
                        {{ speaker_name }}</span
                    >
                    <span
                        v-if="accent_name"
                        class="bg-primary-200 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-primary-700 dark:text-gray-300 capitalize"
                    >
                        {{ accent_name }}</span
                    >
                    <span
                        class="bg-yellow-100 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-yellow-600 dark:text-gray-300"
                    >
                        {{ $t('credits', { value: props.used_credit || 0 }) }}</span
                    >
                    <span
                        class="bg-blue-100 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-blue-600 dark:text-gray-300"
                    >
                        {{ $t(model_name || 'tts-1') }}</span
                    >
                    <span
                        class="bg-violet-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-violet-600 dark:text-gray-300"
                    >
                        <UIcon name="i-fluent-fast-forward-28-filled" class="text-xs" />
                        {{ speed }}x
                    </span>
                    <span
                        v-if="output_format"
                        class="bg-green-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-green-600 dark:text-gray-300"
                    >
                        <UIcon name="ic:round-audio-file" class="text-xs" />
                        {{ output_format?.toUpperCase() }}
                    </span>
                    <span
                        v-if="output_channel"
                        class="bg-orange-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-orange-600 dark:text-gray-300"
                    >
                        <UIcon :name="output_channel === 'mono' ? 'i-material-symbols-speaker-outline' : 'i-material-symbols-speaker-group-outline'" class="text-xs" />
                        {{ $t(output_channel === 'mono' ? 'Mono' : 'Stereo') }}
                    </span>
                    <span v-if="emotion" class="">
                        <UButton
                            size="xs"
                            color="gray"
                            variant="soft"
                            :label="$t(emotionObject(emotion)?.name || '')"
                            :trailing="false"
                            class="group"
                        >
                            <template #leading>
                                <div class="w-3 h-3 flex items-center justify-center">
                                    <img
                                        v-if="emotionObject(emotion)?.emojis"
                                        :src="`/assets/emojis/${emotionObject(emotion)?.emojis}`"
                                        class="absolute w-5 h-5 group-hover:scale-150 transition-all duration-200"
                                    />
                                    <UIcon
                                        v-else
                                        name="ic:outline-emoji-emotions"
                                        class="absolute w-4 h-4 text-gray-500"
                                    />
                                </div>
                            </template>
                        </UButton>
                    </span>
                    <span
                        v-if="props.custom_prompt"
                        class="bg-teal-500 text-gray-100 text-xs font-normal px-2.5 py-0.5 rounded dark:bg-teal-300 dark:text-gray-700"
                    >
                        {{ $t('Custom prompt') }}</span
                    >
                    <UBadge
                        v-if="props.inference_type === 'openai_voice_pro'"
                        :ui="{
                            size: {
                                xs: 'text-[10px] px-1 py-0',
                            },
                        }"
                        color="primary"
                        size="xs"
                        variant="subtle"
                    >
                        <div class="flex flex-row gap-1 items-center">
                            <UIcon
                                name="fluent-color:premium-16"
                                class="text-sm animate__animated animate__swing animate__infinite animate__slow"
                            />
                            <div class="inline-flex items-center gap-1">
                                {{ $t('Text') }}
                                <div
                                    class="bg-primary-400 dark:bg-primary-600 text-white px-1 rounded-md h-4 flex items-center text-[7px]"
                                >
                                    PRO
                                </div>
                            </div>
                        </div>
                    </UBadge>
                </div>
            </div>

            <div class="flex flex-col space-y-3">
                <a
                    @click="toggleIsShowFull"
                    class="cursor-pointer p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                >
                    <div :class="isShowFull ? 'break-words whitespace-break-spaces' : 'truncate'">
                        {{ props.tts_input }}
                    </div>
                </a>
                <div v-if="isShowFull" class="relative mt-3 flex items-center justify-center">
                    <UButton
                        icon="i-uiw:up"
                        size="xs"
                        color="gray"
                        variant="solid"
                        :trailing="false"
                        @click="isShowFull = false"
                    />
                </div>
                <div class="flex justify-center text-primary-500 dark:text-primary-300">
                    <template v-if="!media_url">
                        <UAlert
                            v-if="props.status === 3"
                            :description="$t('Media is not found')"
                            :title="$t('Media load failed')"
                            color="yellow"
                            variant="subtle"
                        />
                        <UIcon
                            v-else-if="props.status === 4"
                            name="mdi:cancel-network"
                            class="text-4xl text-gray-500"
                        />
                        <UIcon v-else name="i-line-md:downloading-loop" class="text-4xl" />
                    </template>
                    <svg
                        v-else
                        class="w-5 h-5"
                        aria-hidden="true"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="1.5"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        ></path>
                    </svg>
                </div>
                <template v-if="!media_url && props.status === 3">
                    <!-- Empty template -->
                </template>
                <a
                    v-else
                    class="flex flex-row items-center gap-2 p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                >
                    <div v-if="props.status === 4">
                        {{ $t('The process has been stopped.') }}
                    </div>
                    <HistoryItemProcessing v-else-if="!media_url" :item="props" />
                    <div
                        v-else-if="loadingAudio"
                        class="flex flex-1 md:flex-row gap-2 flex-col justify-start items-start text-sm"
                    >
                        <UIcon name="i-eos-icons:loading" class="md:text-2xl text-4xl" />

                        <div>
                            {{ $t('Loading audio...') }}
                        </div>
                    </div>
                    <audio
                        v-show="media_url && !loadingAudio"
                        :key="media_url"
                        controls
                        class="flex-1 rounded-full bg-primary-300"
                        :controlsList="canAccess(AccessEnum.IS_DOWNLOAD) ? '' : 'nodownload'"
                        @loadeddata="onLoaded"
                    >
                        <source :src="media_url" type="audio/mpeg" />
                        Your browser does not support the audio element.
                    </audio>
                    <a
                        v-if="canAccess(AccessEnum.IS_DOWNLOAD) && media_url"
                        @click="downloadFile"
                        class="cursor-pointer"
                    >
                        <UIcon
                            :name="isDownloading ? 'eos-icons:loading' : 'i-material-symbols:download'"
                            class="text-2xl dark:text-gray-300"
                        />
                    </a>
                </a>
                <div
                    v-if="media_url && !loadingAudio && props.details?.blocks && props.details.blocks.length > 0"
                    class="pt-2"
                >
                    <UAccordion :items="items" variant="solid" color="gray" v-model="openDetails">
                        <template #details-block>
                            <div
                                v-if="loadings?.fetchStoryDetail[uuid]"
                                class="text-gray-900 dark:text-white text-center"
                            >
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                    <UIcon name="i-eos-icons-loading" class="text-xl" />

                                    {{ $t('Loading...') }}
                                </p>
                            </div>
                            <div v-else>
                                <HistoryItemStoryDetail :details="props.details" :uuid="props.uuid" />
                            </div>
                        </template>
                    </UAccordion>
                </div>
            </div>
            <div class="shrink-0 mt-4 flex justify-between items-center">
                <UTooltip :text="$t('Click to copy')">
                    <UButton
                        size="xs"
                        color="gray"
                        variant="soft"
                        :label="isCopied ? $t('Copied to clipboard') : props.uuid"
                        :trailing="false"
                        :ui="{}"
                        @click="onCopyUUIDToClipboard"
                        class="animate__animated"
                        :class="isCopied ? 'animate__flash' : ''"
                    >
                        <template #leading>
                            <UIcon
                                :name="isCopied ? 'i-tabler-copy-check-filled' : 'i-f7-grid'"
                                class="text-xs"
                                :class="isCopied ? 'text-green-500' : 'text-gray-500'"
                            />
                        </template>
                    </UButton>
                </UTooltip>

                <div class="shrink-0 flex justify-end">
                    <HistoryItemRate @onRated="onRated" :uuid="props.uuid" :rating="props.rating" />
                </div>
            </div>
        </div>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import HistoryItemRate from './HistoryItemRate.vue'
import { AccessEnum } from '~/types'
import { emotionObject } from '~/utils'

dayjs.extend(utc)
dayjs.extend(relativeTime)

const props = defineProps<{
    id: number
    uuid: string
    tts_input: string
    media_url: string
    custom_prompt: string
    created_at: string
    voice: string
    target_lang: string
    used_credit: number
    status: number
    model: string
    rating: string
    error_message: string
    status_percentage: number
    voice_id: string
    speaker_name: string
    details: any
    speed: number
    type: string
    model_name: string
    emotion: string
    inference_type: string
    isDownloading: boolean
    accent: string
    output_format?: string
    output_channel?: string
}>()
const emit = defineEmits(['delete', 'rated', 'download'])

const isShowFull = ref(false)
const toggleIsShowFull = (e: any) => {
    console.log('🚀 ~ toggleIsShowFull ~ e:', e.target.tagName)
    // if not click on the button, then toggle
    if (!['BUTTON', 'svg'].includes(e.target.tagName)) {
        isShowFull.value = true
    }
}
const createdAtFormat = computed(() => dayjs().to(dayjs.utc(props.created_at)))
const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))
const authStore = useAuthStore()
const { canAccess } = storeToRefs(authStore)
const { checkAccessBeforeAction } = useAuthStore()
const { t } = useI18n()

const historyStore = useHistoryStore()
const { loadings } = storeToRefs(historyStore)
const openDetails = ref(false)

const items = computed(() => {
    return [
        {
            label: t('Details for each block'),
            icon: 'i-mingcute-more-3-line',
            defaultOpen: false,
            slot: 'details-block',
        },
    ]
})

const onRated = (rated: any) => {
    emit('rated', { ...rated, uuid: props.uuid })
}

const downloadFile = () => {
    checkAccessBeforeAction(AccessEnum.IS_DOWNLOAD, () => {
        if (props.status === 2) {
            emit('download')
        }
    })
}

const loadingAudio = ref(false)
const onLoaded = () => {
    loadingAudio.value = false
}

onMounted(() => {
    loadingAudio.value = true

    // check if device is IOS
    if (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) {
        loadingAudio.value = false
    }

    if (!props.details && props.media_url) {
        historyStore.fetchStoryDetail(props.uuid)
    }
})

const isCopied = ref(false)
const onCopyUUIDToClipboard = () => {
    navigator.clipboard.writeText(props.uuid)
    isCopied.value = true
    setTimeout(() => {
        isCopied.value = false
    }, 2000)
}

const { getCountryByValue } = useVoiceLibrary(t)
const accent_name = computed(() => {
    return getCountryByValue(props.accent)?.label || ''
})
</script>

<script setup lang="ts">
const authStore = useAuthStore();
const { modals } = storeToRefs(authStore);

const canClose = ref(false);
const countDown = ref(30);
const countDownInterval = ref();
const onPlayerReady = () => {
  nextTick(() => {
    player.value.playVideo();
  });
};
const onPlayerStateChange = (event: any) => {
  // check if video is playing, start the countdown
  // if (event.data === 1) {
  //   countDownInterval.value = setInterval(() => {
  //     if (countDown.value > 0) {
  //       countDown.value--;
  //     } else {
  //       clearInterval(countDownInterval.value);
  //       canClose.value = true;
  //     }
  //   }, 1000);
  // } else {
  //   clearInterval(countDownInterval.value);
  // }
};
onMounted(() => {
  // countDownInterval.value = setInterval(() => {
  //   if (countDown.value > 0) {
  //     countDown.value--;
  //   } else {
  //     clearInterval(countDownInterval.value);
  //     canClose.value = true;
  //   }
  // }, 1000);

  if (process.client) {
    nextTick(() => {
      player.value = new window.YT.Player("youtube-iframe", {
        width: "100%",
        height: "415",
        videoId: "OdDdyu2k20A",
        playerVars: {
          autoplay: 1,
          controls: 1,
          mute: 1,
          list: "PL8DoYyQx5K5GRv_4q5zL7rCKsNEtXH8dN"
        },
        events: {
          onReady: onPlayerReady,
          onStateChange: onPlayerStateChange,
        },
      });
    });
  }
});

const player = ref();
</script>

<template>
  <div>
    <UModal
      v-model="modals.manual"
      prevent-close
      :ui="{
        width: 'sm:max-w-3xl',
      }"
    >
      <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
              {{ $t("How to use") }}
            </h3>
            <UButton
              v-if="canClose || true"
              color="gray"
              variant="ghost"
              icon="i-heroicons-x-mark-20-solid"
              @click="modals.manual = false"
            />
            <UButton v-else color="gray" variant="soft" class="px-4">
              {{
                $t("Can close in {countDown}s", {
                  countDown: countDown,
                })
              }}
            </UButton>
          </div>
        </template>

        <!-- <iframe
          id="youtube-iframe"
          width="100%"
          height="415"
          src="https://www.youtube.com/embed/OdDdyu2k20A?si=qUDZCQNPKK2XTnF7&amp"
          title="YouTube video player"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          referrerpolicy="strict-origin-when-cross-origin"
          allowfullscreen
        ></iframe> -->
        <div class="text-gray-500 dark:text-gray-400 pb-4">
          {{
            $t("Please watch the video at least 30 seconds to learn how to use the app.")
          }}
        </div>
        <div id="youtube-iframe" ref="iframeRef"></div>
      </UCard>
    </UModal>
  </div>
</template>

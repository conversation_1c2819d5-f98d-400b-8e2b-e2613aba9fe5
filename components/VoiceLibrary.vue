<template>
    <UCard class="" :ui="{ body: { padding: '!p-0' }, base: 'overflow-visible' }">
        <div class="w-full">
            <div
                class="flex flex-row items-center gap-4 justify-between sm:pl-4 sm:pr-4 z-10 relative bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800"
            >
                <VoiceLibraryTypes class="" />
                <div v-if="showCloseButton" class="hidden sm:flex flex-row items-center gap-2">
                    <UButton
                        :icon="'i-material-symbols-close'"
                        size="xs"
                        color="white"
                        variant="solid"
                        :trailing="false"
                        @click="emits('close')"
                    />
                </div>
            </div>
            <div
                class="transition-all duration-200 px-4 py-2 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900 flex flex-row items-center justify-between"
                :class="{
                    '-mt-0': showFilter,
                    '-mt-12 -z-10': !showFilter,
                }"
            >
                <div class="flex flex-row items-center gap-1 flex-wrap">
                    <UInput
                        icon="i-heroicons-magnifying-glass-20-solid"
                        size="xs"
                        color="white"
                        :trailing="false"
                        :placeholder="$t('Search...')"
                        class=""
                        v-model="filter.keyword"
                    />
                    <USelectMenu
                        size="xs"
                        v-model="filter.genders"
                        :key="filter.genders + locale"
                        :options="voiceGenders"
                        multiple
                        :placeholder="$t('Gender')"
                        :ui-menu="{
                            width: 'w-screen max-w-32',
                        }"
                    >
                        <template #label>
                            <span>
                                {{ $t('Gender') }}
                            </span>
                        </template>
                    </USelectMenu>
                    <USelectMenu
                        size="xs"
                        v-model="filter.ages"
                        :key="filter.ages + locale"
                        :options="voiceAges"
                        multiple
                        :placeholder="$t('Age')"
                        :ui-menu="{
                            width: 'w-screen max-w-28',
                        }"
                    >
                        <template #label>
                            <span>
                                {{ $t('Age') }}
                            </span>
                        </template>
                    </USelectMenu>
                    <!-- <USelectMenu
            size="xs"
            v-model="filter.languages"
            :options="voiceLanguages"
            multiple
            :placeholder="$t('Language')"
            searchable
          >
            <template #label>
              <span>
                {{ $t("Language") }}
              </span>
            </template>
          </USelectMenu> -->
                    <USelectMenu
                        size="xs"
                        v-model="filter.accents"
                        :options="voiceAccents"
                        multiple
                        :placeholder="$t('Country')"
                        :ui-menu="{
                            width: 'w-screen max-w-40',
                        }"
                    >
                        <template #label>
                            <span>
                                {{ $t('Country') }}
                            </span>
                        </template>
                    </USelectMenu>
                </div>
                <div class="flex flex-wrap flex-row gap-2">
                    <UButton
                        icon="i-lucide-filter-x"
                        size="xs"
                        color="gray"
                        variant="solid"
                        :label="$t('Reset')"
                        :trailing="false"
                        @click="voiceLibraryStore.clearFilter"
                    />
                    <UButton
                        icon="i-ci-arrow-reload-02"
                        size="xs"
                        color="white"
                        variant="solid"
                        :trailing="false"
                        @click="voiceLibraryStore.fetchVoiceLibraryByType(filter.voiceTypes, true)"
                    />
                </div>
            </div>
            <!-- <div
        class="px-4 py-1 text-xs border-b border-gray-200 dark:border-gray-800 shadow-sm bg-gray-50 dark:bg-gray-900"
      >
        <div v-if="hasFilter">
          <div class="truncate text-gray-500 font-light">{{ filterDescription }}</div>
        </div>
        <div v-else class="font-semibold">{{ $t("All") }}</div>
      </div> -->
            <div
                class="py-4 pb-36 md:pb-10 h-full overflow-auto flex flex-col space-y-4 scrollbar-thin"
                :class="{
                    'md:max-h-none': props.full,
                    'md:h-[calc(100vh-318px)]': !props.full,
                }"
            >
                <div
                    v-if="loadings['fetchVoiceLibraryByType'][filter.voiceTypes]"
                    class="grid grid-cols-1 gap-4 px-4"
                    :class="{
                        'md:grid-cols-2': !props.full,
                        'md:grid-cols-4 sm:grid-col-3': props.full,
                    }"
                >
                    <BaseVoiceCard loading v-for="i in 10" />
                </div>
                <div
                    v-else-if="filteredVoiceLibraries?.length"
                    class="dragArea list-group grid grid-cols-1 gap-4 px-4"
                    :class="{
                        'md:grid-cols-2': !props.full,
                        'md:grid-cols-4 sm:grid-col-3': props.full,
                    }"
                >
                    <template
                        v-for="(element, voiceIndex) in filteredVoiceLibraries"
                        :key="element.id + element.is_favorite"
                    >
                        <BaseVoiceCard
                            :add_new_card="
                                filter.voiceTypes === 'user_voice' && element.id === filteredVoiceLibraries[0].id
                            "
                            v-bind="{
                                type: element.type,
                                id: element.id,
                                language: element.language,
                                accent: element.accent,
                                speaker_name: element.speaker_name,
                                gender: element.gender,
                                age: element.age,
                                description: element.description,
                                avatar: element.avatar,
                                sample_audio_path: element.sample_audio_path,
                                is_favorite: element.is_favorite,
                                training_status: element.training_status,
                                created_at: element.created_at,
                                status: element.status,
                                used_credit: element.used_credit,
                                badge: element.training_type === 'openai_voice_pro' ? 'PRO' : null,
                            }"
                            :active="activeStory?.voice_id === element.id || props.activeVoiceId === element.id"
                            @select="(id) => emits('select', id)"
                            @toggle-favorite="voiceLibraryStore.toggleFavorite"
                            :updating="loadings['toggleFavorite'][element.id]"
                            :playing="isPlaying"
                            @toggle-play="togglePlay"
                        />
                        <div
                            v-if="(voiceIndex + 1) % (props.full ? 4 : 2) === 0 && showAds"
                            class="h-auto"
                            :class="{
                                'md:col-span-2': !props.full,
                                'md:col-span-2 sm:col-span-1': props.full,
                            }"
                        >
                            <AdsenseVoiceLibrary />
                        </div>
                    </template>
                </div>
                <!-- <draggable
          v-else-if="filteredVoiceLibraries?.length"
          ref="voiceLibraryDragArea"
          class="dragArea list-group grid grid-cols-1 gap-4 px-4"
          :class="{
            'md:grid-cols-2': !props.full,
            'md:grid-cols-4 sm:grid-col-3': props.full,
          }"
          :list="filteredVoiceLibraries"
          disabled
          handle=".handle"
        >
          <template #item="{ element }">
            <BaseVoiceCard
              :add_new_card="
                filter.voiceTypes === 'user_voice' &&
                element.id === filteredVoiceLibraries[0].id
              "
              :data-id="element.id"
              v-bind="element"
              :active="
                activeStory?.voice_id === element.id || props.activeVoiceId === element.id
              "
              @select="(id) => emits('select', id)"
              @toggle-favorite="voiceLibraryStore.toggleFavorite"
              :updating="loadings['toggleFavorite'][element.id]"
              :playing="isPlaying"
              @toggle-play="togglePlay"
            />
          </template>
        </draggable> -->
                <div
                    class="dragArea list-group grid grid-cols-1 gap-4 px-4"
                    :class="{
                        'md:grid-cols-2': !props.full,
                        'md:grid-cols-4 sm:grid-col-3': props.full,
                    }"
                    v-else-if="!filteredVoiceLibraries?.length && filter.voiceTypes === 'user_voice'"
                >
                    <MyVoiceNewCard />
                </div>
                <div
                    v-else-if="
                        !filteredVoiceLibraries?.length &&
                        !user &&
                        filter.voiceTypes !== 'openai_voice' &&
                        filter.voiceTypes !== 'system_voice'
                    "
                    class="h-full flex flex-col items-center justify-center text-gray-400"
                >
                    <div class="text-center">
                        <div class="text-gray-500 text-base">
                            {{ $t('Please login to access voice library') }}
                        </div>
                        <div class="mt-1">
                            <div class="flex justify-center">
                                <button
                                    @click="router.push({ path: '/signup' })"
                                    type="button"
                                    class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                                >
                                    {{ $t('Sign up for free') }}

                                    <svg
                                        aria-hidden="true"
                                        class="ml-2 h-4 w-4"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="1.5"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        ></path>
                                    </svg>
                                </button>
                                <button
                                    @click="router.push({ path: '/signin' })"
                                    type="button"
                                    class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                                >
                                    {{ $t('Sign In') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    v-else-if="!filteredVoiceLibraries?.length"
                    class="h-full flex flex-col items-center justify-center text-gray-400"
                >
                    <UIcon name="i-lucide-filter-x" class="text-6xl" />
                    <div class="text-center">
                        <div class="text-gray-500 text-lg">{{ $t('No voice found') }}</div>
                        <div class="text-gray-500 text-sm px-6">
                            {{ $t('Please try another filter') }}
                        </div>
                    </div>
                </div>
                <audio
                    :key="selectedVoiceLibrary?.id"
                    :src="selectedVoiceLibrary?.sample_audio_path"
                    ref="audio"
                    @play="onPlay"
                />
                <div class="sm:hidden fixed bottom-4 w-full text-center gap-2 flex flex-row justify-center">
                    <UButton
                        @click="emits('close')"
                        :icon="'i-material-symbols-close'"
                        size="xl"
                        color="white"
                        variant="solid"
                        :label="$t('Cancel')"
                        class="w-32 justify-center"
                        :trailing="false"
                    />
                </div>
            </div>
        </div>
    </UCard>
</template>

<script setup lang="ts">
import { debounce } from 'lodash'
import { storeToRefs } from 'pinia'
const storyStore = useStoryStore()
const voiceLibraryStore = useVoiceLibraryStore()
import draggable from 'vuedraggable'
const { activeStory, voiceLibraryDragArea } = storeToRefs(storyStore)
const { filteredVoiceLibraries, filter, hasFilter, filterDescription, loadings, selectedVoiceLibrary, playingId } =
    storeToRefs(voiceLibraryStore)
const showFilter = ref(true)
const { t, locale } = useI18n()
const props = defineProps({
    showCloseButton: {
        type: Boolean,
        default: false,
    },
    full: {
        type: Boolean,
        default: false,
    },
    activeVoiceId: {
        type: String,
        default: '',
    },
})
const emits = defineEmits(['close', 'select'])

const { voiceTypes, voiceLanguages, voiceAccents } = useVoiceLibrary(t)

const voiceGenders = computed(() => {
    return useVoiceLibrary(t).voiceGenders
})

const voiceAges = computed(() => {
    return useVoiceLibrary(t).voiceAges
})

watch(
    () => activeStory.value,
    (active) => {
        if (active) {
            // nextTick(() => {
            //   const voiceElm = voiceLibraryDragArea.value.$el.querySelector(
            //     `[data-id="${active.voice_id}"]`
            //   );
            //   console.log("🚀 ~ nextTick ~ active.voice_id:", active.voice_id);
            //   console.log("🚀 ~ nextTick ~ voiceElm:", voiceElm);
            //   if (voiceElm) {
            //     voiceElm.scrollIntoView({
            //       behavior: "smooth",
            //       block: "center",
            //       inline: "center",
            //     });
            //   }
            // });
        }
    }
)

onMounted(() => {
    // voiceLibraryStore.fetchVoiceLibrary();
    voiceTypes.forEach((type) => {
        voiceLibraryStore.fetchVoiceLibraryByType(type.value as string)
    })
    voiceLibraryStore.filterVoiceLibraries()
})

const authStore = useAuthStore()
const { user, showAds } = storeToRefs(authStore)
const router = useRouter()

watch(
    () => filter,
    () => {
        // debounce
        debounce(() => {
            voiceLibraryStore.filterVoiceLibraries()
        }, 300)()
    },
    { deep: true }
)
const isPlaying = ref(false)
const audio = ref<HTMLAudioElement>(new Audio())
const onPlay = () => {
    isPlaying.value = true
}

// const onPause = () => {
//   isPlaying.value = false;
//   console.log("🚀 ~ onPause ~ isPlaying.value", isPlaying.value);
// };

const togglePlay = (voice: any) => {
    if (voice?.id !== selectedVoiceLibrary.value?.id) {
        selectedVoiceLibrary.value = voice
        // isPlaying.value = true;
        nextTick(() => {
            audio.value.play()
        })
    } else {
        if (isPlaying.value) {
            isPlaying.value = false
            audio.value.pause()
        } else {
            isPlaying.value = true
            audio.value.play()
        }
    }
}

watch(
    () => filter.value?.voiceTypes,
    (newValue, oldValue) => {
        if (newValue !== oldValue) {
            isPlaying.value = false
            audio.value.pause()
        }
    }
)
</script>

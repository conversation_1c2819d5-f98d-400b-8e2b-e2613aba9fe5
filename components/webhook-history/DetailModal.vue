<template>
    <UModal
        v-model="isShowDetailModal"
        :ui="{
            width: 'sm:max-w-3xl',
            height: 'h-full',
        }"
    >
        <UCard
            :ui="{
                divide: 'divide-y divide-gray-100 dark:divide-gray-800',
                base: 'w-full',
            }"
        >
            <template #header>
                <div class="flex items-center justify-between px-2">
                    <div class="flex items-center gap-2">
                        <UIcon name="i-heroicons-code-bracket" class="w-5 h-5 text-primary-500" />
                        <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                            {{ $t('Webhook Event Data') }}
                        </h3>
                        <UBadge
                            v-if="item.http_response"
                            size="xs"
                            :label="item.http_response"
                            :color="item.http_response === '200' ? 'emerald' : 'red'"
                            variant="subtle"
                        />
                    </div>
                    <div class="flex items-center gap-2">
                        <UButton
                            color="primary"
                            variant="soft"
                            size="sm"
                            :ui="{
                                padding: 'px-3 py-1',
                                rounded: 'rounded-lg',
                                ring: 'ring-2 ring-primary-500/20 ring-offset-1',
                            }"
                            @click="copyToClipboard"
                        >
                            <template #leading>
                                <UIcon name="i-heroicons-clipboard" class="w-4 h-4" />
                            </template>
                            {{ $t('Copy JSON') }}
                        </UButton>
                        <UButton
                            color="gray"
                            variant="ghost"
                            icon="i-heroicons-x-mark-20-solid"
                            class="-my-1"
                            @click="setIsShowDetailModal(false)"
                        />
                    </div>
                </div>
            </template>

            <div class="relative">
                <div class="absolute right-4 top-4 flex gap-2">
                    <UButton
                        color="primary"
                        variant="soft"
                        size="xs"
                        :ui="{ padding: 'px-2 py-1' }"
                        @click="toggleWrap"
                    >
                        <template #leading>
                            <UIcon
                                :name="isWrap ? 'i-heroicons-arrow-path' : 'i-heroicons-arrows-right-left'"
                                class="w-4 h-4"
                            />
                        </template>
                        {{ isWrap ? $t('Wrap') : $t('No Wrap') }}
                    </UButton>
                    <UButton
                        color="primary"
                        variant="soft"
                        size="xs"
                        :ui="{ padding: 'px-2 py-1' }"
                        @click="toggleLineNumbers"
                    >
                        <template #leading>
                            <UIcon
                                :name="showLineNumbers ? 'i-heroicons-list-bullet' : 'i-heroicons-bars-3'"
                                class="w-4 h-4"
                            />
                        </template>
                        {{ showLineNumbers ? $t('Hide Lines') : $t('Show Lines') }}
                    </UButton>
                </div>

                <div class="flex">
                    <!-- Line Numbers -->
                    <div
                        v-if="showLineNumbers"
                        class="pt-4 pr-4 text-right text-gray-400 select-none bg-gray-50 dark:bg-gray-800/50 border-r border-gray-200 dark:border-gray-700"
                    >
                        <div v-for="i in totalLines" :key="i" class="leading-6 text-xs font-mono">
                            {{ i }}
                        </div>
                    </div>

                    <!-- JSON Content -->
                    <pre
                        class="flex-1 p-4 text-sm rounded-lg bg-gray-50 dark:bg-gray-800/50 overflow-x-auto custom-scrollbar"
                        :class="{ 'whitespace-pre-wrap break-all': isWrap }"
                    ><code class="language-json leading-6" v-html="highlightedJson"></code></pre>
                </div>
            </div>
        </UCard>
    </UModal>
</template>

<script setup lang="ts">
import { useWebhookHistoryStore } from '@/stores/webhook-history'
import { onMounted, computed, ref } from 'vue'

const { t } = useI18n()
const webhookHistoryStore = useWebhookHistoryStore()
const { isShowDetailModal } = storeToRefs(webhookHistoryStore)
const { setIsShowDetailModal } = webhookHistoryStore

const isWrap = ref(false)
const showLineNumbers = ref(true)

const props = defineProps<{
    jsonData: Record<string, any>
    item: any
}>()

const eventPayload = computed(() => {
    return {
        event: props.item.event_name,
        uuid: props.item.uuid,
        data: props.jsonData,
    }
})

const toggleWrap = () => {
    isWrap.value = !isWrap.value
}

const toggleLineNumbers = () => {
    showLineNumbers.value = !showLineNumbers.value
}

const totalLines = computed(() => {
    return JSON.stringify(eventPayload.value, null, 2).split('\n').length
})

// Format and syntax highlight JSON with enhanced colors
const highlightedJson = computed(() => {
    const jsonString = JSON.stringify(eventPayload.value, null, 2)
    return jsonString
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(
            /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
            (match) => {
                let cls = 'text-blue-600 dark:text-blue-400 font-medium'
                if (/^"/.test(match)) {
                    if (/:$/.test(match)) {
                        cls = 'text-violet-600 dark:text-violet-400 font-semibold' // key
                    } else {
                        cls = 'text-emerald-600 dark:text-emerald-400' // string
                    }
                } else if (/true|false/.test(match)) {
                    cls = 'text-amber-600 dark:text-amber-400 font-medium' // boolean
                } else if (/null/.test(match)) {
                    cls = 'text-red-500 dark:text-red-400 font-medium' // null
                } else {
                    cls = 'text-blue-600 dark:text-blue-400 font-medium' // number
                }
                return `<span class="${cls}">${match}</span>`
            }
        )
        .replace(/\n/g, '<br>')
        .replace(/\s{2}/g, '&nbsp;&nbsp;')
})

const copyToClipboard = async () => {
    try {
        await navigator.clipboard.writeText(JSON.stringify(eventPayload.value, null, 2))
        const toast = useToast()
        toast.add({
            title: t('Success'),
            description: t('JSON copied to clipboard.'),
            color: 'green',
            icon: 'i-heroicons-check-circle-20-solid',
            timeout: 2000,
        })
    } catch (err) {
        console.error('Failed to copy:', err)
    }
}
</script>

<style scoped></style>

<template>
    <UModal
        v-model="showNoAdsModal"
        :ui="{
            width: '!max-w-md',
        }"
    >
        <RemoveAdsCard :month="1" @buy="buyNoAds" showClose @close="showNoAdsModal = false"/>
    </UModal>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const { showNoAdsModal } = storeToRefs(authStore)

const paymentsStore = usePaymentsStore()
const { paymentInfo, isOpen, noAdsProduct } = storeToRefs(paymentsStore)

const buyNoAds = (item: any) => {
    paymentInfo.value = {
        price: noAdsProduct.value.price_divide_100 * item.month,
        quantity: item.month,
        id: noAdsProduct.value?.id,
        type: 'no-ads',
    }
    isOpen.value = true
}
</script>

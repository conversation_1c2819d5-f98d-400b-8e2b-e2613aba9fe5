<template>
  <select
    :disabled="isUploading || showCustomPrompt"
    v-model="translateOptions.translateTone"
    class="text-xs font-medium border border-gray-300 dark:border-gray-600 bg-gray-300 dark:bg-gray-600 rounded-lg block w-full p-2.5 focus:ring-primary-500 dark:focus:ring-primary-500 dark:focus:border-primary-500"
    :class="
      isUploading || showCustomPrompt
        ? 'bg-gray-300 dark:bg-gray-600 text-gray-400 dark:text-gray-400 cursor-not-allowed'
        : 'text-gray-900 dark:text-white  hover:bg-gray-400 dark:hover:bg-gray-700'
    "
  >
    <option selected value="">{{ $t("Select voice") }}</option>
    <option v-for="{ value, displayText } in options" :key="value" :value="value">
      {{ $t(displayText) }}
    </option>
  </select>
</template>

<script lang="ts" setup>
import _ from "lodash";
const translateStore = useTranslateStore();
const { translateOptions, isUploading, showCustomPrompt } = storeToRefs(translateStore);

const { t, locale } = useI18n({ useScope: "global" });

const options = _.orderBy(
  [
    { value: "alloy", text: "Alloy" },
    { value: "echo", text: "Echo" },
    { value: "fable", text: "Fable" },
    { value: "onyx", text: "Onyx" },
    { value: "nova", text: "Nova" },
    { value: "shimmer", text: "Shimmer" },
  ].map((item) => {
    return {
      ...item,
      displayText: t(item.text),
    };
  }),
  "displayText"
);
</script>

<template>
  <UButton
    block
    :ui="{
      rounded: 'rounded-md',
    }"
    size="xl"
    color="purple"
    icon="token-branded:usdt"
    :loading="loading['createOrderCryptomus']"
    @click="openWarning = true"
    class="group"
  >
    {{ $t("Pay with Crypto") }}
    <div>
      <UBadge
        v-if="specialBonusPercent > 0"
        :ui="{ rounded: 'rounded-full' }"
        color="green"
      >
        <div class="group-hover:hidden">
          {{
            $t("+{per}% credit bonus", {
              per: specialBonusPercent > 0 ? specialBonusPercent : 0,
            })
          }}
        </div>
        <div class="hidden group-hover:block">
          <div class="w-full flex flex-inline items-center gap-1">
            <div class="font-semibold">${{ totalPrice }}</div>
            <UIcon name="i-ep-right" />
            <div>
              {{
                new Intl.NumberFormat().format(
                  totalCredits + specialBonusCredits * quantity
                )
              }}
              <small class="text-xs">{{ $t("credits") }}</small>
            </div>
          </div>
        </div>
      </UBadge>
    </div>
  </UButton>
  <UDashboardModal
    v-model="openWarning"
    :title="$t('Crypto Payment Confirmation')"
    :description="$t('Please note that the crypto payment is non-refundable.')"
    icon="i-heroicons-exclamation-circle"
    :ui="{
      icon: { base: 'text-purple-500 dark:text-purple-400' } as any,
      footer: { base: 'ml-16' } as any
    }"
  >
    <template #footer>
      <UButton color="purple" :label="$t(`I understand. Let's continue`)" @click="onPay" />
      <UButton color="white" :label="$t('Cancel')" @click="openWarning = false" />
    </template>
  </UDashboardModal>
</template>

<script setup lang="ts">
const props = defineProps<{
  productId: string;
  quantity: number;
  specialBonusPercent: number;
  totalPrice: number;
  totalCredits: number;
  specialBonusCredits: number;
}>();

const paymentsStore = usePaymentsStore();
const { loading, isOpen } = storeToRefs(paymentsStore);
const openWarning = ref(false);
const onPay = async () => {
  const res: any = await paymentsStore.createOrderCryptomus(
    props.productId,
    props.quantity
  );
  if (res && res?.approval_url && res?.success) {
    // open new tab
    // window.open(res.approval_url, "_blank");

    // open new small window centered
    // const _window = window.open(res.approval_url, "Payment", "width=420,height=600,scrollbars=yes");
    // listen to close event
    popupCenter({ url: res.approval_url, title: "Payment", w: 900, h: 900 });
    isOpen.value = false;
  }
};

const popupCenter = ({ url, title, w, h }) => {
  // Fixes dual-screen position                             Most browsers      Firefox
  const dualScreenLeft =
    window.screenLeft !== undefined ? window.screenLeft : window.screenX;
  const dualScreenTop =
    window.screenTop !== undefined ? window.screenTop : window.screenY;

  const width = window.innerWidth
    ? window.innerWidth
    : document.documentElement.clientWidth
    ? document.documentElement.clientWidth
    : screen.width;
  const height = window.innerHeight
    ? window.innerHeight
    : document.documentElement.clientHeight
    ? document.documentElement.clientHeight
    : screen.height;

  const systemZoom = width / window.screen.availWidth;
  const left = (width - w) / 2 / systemZoom + dualScreenLeft;
  const top = (height - h) / 2 / systemZoom + dualScreenTop;
  const newWindow = window.open(
    url,
    title,
    `
      scrollbars=yes,
      width=${w / systemZoom}, 
      height=${h / systemZoom}, 
      top=${top}, 
      left=${left}
      `
  );

  if (window?.focus) newWindow?.focus();
  const interval = setInterval(() => {
    if (window?.closed) {
      const authStore = useAuthStore();
      clearInterval(interval);
      isOpen.value = false;
      // sync token after 3 seconds
      setTimeout(() => {
        authStore.syncUserTokenInfo();
      }, 3000);
    }
  }, 500);
};
</script>

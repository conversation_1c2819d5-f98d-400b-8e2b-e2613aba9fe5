<template>
    <UHorizontalNavigation
        :ui="{
            base: 'flex-1 sm:flex-none !text-xs text-center mx-1.5 flex flex-row justify-center items-center py-2.5',
            inner: 'w-full sm:w-fit flex items-center justify-between sm:justify-around',
            container: 'flex justify-start sm:justify-between w-full sm:min-w-0',
            wrapper: 'w-full',
            active: 'after:bg-transparent before:bg-white dark:before:bg-gray-900',
        }"
        :links="voiceTypes"
        class="bg-gray-100 dark:bg-gray-800 my-1 rounded-xl"
    >
        <template #default="{ link, isActive }">
            <span
                class="relative w-full text-xs truncate hidden sm:block hover:text-primary-500"
                :class="{
                    'text-primary-500': isActive,
                }"
                >{{ link.label }}</span
            >
        </template>

        <template #icon="{ link, isActive }">
            <div class="w-full sm:w-fit flex flex-col truncate items-center justify-center">
                <div class="flex items-start gap-1">
                    <UIcon
                        :name="link.icon"
                        class="w-5 h-5 sm:w-5 sm:h-5"
                        :class="{
                            'text-primary-500': isActive,
                        }"
                    />
                </div>
                <span class="relative truncate text-[8px] sm:hidden hover:text-primary-500">{{ link.label }}</span>
            </div>
        </template>
    </UHorizontalNavigation>
</template>

<script setup lang="ts">
import { Switch } from '@headlessui/vue'
const voiceLibraryStore = useVoiceLibraryStore()
const { filter } = storeToRefs(voiceLibraryStore)
const { t } = useI18n()

// const { voiceTypes } = useVoiceLibrary(t);
const voiceTypes = computed(() => {
    return useVoiceLibrary(t).voiceTypes.map((item) => {
        return {
            ...item,
            active: filter.value.voiceTypes === item.value,
            click: () => {
                filter.value.voiceTypes = item.value
                nextTick(() => {
                    voiceLibraryStore.filterVoiceLibraries()
                })
            },
        }
    })
})
function onChange(index: number) {
    console.log('🚀 ~ onChange ~ index:', index)
    const item = voiceTypes.value[index]
    filter.value.voiceTypes = item.value
    // voiceLibraryStore.fetchVoiceLibrary();
}

const defaultTab = computed(() => {
    return voiceTypes.value.findIndex((item) => item.value === filter.value.voiceTypes)
})
</script>

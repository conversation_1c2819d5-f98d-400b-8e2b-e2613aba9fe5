<template>
  <div class="flex flex-col h-full max-h-full pb-0.5 space-y-6">
    <VoiceSettings class="hidden md:flex flex-col flex-1" />
    <UModal v-model="isOpen" fullscreen class="">
      <div class="overflow-auto">
        <VoiceSettings showCloseButton @close="isOpen = false" />
      </div>
    </UModal>
    <div
      class="md:relative z-30 md:z-10 md:px-0 md:bottom-0 md:py-0 md:bg-transparent flex flex-row justify-between md:space-x-1 space-x-0 fixed bottom-1 w-full left-0 px-4 py-4"
    >
      <div
        v-if="audioResult?.link"
        @click="showAudioPlayer = true"
        class="flex px-4 md:hidden items-center justify-center p-3 dark:bg-gray-800 rounded-l-full border border-gray-300 border-r-0 dark:border-gray-700 hover:bottom-0 cursor-pointer transition-all duration-200"
      >
        <div
          class="relative flex flex-row items-center justify-center ring-1 ring-gray-200 dark:ring-gray-800 h-6 w-6 bg-white/75 backdrop-blur dark:bg-gray-900/80 rounded-full"
        >
          <span
            v-if="isPlaying"
            class="animate-ping absolute inline-flex h-10 w-10 rounded-full bg-primary-200 dark:bg-primary-900 opacity-20"
          ></span>
          <UAvatar
            :src="`/assets/images/avatars/${audioResult?.voice}.svg`"
            size="md"
            :class="{
              innertape2: isPlaying,
            }"
          />
        </div>
      </div>
      <USelect
        icon="i-line-md-speed-loop"
        color="white"
        size="xl"
        :options="speedOptionsFormat"
        :placeholder="$t('Select speed')"
        v-model="speed"
        class="w-fit inline-flex flex-1 md:flex-none"
        :ui="{
          rounded: 'rounded-none md:rounded-l-full',
          size: {
            xl: 'text-xs md:text-base',
          },
        }"
      />

      <div
        class="border-8 hover:shadow-2xl shadow-md rounded-full border-gray-50 dark:border-gray-950 md:border-none md:flex-1 absolute top-1/2 left-1/2 transform md:transform-none md:top-0 md:left-0 -translate-x-1/2 -translate-y-1/2 md:relative"
      >
        <UButton
          @click="onCreateSpeech"
          icon="i-ri-chat-voice-fill"
          size="xl"
          color="primary"
          variant="solid"
          :trailing="true"
          class=""
          :ui="{
            inline: 'md:justify-between justify-center',
            rounded: 'md:rounded-l-none',
            icon: {
              size: {
                xl: 'w-14 h-14 md:w-6 md:h-6',
              },
            },
            base: 'w-20 h-20 md:w-full md:h-full',
          }"
          :loading="loadings.createSpeech"
          :disabled="!canCreateSpeech"
        >
          <div class="hidden md:block truncate">
            <span class="hidden md:block">{{ $t("Create Speech") }}</span>
          </div>
        </UButton>
      </div>
      <UButton
        @click="isOpen = true"
        icon="i-icon-park-solid-voice-one"
        size="xl"
        color="white"
        square
        variant="solid"
        class="pl-4 pr-3 md:hidden inline-flex flex-1 justify-end items-center"
        :ui="{
          rounded: 'rounded-l-none',
        }"
        :trailing="true"
      >
        <div>
          <span class="block md:hidden text-xs sm:text-base">{{
            $t(voiceSelectedObj?.text || "")
          }}</span>
          <div class="text-xs dark:text-gray-400">
            {{ $t(modelSelectedObj?.label || "") }}
          </div>
        </div>
        <template #trailing>
          <UAvatar
            :src="`/assets/images/avatars/${voiceSelectedObj?.value}.svg`"
            :alt="voiceSelectedObj?.text"
          />
        </template>
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
const translateStore = useTranslateStore();
const {
  speedOptions,
  speed,
  loadings,
  voiceSelectedObj,
  modelSelectedObj,
  canCreateSpeech,
  audioResult,
  showAudioPlayer,
  isPlaying,
} = storeToRefs(translateStore);
const authStore = useAuthStore();
const { user } = storeToRefs(authStore);
const isOpen = ref(false);
const toast = useToast();
const { t } = useI18n();

const speedOptionsFormat = computed(() => {
  return speedOptions.value.map((item) => {
    return {
      value: item.value,
      label: t(item.label),
    };
  });
});

const onCreateSpeech = async () => {
  const { t } = useI18n();
  let result: any = null;
  if (user.value) {
    result = await translateStore.createSpeech(t);
  } else {
    const grecaptcha = window?.grecaptcha;
    if (grecaptcha) {
      const runtimeConfig = useRuntimeConfig();
      const rechatchaKey = runtimeConfig.public.NUXT_RECAPCHA_V3_SITE_KEY;
      const token = await grecaptcha.execute(rechatchaKey, { action: "submit" });
      console.log("🚀 ~ onCreateSpeech ~ token:", token)
      result = await translateStore.createSpeech(t, token);
    }
  }
  if (result?.uuid) {
    const router = useRouter();
    if (user.value) {
      toast.add({
        id: "create-speech",
        color: "primary",
        title: t("Success"),
        description: t(
          "Vocalization created successfully, you can check the result in history."
        ),
        actions: [
          {
            label: t("Go to history"),
            variant: "solid",
            color: "primary",
            click: () => {
              router.push({ name: "history", query: { id: result.uuid } });
              toast.remove("create-speech");
            },
          },
        ],
        timeout: 30000,
        icon: "i-ooui:success",
      });
    } else {
      toast.add({
        id: "create-speech",
        color: "primary",
        title: t("Success"),
        description: t(
          "Vocalization created successfully, you can check the result by this page."
        ),
        timeout: 30000,
        icon: "i-ooui:success",
      });
      router.push({ name: "public-history-uuid", params: { uuid: result.uuid } });
    }
  }
};
</script>

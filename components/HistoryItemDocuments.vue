<template>
    <li class="mb-10 md:ml-8 ml-3">
        <span
            class="absolute flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full -left-4 ring-8 ring-gray-50 dark:ring-gray-900 dark:bg-orange-600"
        >
            <svg
                class="w-5 h-5 text-primary-800 dark:text-primary-300"
                aria-hidden="true"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                ></path>
            </svg>
        </span>
        <div
            class="px-4 pt-2 pb-4 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-700 dark:border-gray-600"
        >
            <div class="items-center justify-between mb-4 sm:flex">
                <time
                    class="group cursor-pointer flex flex-inline space-x-2 items-center text-xs font-normal text-gray-400 sm:order-last sm:mb-0 justify-between sm:justify-start"
                >
                    <div class="group-hover:hidden">{{ createdAtFormat }}</div>
                    <div class="hidden group-hover:block">{{ createdAtRawFormat }}</div>
                    <HistoryItemMenu :item="props" />
                </time>
                <div
                    class="flex md:flex-row flex-wrap gap-1 items-center text-sm font-normal text-gray-500 dark:text-gray-300"
                >
                    <!-- <a
            class="flex flex-row items-center font-base text-gray-900 dark:text-white mr-2"
          >
            <UAvatar
              :src="`/assets/images/avatars/${voice}.svg`"
              :alt="voice"
              size="xs"
            />
          </a> -->
                    <span
                        v-if="voice_id"
                        class="bg-red-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-primary-600 dark:text-gray-300"
                    >
                        {{ voice_id }}</span
                    >
                    <span
                        class="bg-primary-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-primary-600 dark:text-gray-300 capitalize"
                    >
                        {{ speaker_name }}</span
                    >
                    <span
                        class="bg-yellow-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-600 dark:text-gray-300"
                    >
                        {{ $t('credits', { value: props.used_credit }) }}</span
                    >
                    <span
                        class="bg-blue-100 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-blue-600 dark:text-gray-300"
                    >
                        {{ $t(model_name || 'tts-1') }}</span
                    >
                    <span
                        class="bg-violet-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-violet-600 dark:text-gray-300"
                    >
                        <UIcon name="i-fluent-fast-forward-28-filled" class="text-xs" />
                        {{ speed }}x
                    </span>
                    <span
                        v-if="output_format"
                        class="bg-green-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-green-600 dark:text-gray-300"
                    >
                        <UIcon name="ic:round-audio-file" class="text-xs" />
                        {{ output_format?.toUpperCase() }}
                    </span>
                    <span
                        v-if="output_channel"
                        class="bg-orange-100 inline-flex items-center gap-1 text-gray-800 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-orange-600 dark:text-gray-300"
                    >
                        <UIcon :name="output_channel === 'mono' ? 'i-material-symbols-speaker-outline' : 'i-material-symbols-speaker-group-outline'" class="text-xs" />
                        {{ $t(output_channel === 'mono' ? 'Mono' : 'Stereo') }}
                    </span>
                    <span
                        v-if="props.custom_prompt"
                        class="bg-teal-500 text-gray-100 text-xs font-normal mr-2 px-2.5 py-0.5 rounded dark:bg-teal-300 dark:text-gray-700"
                    >
                        {{ $t('Custom prompt') }}</span
                    >
                </div>
            </div>
            <div class="flex flex-wrap md:flex-inline justify-between items-center gap-3 flex-row">
                <a
                    class="flex flex-1 cursor-pointer px-3 py-1.5 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                >
                    <div>
                        <div
                            class="flex truncate w-full md:flex-1 mb-4 md:mb-0 flex-inline items-center pl-3 text-sm font-normal text-gray-500 dark:text-gray-300"
                        >
                            <svg
                                class="w-4 min-w-[22px] h-4 mr-2"
                                aria-hidden="true"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                            <div class="whitespace-break-spaces break-all">
                                {{ props.tts_input }}

                                <span v-if="props.status === 1">
                                    ({{ `${$t('Processing...')} ${props.status_percentage || 0}%` }})
                                </span>
                            </div>
                        </div>
                    </div>
                </a>
                <div class="w-full md:w-auto text-right" v-if="props.status === 3">
                    <div
                        @click="showError = !showError"
                        class="cursor-pointer inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-900 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:outline-none focus:ring-gray-200 focus:text-blue-700 dark:bg-gray-800 dark:text-primary-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-700"
                    >
                        <svg
                            class="flex-shrink-0 inline w-4 h-4 mr-3 fill-red-600 dark:fill-red-500"
                            aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                        >
                            <path
                                d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm0 16a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3Zm1-5.034V12a1 1 0 0 1-2 0v-1.418a1 1 0 0 1 1.038-.999 1.436 1.436 0 0 0 1.488-1.441 1.501 1.501 0 1 0-3-.116.986.986 0 0 1-1.037.961 1 1 0 0 1-.96-1.037A3.5 3.5 0 1 1 11 11.466Z"
                            />
                        </svg>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ $t('Error') }}</span>
                    </div>
                </div>
            </div>

            <div
                v-if="props.status === 3 && showError"
                class="mt-4 p-3 text-sm font-normal text-red-500 border border-red-200 rounded-lg bg-red-50 dark:bg-gray-800 dark:border-gray-500 dark:text-red-500"
            >
                <div class="font-semibold flex flex-row items-center justify-between">
                    <div>
                        {{ $t('Error') }}:
                        <span class="font-normal">{{ $t(props.error_message || 'System error!') }}</span>
                    </div>
                    <div v-if="props.error_message === 'NotEnoughToken'">
                        <button
                            @click="router.push({ path: '/profile/buy-credits' })"
                            type="button"
                            class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                        >
                            {{ $t('Buy credits') }}

                            <svg
                                aria-hidden="true"
                                class="ml-2 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="1.5"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                ></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="my-3 flex justify-center text-primary-500 dark:text-primary-300">
                <template v-if="!media_url">
                    <UAlert
                        v-if="props.status === 3"
                        :description="$t('Media is not found')"
                        :title="$t('Media load failed')"
                        color="yellow"
                        variant="subtle"
                    />
                    <UIcon v-else-if="props.status === 4" name="mdi:cancel-network" class="text-4xl text-gray-500" />
                    <UIcon v-else name="i-line-md:downloading-loop" class="text-4xl" />
                </template>
                <svg
                    v-else
                    class="w-5 h-5"
                    aria-hidden="true"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M19.5 13.5L12 21m0 0l-7.5-7.5M12 21V3"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
            </div>
            <template v-if="!media_url && props.status === 3">
                <!-- Empty template -->
            </template>
            <a
                v-else
                class="flex flex-row items-center gap-2 p-3 text-sm font-normal text-gray-500 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-300"
                :class="isShowFull ? 'break-words' : 'truncate'"
            >
                <div v-if="props.status === 4">
                    {{ $t('The process has been stopped.') }}
                </div>
                <HistoryItemProcessing v-else-if="!media_url" :item="props" />
                <div
                    v-else-if="loadingAudio"
                    class="flex flex-1 md:flex-row gap-2 flex-col justify-start items-start text-sm"
                >
                    <UIcon name="i-eos-icons:loading" class="md:text-2xl text-4xl" />

                    <div>
                        {{ $t('Loading audio...') }}
                    </div>
                </div>
                <audio
                    v-show="media_url && !loadingAudio"
                    :key="media_url"
                    controls
                    class="flex-1 rounded-full bg-primary-300"
                    :controlsList="canAccess(AccessEnum.IS_DOWNLOAD) ? '' : 'nodownload'"
                    @loadeddata="onLoaded"
                >
                    <source :src="media_url" type="audio/mpeg" />
                    Your browser does not support the audio element.
                </audio>
                <a v-if="canAccess(AccessEnum.IS_DOWNLOAD) && media_url" @click="downloadFile" class="cursor-pointer">
                    <UIcon
                        :name="isDownloading ? 'eos-icons:loading' : 'i-material-symbols:download'"
                        class="text-2xl dark:text-gray-300"
                    />
                </a>
            </a>
            <div
                v-if="media_url && !loadingAudio && props.details?.blocks && props.details.blocks.length > 0"
                class="pt-2"
            >
                <UAccordion :items="items" variant="solid" color="gray" v-model="openDetails">
                    <template #details-block>
                        <div v-if="loadings?.fetchStoryDetail[uuid]" class="text-gray-900 dark:text-white text-center">
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                <UIcon name="i-eos-icons-loading" class="text-xl" />

                                {{ $t('Loading...') }}
                            </p>
                        </div>
                        <div v-else>
                            <HistoryItemStoryDetail :details="props.details" :uuid="props.uuid" />
                        </div>
                    </template>
                </UAccordion>
            </div>
            <div class="shrink-0 mt-4 flex justify-between items-center">
                <UTooltip :text="$t('Click to copy')">
                    <UButton
                        size="xs"
                        color="gray"
                        variant="soft"
                        :label="isCopied ? $t('Copied to clipboard') : props.uuid"
                        :trailing="false"
                        :ui="{}"
                        @click="onCopyUUIDToClipboard"
                        class="animate__animated"
                        :class="isCopied ? 'animate__flash' : ''"
                    >
                        <template #leading>
                            <UIcon
                                :name="isCopied ? 'i-tabler-copy-check-filled' : 'i-f7-grid'"
                                class="text-xs"
                                :class="isCopied ? 'text-green-500' : 'text-gray-500'"
                            />
                        </template>
                    </UButton>
                </UTooltip>

                <div class="shrink-0 flex flex-row items-center gap-2 justify-end py-2">
                    <div class="flex-wrap flex">
                        <HistoryItemBugReport v-if="props.status === 3" :uuid="props.uuid" />
                        <HistoryItemRate v-else :key="props.uuid" :uuid="props.uuid" :rating="props.rating" />
                    </div>
                </div>
            </div>
        </div>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import HistoryItemRate from './HistoryItemRate.vue'
import HistoryItemBugReport from './HistoryItemBugReport.vue'
import { AccessEnum } from '~/types'
dayjs.extend(relativeTime)
dayjs.extend(utc)

const { t } = useI18n()

const props = defineProps<{
    uuid: string
    tts_input: string
    trans_result: string
    custom_prompt: string
    created_at: string
    voice: string
    target_lang: string
    used_credit: number
    status: number
    model: string
    rating: string
    error_message: string
    status_percentage: number
    media_url: string
    voice_id: string
    speaker_name: string
    details?: any
    speed: number
    type: string
    id: number
    input_file_path: string
    file_password: string
    file_size: number
    model_name: string
    isDownloading: boolean
    output_format?: string
    output_channel?: string
}>()
const emit = defineEmits(['delete', 'download'])

const isShowFull = ref(false)
const showError = ref(true)
const toggleIsShowFull = () => {
    isShowFull.value = !isShowFull.value
}
const authStore = useAuthStore()
const { canAccess } = storeToRefs(authStore)
const { checkAccessBeforeAction } = useAuthStore()

const downloadFile = () => {
    checkAccessBeforeAction(AccessEnum.IS_DOWNLOAD, () => {
        if (props.status === 2) {
            emit('download')
        }
    })
}

const historyStore = useHistoryStore()
const { loadings } = storeToRefs(historyStore)

const createdAtFormat = computed(() => dayjs().to(dayjs.utc(props.created_at)))
const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))

const router = useRouter()

const loadingAudio = ref(false)
const onLoaded = () => {
    loadingAudio.value = false
}

const openDetails = ref()
const items = computed(() => {
    return [
        {
            label: t('Details for each block'),
            icon: 'i-mingcute-more-3-line',
            defaultOpen: false,
            slot: 'details-block',
        },
    ]
})

onMounted(() => {
    loadingAudio.value = true

    // check if device is IOS
    if (navigator.platform && /iPad|iPhone|iPod/.test(navigator.platform)) {
        loadingAudio.value = false
    }

    if (!props.details && props.media_url) {
        historyStore.fetchStoryDetail(props.uuid)
    }
})

const isCopied = ref(false)
const onCopyUUIDToClipboard = () => {
    navigator.clipboard.writeText(props.uuid)
    isCopied.value = true
    setTimeout(() => {
        isCopied.value = false
    }, 2000)
}
</script>

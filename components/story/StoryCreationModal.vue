<script setup lang="ts">
const storyStore = useStoryStore();
const {
  isOpenCreateStoryModal,
  storyOptions,
  totalDuration,
  totalTextLength,
  totalSilenceTime,
  totalListeningTime,
  blocks,
  voicesSummary,
  errors,
  loadings,
} = storeToRefs(storyStore);

const { clearErrors } = storyStore;

const authStore = useAuthStore();
const config = useRuntimeConfig();
const { isSuperUser } = storeToRefs(authStore);

import { isNaN } from "lodash";
const toast = useToast();

const onSubmit = async () => {
  if (totalTextLength.value === 0) return;
  const result: any = await storyStore.createStoryBlocks();
  if (result?.history_uuid) {
    const router = useRouter();
    toast.add({
      id: "create-speech",
      color: "primary",
      title: t("Success"),
      description: t(
        "Your story has been created successfully, you can check the result in history."
      ),
      actions: [
        {
          label: t("Go to history"),
          variant: "solid",
          color: "primary",
          click: () => {
            router.push({ name: "history", query: { id: result.history_uuid } });
            toast.remove("create-speech");
          },
        },
      ],
      timeout: 30000,
      icon: "i-ooui:success",
    });

    isOpenCreateStoryModal.value = false;
  }
};

const { t } = useI18n();
const formats = [
  { value: "mp3", label: "MP3" },
  { value: "wav", label: "WAV " },
  // { value: "flac", label: "FLAC" },
];

const channels = [
  { value: "mono", label: "Mono" },
  { value: "stereo", label: "Stereo" },
];

const { getAccentByValue } = useVoiceLibrary(t);

const showPayAttention = ref(true);

// Feature flag logic: Allow access if feature flag is enabled OR user is superuser
const features = config.public.features;
const canUseOutputChannel = computed(() => {
  return (features as any).outputChannel || isSuperUser.value;
});

watch(isOpenCreateStoryModal, (newValue) => {
  if (!newValue) {
    clearErrors();
  }
});
</script>

<template>
  <div>
    <UModal v-model="isOpenCreateStoryModal">
      <UForm @submit="onSubmit">
        <UCard
          :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }"
        >
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                {{ $t("Ready to create a new story?") }}
              </h3>
              <UButton
                color="gray"
                variant="ghost"
                icon="i-heroicons-x-mark-20-solid"
                class="-my-1"
                @click="isOpenCreateStoryModal = false"
              />
            </div>
          </template>

          <div class="space-y-3">
            <UFormGroup :label="$t('Story name')" name="name">
              <UInput
                v-model="storyOptions.name"
                :placeholder="$t('Your story name here (optional)')"
              />
            </UFormGroup>
            <UFormGroup :label="$t('Output format')" name="format">
              <div class="flex flex-row gap-6">
                <URadio
                  v-for="format of formats"
                  :key="format.value"
                  v-model="storyOptions.format"
                  v-bind="format"
                />
              </div>
            </UFormGroup>
            <UFormGroup
              v-if="canUseOutputChannel"
              :label="$t('Output channel')"
              name="output_channel"
            >
              <div class="flex flex-row gap-6">
                <URadio
                  v-for="channel of channels"
                  :key="channel.value"
                  v-model="storyOptions.output_channel"
                  v-bind="channel"
                />
              </div>
            </UFormGroup>
            <UMeterGroup :max="totalDuration" label="Storage usage">
              <template #indicator>
                <div class="flex gap-1.5 justify-between text-sm">
                  <label class="block font-medium text-gray-700 dark:text-gray-200">
                    {{ $t("Blocks") }}

                    <span> ({{ blocks.length }}) </span>
                  </label>
                  <p class="text-gray-500 dark:text-gray-400">
                    {{ $t("Audio duration") }}
                    <span v-if="isNaN(totalDuration)"> {{ $t("Auto calculate") }} </span>
                    <span v-else> ~ {{ totalDuration.toFixed(3) }}s </span>
                    <span> ({{ totalTextLength }} {{ $t("chars") }}) </span>
                  </p>
                </div>
              </template>
              <UMeter
                v-if="!isNaN(totalDuration)"
                :value="totalListeningTime"
                color="green"
                :label="$t('Voice time')"
                icon="i-fluent-person-voice-16-filled"
              >
              </UMeter>
              <UMeter
                v-if="!isNaN(totalDuration)"
                :value="totalSilenceTime"
                color="red"
                :label="$t('Silence time')"
                icon="i-mdi-mute"
              />
            </UMeterGroup>
            <UFormGroup :label="$t('Voices are in use')">
              <UAvatarGroup size="sm" :max="10">
                <UAvatar
                  v-for="voice in voicesSummary"
                  v-bind="voice?.avatar"
                  :icon="
                    getAccentByValue(voice?.accent || '')?.icon ||
                    'i-iconoir-voice-circle'
                  "
                  :alt="voice.name"
                  :ui="{
                    icon: {
                      size: { sm: 'w-8 h-8' },
                    },
                  }"
                />
              </UAvatarGroup>
            </UFormGroup>

            <UAlert
              v-if="showPayAttention"
              :close-button="{
                icon: 'i-heroicons-x-mark-20-solid',
                color: 'gray',
                variant: 'link',
                padded: false,
              }"
              @close="showPayAttention = false"
              icon="i-clarity-info-solid"
              :title="$t('Pay attention')"
              color="yellow"
              variant="soft"
            >
              <template #description>
                <div class="flex flex-col gap-2">
                  <div>
                    {{
                      $t(
                        "Due to some limitations of Open AI, this function currently only works well in English."
                      )
                    }}
                  </div>
                  <div>
                    {{
                      $t(
                        "By clicking the button below, you agree and understand the risks regarding the quality of the resulting audio file"
                      )
                    }}
                  </div>
                </div>
              </template>
            </UAlert>
            <UAlert
              v-if="errors['createStoryBlocks'] === 'NOT_VERIFY_ACCOUNT'"
              icon="i-heroicons-information-circle-solid"
              color="red"
              variant="soft"
              :title="$t('Error')"
              :description="$t(`NOT_VERIFY_ACCOUNT`)"
              :actions="[
                {
                  label: $t('Resend verify email'),
                  variant: 'solid',
                  color: 'red',
                  click: () => authStore.resendVerifyEmail(),
                },
              ]"
            />
            <UAlert
              v-if="
                ['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(
                  errors['createStoryBlocks'] || ''
                )
              "
              icon="i-heroicons-information-circle-solid"
              color="red"
              variant="soft"
              :title="$t('Error')"
              :description="$t(errors['createStoryBlocks'] || '')"
              :actions="[
                {
                  label: $t('Buy credits'),
                  variant: 'solid',
                  color: 'red',
                  click: () => navigateTo('/profile/buy-credits'),
                },
              ]"
            />
            <UAlert
              v-else-if="errors['createStoryBlocks']"
              icon="i-heroicons-information-circle-solid"
              color="red"
              variant="soft"
              :title="$t('Error while creating story')"
              :description="$t('Please check your story blocks and try again.')"
            />
            <UAlert
              v-if="totalTextLength === 0"
              icon="i-heroicons-information-circle-solid"
              color="red"
              variant="soft"
              :title="$t('Error while creating story')"
              :description="$t('Can not create empty story.')"
            />
          </div>
          <template #footer>
            <div class="flex flex-row justify-end">
              <UButton
                :type="totalTextLength === 0 ? 'button' : 'submit'"
                icon="i-ep-right"
                color="primary"
                variant="solid"
                :trailing="true"
                :loading="loadings.createStoryBlocks"
                :disabled="totalTextLength === 0"
              >
                {{ $t("Let's start creating") }}
              </UButton>
            </div>
          </template>
        </UCard>
      </UForm>
    </UModal>
  </div>
</template>

<template>
  <UModal
    v-model="isOpenBatchUpdateModal"
    :ui="{
      width: 'sm:max-w-xl',
    }"
  >
    <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
      <template #header>
        {{ t("Batch Update") }}
      </template>
      <UFormGroup size="xs" :label="$t('Block name')" class="mb-3">
        <template #hint>
          <UPopover
            class="h-full"
            mode="hover"
            :popper="{ placement: 'top' }"
            :ui="{ wrapper: 'items-center justify-center flex' }"
          >
            <UIcon name="i-ri-question-fill" class="me-1" />

            <template #panel>
              <div class="p-2">
                {{ $t("We will export to the file with the name you set here.") }}
              </div>
            </template>
          </UPopover>
        </template>
        <UInput
          color="primary"
          :placeholder="$t('Enter block name here')"
          v-model="voiceSetting.name"
          :padded="true"
          :ui="{
            rounded: 'rounded-md',
          }"
        />
      </UFormGroup>
      <StorySetting :modelValue="voiceSetting" @update-speed="onUpdateSpeed" batch />

      <template #footer>
        <div class="flex flex-row gap-2 justify-end">
          <UButton color="white" @click="isOpenBatchUpdateModal = false" class="px-6">
            {{ t("Cancel") }}
          </UButton>
          <UButton
            color="primary"
            @click="storyStore.batchUpdateStorySettings(voiceSetting)"
            class="px-6"
          >
            {{ t("Update") }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
  <Teleport to="body">
    <UModal
      v-model="showVoiceLibrariesForBatchModal"
      :ui="{
        width: 'sm:max-w-7xl',
      }"
      prevent-close
    >
      <VoiceLibrary
        class="flex-1"
        full
        @select="onSelectVoiceLibraryForBatch"
        show-close-button
        @close="showVoiceLibrariesForBatchModal = false"
      />
    </UModal>
  </Teleport>
</template>

<script setup lang="ts">
const translateStore = useTranslateStore();
const { speedOptions } = storeToRefs(translateStore);
const voiceLibraryStore = useVoiceLibraryStore();
const storyStore = useStoryStore();
const {
  isOpenBatchUpdateModal,
  showVoiceLibrariesForBatchModal,
  selecteds,
} = storeToRefs(storyStore);
const { t } = useI18n();

const voiceSetting = ref({
  voice_id: "",
  name: "",
  voice: null,
});

const onSelectVoiceLibraryForBatch = (id: string) => {
  const voice = voiceLibraryStore.voiceLibraries.find((v) => v.id === id);
  voiceSetting.value.voice_id = id;
  voiceSetting.value.voice = {
    ...voice,
    avatar: {
      src:
        voice?.type === "openai_voice"
          ? `assets/images/avatars/${voice?.speaker_name?.toLowerCase()}.svg`
          : "",
    },
  };
  showVoiceLibrariesForBatchModal.value = false;
  isOpenBatchUpdateModal.value = true;
};

const onUpdateSpeed = (speed: number) => {
  voiceSetting.value = {
    ...voiceSetting.value,
    speed,
  } as any;
};
</script>

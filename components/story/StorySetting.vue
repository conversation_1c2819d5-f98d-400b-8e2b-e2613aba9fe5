<template>
  <div
    class="rounded-b-md"
    :class="{
      'border-transparent bg-transparent': batch,
      'px-2 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-300 dark:border-gray-700': !batch,
    }"
  >
    <div class="flex flex-row justify-between items-center">
      <div class="grid grid-cols-3 gap-3 items-end w-full">
        <UFormGroup size="xs" :label="$t('Silence before')">
          <template #hint>
            <UPopover
              class="h-full"
              mode="hover"
              :popper="{ placement: 'top' }"
              :ui="{ wrapper: 'items-center justify-center flex' }"
            >
              <UIcon name="i-ri-question-fill" class="me-1" />

              <template #panel>
                <div class="p-2">
                  {{ $t("Add a period of silence before the speech starts.") }}
                </div>
              </template>
            </UPopover>
          </template>
          <UInput
            icon="i-jam-volume-mute-circle"
            size="xs"
            v-model="modelValue.silence_before"
          >
            <template #trailing>
              <span class="text-gray-500 dark:text-gray-400 text-xs">
                {{ $t("seconds") }}
              </span>
            </template>
          </UInput>
        </UFormGroup>
        <!-- <UFormGroup size="xs" :label="$t('Duration')">
                    <template #hint>
                        <UPopover
                            class="h-full"
                            mode="hover"
                            :popper="{ placement: 'top' }"
                            :ui="{ wrapper: 'items-center justify-center flex' }"
                        >
                            <UIcon name="i-ri-question-fill" class="me-1" />

                            <template #panel>
                                <div class="p-2">
                                    {{
                                        $t(
                                            'Specify the duration of the speech. If not set, the duration will be calculated automatically.'
                                        )
                                    }}
                                </div>
                            </template>
                        </UPopover>
                    </template>
                    <UInput
                        icon="i-game-icons-duration"
                        size="xs"
                        v-model="modelValue.duration"
                        :placeholder="$t('Auto')"
                    >
                        <template #trailing>
                            <span v-if="modelValue.duration" class="text-gray-500 dark:text-gray-400 text-xs">
                                {{ $t('seconds') }}
                            </span>
                            <span v-else />
                        </template>
                    </UInput>
                </UFormGroup> -->
        <UFormGroup size="xs" :label="$t('Voice')" class="w-full">
          <BaseVoiceBadge
            class="min-w-32 w-full animate__animated"
            :class="{
              animate__bounceInRight: animation,
            }"
            :voice_id="modelValue?.voice_id"
            :key="modelValue?.voice_id"
            :voice="modelValue?.voice"
            @click="onSelectVoice"
          />
        </UFormGroup>
        <UFormGroup
          size="xs"
          :label="$t('Emotion')"
          :hint="$t('(coming soon)')"
          class="w-full hidden lg:block"
        >
          <USelectMenu
            size="xs"
            :placeholder="$t('Emotion')"
            icon="i-ic-outline-emoji-emotions"
            :ui-menu="{
              width: 'w-screen max-w-44',
            }"
            :ui="{
              rounded: 'rounded-full',
            }"
            disabled
          >
          </USelectMenu>
        </UFormGroup>
        <SliderSpeed :modelValue="modelValue.speed" @onChangeSpeed="onChangeSpeed" @onError="onError" />
        <div class="">
          <slot />
        </div>
      </div>
    </div>
    <div v-if="errorMessage" class="text-red-500 text-xs mx-2">{{ errorMessage }}</div>
  </div>
</template>

<script setup lang="ts">
const translateStore = useTranslateStore();
const { speedOptions } = storeToRefs(translateStore);
const storyStore = useStoryStore();

const {
  showVoiceLibrariesModal,
  showVoiceLibrariesForBatchModal,
  selecteds,
} = storeToRefs(storyStore);
const { t } = useI18n();
const props = defineProps({
  active: Boolean,
  modelValue: {
    type: Object as PropType<any>,
    required: true,
  },
  index: Number,
  batch: Boolean,
});


const onChangeSpeed = async (value: number) => {
  emit("updateSpeed", value);
};

const errorMessage = ref("")

const onError = (error: string) => {
  errorMessage.value = error
}


const emit = defineEmits([
  "update:modelValue",
  "delete",
  "edit",
  "duplicate",
  "select",
  "updateSpeed",
  "deleteAll"
]);

const speedOptionsFormat = computed(() => {
  return speedOptions.value.map((item) => {
    return {
      value: item.value,
      label: t(item.label),
    };
  });
});

const onSelectVoice = () => {
  if (!props.batch) {
    showVoiceLibrariesModal.value = true;
  } else {
    storyStore.showVoiceLibrariesForBatchModal = true;
  }
};

const animation = ref(false);
const animationTimeout = ref();
watch(
  () => props.modelValue?.voice_id,
  (newValue, oldValue) => {
    if (oldValue && oldValue !== newValue) {
      animation.value = true;
      clearTimeout(animationTimeout.value);
      animationTimeout.value = setTimeout(() => {
        animation.value = false;
      }, 3000);
    }
  },
  { immediate: true, deep: true }
);
</script>

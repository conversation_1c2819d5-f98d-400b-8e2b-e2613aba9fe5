<script setup lang="ts">
import { ModeSelectDurationEnum } from '~/types'
const storyStore = useStoryStore()
const { isOpenSelectDurationModal } = storeToRefs(storyStore)
const { setModeSelectDuration } = storyStore

</script>

<template>
    <UModal v-model="isOpenSelectDurationModal" prevent-close>
        <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
            <template #header>
                <div class="flex items-center justify-between">
                    <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                        {{ $t('Select mode duration') }}
                    </h3>
                    <!-- <UButton
                        color="gray"
                        variant="ghost"
                        icon="i-heroicons-x-mark-20-solid"
                        class="-my-1"
                        @click="isOpenSelectDurationModal = false"
                    /> -->
                </div>
            </template>
            <div class="space-y-4">
                {{
                    $t(
                        `Do you want to use the audio length according to the SRT file? If you choose 'Yes', the audio length will be adjusted according to the SRT file. If you choose 'No', we will calculate the audio length based on the text length.`
                    )
                }}
            </div>
            <template #footer>
                <div class="flex justify-center gap-2 space-x-4">
                    <UButton class="w-24 justify-center" :ui="{ ring: 'ring-gray-300' }" square size="lg" variant="outline" @click="setModeSelectDuration(ModeSelectDurationEnum.MANUAL)">{{ $t('No') }}</UButton>
                    <UButton class="w-24 justify-center" :ui="{ ring: 'ring-gray-300' }" square size="lg" variant="solid" @click="setModeSelectDuration(ModeSelectDurationEnum.AUTO)">{{ $t('Yes') }}</UButton>
                </div>
            </template>
        </UCard>
    </UModal>
</template>

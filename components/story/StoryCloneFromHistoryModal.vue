<template>
    <UModal
        v-model="isOpenStoryCloneFromHistoryModal"
        :ui="{
            width: 'sm:max-w-5xl',
        }"
    >
        <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
            <template #header>
                {{ t('Clone story from history') }}
            </template>
            <loading loader="bars" height="35" v-model:active="isLoading" :is-full-page="true" />
            <div
                v-if="!filteredHistories.length"
                class="flex-1 text-center text-gray-600 dark:text-gray-400 font-thin pt-10"
            >
                {{ $t('No history found.') }}
            </div>
            <div class="px-4">
                <ol
                    v-if="filteredHistories"
                    class="relative border-l border-gray-200 dark:border-gray-700 list-none pl-0"
                >
                    <template :key="row.id + row?.media_url" v-for="row in filteredHistories">
                        <component
                            :is="historyItemComponents[row.type]"
                            v-bind="row"
                            selectable
                            mini
                            @select="onSelectStoryHistory"
                        />
                    </template>
                </ol>
            </div>
            <div class="flex flex-inline w-max mx-auto">
                <!-- Previous Button -->
                <a
                    v-if="currentPage > 1"
                    @click="previousPage"
                    href="#"
                    class="inline-flex items-center px-4 py-2 mr-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                >
                    <svg
                        aria-hidden="true"
                        class="w-5 h-5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            fill-rule="evenodd"
                            d="M7.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l2.293 2.293a1 1 0 010 1.414z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                    {{ $t('Previous') }}
                </a>
                <a
                    v-if="currentPage < totalPage"
                    @click="nextPage"
                    href="#"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
                >
                    {{ $t('Next') }}
                    <svg
                        aria-hidden="true"
                        class="w-5 h-5 ml-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            fill-rule="evenodd"
                            d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                        ></path>
                    </svg>
                </a>
            </div>
            <template #footer>
                <div class="flex flex-row gap-2 justify-end">
                    <UButton color="white" @click="isOpenStoryCloneFromHistoryModal = false" class="px-6">
                        {{ t('Cancel') }}
                    </UButton>
                </div>
            </template>
        </UCard>
    </UModal>
</template>

<script setup lang="ts">
import Loading from 'vue-loading-overlay'
import HistoryItemStory from '~/components/HistoryItemStory.vue'
const storyStore = useStoryStore()
const { isOpenStoryCloneFromHistoryModal } = storeToRefs(storyStore)
const { t } = useI18n()
const historyStore = useHistoryStore()
const { filteredHistories, isLoading, totalPage, currentPage, filterBy } = storeToRefs(historyStore)

const historyItemComponents = {
    'tts-story': HistoryItemStory,
}

const previousPage = async () => {
    currentPage.value = currentPage.value > 1 ? currentPage.value - 1 : 1
    await historyStore.filterHistories()
}

const nextPage = async () => {
    currentPage.value++
    await historyStore.filterHistories()
}

const onSelectStoryHistory = (history: any) => {
    historyStore.cloneHistory(history)
    isOpenStoryCloneFromHistoryModal.value = false
}

onMounted(() => {
    nextTick(() => {
        currentPage.value = 1
        filterBy.value = 'tts-story'
        historyStore.filterHistories()
    })
})
</script>

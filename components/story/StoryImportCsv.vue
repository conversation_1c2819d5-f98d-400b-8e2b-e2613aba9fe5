import { storeToRefs } from 'pinia';
<template>
    <div>
        <UButton
            icon="i-material-symbols-csv-outline"
            :size="mini ? 'md' : 'sm'"
            color="white"
            variant="solid"
            :label="mini ? '' : $t('Import csv')"
            :trailing="false"
            @click="openFileUpload"
            :ui="{
                rounded: mini ? 'rounded-md' : 'rounded-full',
            }"
            :disabled="!canImportFromCsv"
        />
        <input
            :value="csvFile"
            id="dropzone-file"
            type="file"
            class="hidden"
            @change="handleFileUpload"
            accept=".csv"
        />
    </div>
</template>

<script setup lang="ts">
defineProps({
    mini: Boolean,
})
const storyStore = useStoryStore()
const voiceLibraryStore = useVoiceLibraryStore()
const { canImportFromCsv } = storeToRefs(voiceLibraryStore)
const { csvFile } = storeToRefs(storyStore)
const handleFileUpload = (e: any) => {
    const file = e.target.files[0]
    if (file) {
        storyStore.importFromCsvFile(file)
    }
}

const openFileUpload = () => {
    const fileInput = document.getElementById('dropzone-file')
    if (fileInput) {
        fileInput.click()
    }
}
</script>

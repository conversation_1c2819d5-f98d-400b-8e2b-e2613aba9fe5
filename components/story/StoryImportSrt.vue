<template>
  <div>
    <UButton
      icon="i-mingcute-subtitle-line"
      :size="mini ? 'md' : 'sm'"
      color="white"
      variant="solid"
      :label="mini ? '' : $t('Import srt')"
      :trailing="false"
      @click="openFileUpload"
      :ui="{
        rounded: mini ? 'rounded-md' : 'rounded-full',
      }"
      class="w-full justify-center"
    />
    <input
      :value="srtFile"
      id="srt-file-import"
      type="file"
      class="hidden"
      @change="handleFileUpload"
      accept=".srt"
    />
  </div>
</template>

<script setup lang="ts">
defineProps({
  mini: Boolean,
});
const storyStore = useStoryStore();
const { srtFile } = storeToRefs(storyStore);
const handleFileUpload = (e: any) => {
  const file = e.target.files[0];
  if (file) {
    storyStore.setSelectedFile(file);
  }
};

const openFileUpload = () => {
  const fileInput = document.getElementById("srt-file-import");
  if (fileInput) {
    fileInput.click();
  }
};
</script>

import { storeToRefs } from 'pinia';
<template>
    <div>
        <UButton
            icon="icon-park-outline:history-query"
            :size="mini ? 'md' : 'sm'"
            color="white"
            variant="solid"
            :label="mini ? '' : $t('Clone from history')"
            :trailing="false"
            @click="isOpenStoryCloneFromHistoryModal = true"
            :ui="{
                rounded: mini ? 'rounded-md' : 'rounded-full',
            }"
        />
        <StoryCloneFromHistoryModal />
    </div>
</template>

<script setup lang="ts">
defineProps({
    mini: Boolean,
})
const storyStore = useStoryStore()
const { isOpenStoryCloneFromHistoryModal } = storeToRefs(storyStore)
</script>

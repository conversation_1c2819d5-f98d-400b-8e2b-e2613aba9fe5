<template>
    <div v-if="active" class="flex flex-col overflow-visible my-3">
        <UFormGroup size="xs" :label="$t('Block name')" class="mb-3">
            <template #hint>
                <UPopover class="h-full" mode="hover" :ui="{ wrapper: 'items-center justify-center flex' }">
                    <UIcon name="i-ri-question-fill" class="me-1" />

                    <template #panel>
                        <div class="p-2">
                            {{ $t('We will export to the file with the name you set here.') }}
                        </div>
                    </template>
                </UPopover>
            </template>
            <UInput
                :placeholder="$t('Enter block name here')"
                :model-value="modelValue?.name"
                :padded="true"
                @input="$emit('update:modelValue', { ...modelValue, name: $event.target.value })"
                :ui="{
                    rounded: 'rounded-md',
                }"
            />
        </UFormGroup>
        <UFormGroup
            size="xs"
            :label="modelValue?.isSoundEffect ? $t('Sound effect') : $t('Input text')"
            :hint="modelValue?.isSoundEffect ? $t('We will not convert this text to speech.') : ''"
        >
            <div class="relative flex flex-col ring-1 rounded-md ring-gray-300 dark:ring-gray-700">
                <div class="relative">
                    <UTextarea
                        variant="none"
                        autoresize
                        :placeholder="$t('Enter the text you want to convert to speech here.')"
                        :model-value="modelValue?.input"
                        @input="$emit('update:modelValue', { ...modelValue, input: $event.target.value })"
                        :disabled="modelValue?.isSoundEffect"
                        :maxlength="maxLengthTextStory"
                    />
                    <div
                        v-if="modelValue?.input"
                        class="absolute bottom-[10px] right-3 text-right text-xs font-thin dark:text-gray-300 flex flex-row space-x-4 items-center"
                    >
                        <div>{{ modelValue?.input?.length }} / {{ maxLengthTextStory }}</div>
                    </div>
                </div>
                <StorySetting
                    :modelValue="modelValue"
                    @update:model-value="emit('update:modelValue')"
                    @updateSpeed="emit('update:modelValue', { ...modelValue, speed: $event })"
                >
                    <div class="h-full pb-0 items-end justify-end flex flex-row">
                        <UTooltip :text="$t('Duplicate')" :popper="{ offsetDistance: 3, arrow: true }">
                            <UButton
                                icon="i-fa-clone"
                                size="xs"
                                color="gray"
                                square
                                variant="ghost"
                                @click="$emit('duplicate', modelValue)"
                            />
                        </UTooltip>
                        <UTooltip :text="$t('Delete')" :popper="{ offsetDistance: 3, arrow: true }">
                            <UButton
                                icon="i-fa-trash-o"
                                size="xs"
                                color="gray"
                                square
                                variant="ghost"
                                div
                                @click="$emit('delete', modelValue?.id)"
                            />
                        </UTooltip>
                    </div>
                </StorySetting>
            </div>
        </UFormGroup>
    </div>
    <div v-else class="group text-sm py-1 h-full truncate flex flex-row items-center justify-between gap-2 my-3">
        <div @click="onToggle">
            <UIcon
                v-if="selecteds.includes(modelValue?.id)"
                name="i-icon-park-solid-check-one"
                class="text-green-500 text-lg"
            />
            <UIcon v-else name="i-mingcute-round-line" class="text-gray-500 text-lg" />
        </div>
        <div
            class="flex flex-col -gap-1 flex-1 w-fit truncate"
            @click="onSelect"
            :class="{
                'text-gray-400': modelValue?.isSoundEffect,
            }"
        >
            <div class="text-xs font-semibold">
                {{ modelValue?.name || `#${index}` }}
            </div>
            <div v-if="!modelValue?.input" class="text-gray-500 text-left">
                {{ $t('Click here to edit text') }}
            </div>
            <div v-else class="truncate">
                {{ modelValue?.input }}
            </div>
        </div>
        <div v-if="modelValue?.isSoundEffect">
            <UBadge
                color="white"
                class="h-7"
                variant="solid"
                :ui="{ rounded: 'rounded-full', size: { sm: 'pl-1 pr-2' } }"
                size="sm"
            >
                <div class="flex flex-row items-center gap-1 text-gray-400">
                    <div>
                        <UIcon name="i-jam-volume-mute-circle" class="text-gray-400 text-xl" />
                    </div>
                    <div>{{ modelValue?.duration }}s</div>
                </div>
            </UBadge>
        </div>
        <div v-else class="relative overflow-visible flex flex-row items-center gap-1" @click="onSelect">
            <UBadge
                color="white"
                class="h-7"
                variant="solid"
                :ui="{ rounded: 'rounded-full', size: { sm: 'pl-1 pr-2' } }"
                size="sm"
            >
                <div class="flex flex-row items-center gap-1 text-gray-400">
                    <div>
                        <UIcon name="i-jam-volume-mute-circle" class="text-gray-400 text-xl" />
                    </div>
                    <div>{{ modelValue?.silence_before }}s</div>
                </div>
            </UBadge>
            <BaseVoiceBadge
                :key="modelValue?.voice_id"
                :voice_id="modelValue?.voice_id"
                :voice="modelValue?.voice"
                :setting="modelValue"
            />
            <UBadge
                color="white"
                class="h-7"
                variant="solid"
                :ui="{ rounded: 'rounded-full', size: { sm: 'pl-1 pr-2' } }"
                size="sm"
            >
                <div class="flex flex-row items-center gap-1 text-gray-400">
                    <div>
                        <UIcon name="i-line-md-speed" class="text-gray-400 text-xl" />
                    </div>
                    <div>{{ modelValue?.speed }}x</div>
                </div>
            </UBadge>
        </div>
        <div class="border-l dark:border-gray-700 pl-1.5 ml-1 flex-row flex">
            <UTooltip :text="$t('Duplicate')" :popper="{ offsetDistance: 3, arrow: true }">
                <UButton
                    icon="i-fa-clone"
                    size="xs"
                    color="gray"
                    square
                    variant="ghost"
                    @click="$emit('duplicate', modelValue)"
                />
            </UTooltip>
            <UTooltip :text="$t('Delete')" :popper="{ offsetDistance: 3, arrow: true }">
                <UButton
                    icon="i-fa-trash-o"
                    size="xs"
                    color="gray"
                    square
                    variant="ghost"
                    div
                    @click="$emit('delete', modelValue?.id)"
                />
            </UTooltip>
        </div>
    </div>
</template>

<script setup lang="ts">
const translateStore = useTranslateStore()
const { speedOptions } = storeToRefs(translateStore)
const storyStore = useStoryStore()
const { maxLengthTextStory, mode, selecteds } = storeToRefs(storyStore)
const { t } = useI18n()
const props = defineProps({
    active: Boolean,
    modelValue: {
        type: Object as PropType<any>,
        required: true,
    },
    index: Number,
})

const emit = defineEmits(['update:modelValue', 'delete', 'edit', 'duplicate', 'select'])

watch(
    () => props.active,
    (active) => {
        if (active) {
            nextTick(() => {
                focusTextarea()
            })
        }
    }
)

const focusTextarea = () => {
    const textarea = document.querySelector('textarea')
    if (textarea) {
        textarea.focus()
    }
}
const speedOptionsFormat = computed(() => {
    return speedOptions.value.map((item) => {
        return {
            value: item.value,
            label: t(item.label),
        }
    })
})

onMounted(() => {
    if (props.active) {
        focusTextarea()
    }
})

const onSelect = () => {
    if (mode.value === 'edit') {
        emit('edit')
    } else if (mode.value === 'select') {
        emit('select', props.modelValue)
    }
}

const onToggle = () => {
    emit('select', props.modelValue)
}

const { voiceEmotions } = useVoiceLibrary(t)
</script>

<template>
  <a
    class="relative flex px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
  >
    <div class="flex-shrink-0 btn-download-result">
      <svg
        v-if="success"
        @click="downloadFile"
        class="rounded-full w-10 h-10 p-2 dark:text-gray-50 dark:group-hover:text-gray-300"
        :class="
          seen
            ? 'text-gray-400 bg-gray-100 dark:bg-gray-500'
            : 'text-primary-500 bg-primary-100 dark:bg-primary-700'
        "
        aria-hidden="true"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 20 19"
      >
        <path
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 15h.01M4 12H2a1 1 0 0 0-1 1v4a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1h-3M9.5 1v10.93m4-3.93-4 4-4-4"
        />
      </svg>
      <svg
        v-else
        class="rounded-full w-10 h-10 p-3 dark:group-hover:text-gray-300"
        :class="
          seen
            ? 'text-red-400 bg-gray-100 dark:bg-gray-500'
            : 'text-white bg-red-400 dark:bg-red-400'
        "
        aria-hidden="true"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 14 14"
      >
        <path
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
        />
      </svg>
      <div
        v-if="!seen"
        class="absolute flex items-center justify-center w-3 h-3 ml-7 -mt-2 bg-primary-600 border border-white rounded-full dark:border-gray-800"
      ></div>
    </div>
    <div @click="onNotificationDetail" class="w-full pl-3">
      <div
        class="text-gray-500 text-sm mb-1.5 dark:text-gray-400 break-word detail-translated-document-link"
      >
        <i18n-t
          v-if="fileName"
          :keypath="
            success ? 'translation_document_completed' : 'translation_document_error'
          "
          tag="p"
        >
          <a
            class="font-semibold underline !break-all"
            :class="
              seen
                ? 'text-gray-500 dark:text-gray-400'
                : success
                ? 'text-primary-500 dark:text-primary-500'
                : 'text-red-300 dark:text-red-300'
            "
            >{{ fileName }}</a
          >
        </i18n-t>
        <p
          v-else
          class="break-word"
          :class="{
            'font-semibold': !seen,
            'text-gray-500 dark:text-gray-400': seen,
          }"
        >
          {{
            success
              ? $t("Your text has been converted to audio successfully!")
              : $t("Failed to convert text to audio.")
          }}
        </p>
      </div>
      <div
        class="text-xs"
        :key="locale"
        :class="
          seen
            ? 'text-gray-500 dark:text-gray-400'
            : success
            ? 'text-primary-500 dark:text-primary-500'
            : 'text-red-300 dark:text-red-300'
        "
      >
        {{ createdAtFormat }}
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import relativeTime from "dayjs/plugin/relativeTime";
dayjs.extend(utc);
dayjs.extend(relativeTime);

const props = defineProps<{
  seen: boolean;
  fileName: string;
  createdAt: string;
  translationId: string;
  historyId: string;
  success: boolean;
  event_type: string;
}>();

const emit = defineEmits<{
  onNotificationDetail: () => void;
  onDownloadFile: () => void;
}>();
const { locale } = useI18n();

const createdAtFormat = computed(() => {
  // locale
  dayjs.locale(locale.value);
  return dayjs.utc(props.createdAt).fromNow();
});
const onNotificationDetail = () => {
  emit("onNotificationDetail");
};

const downloadFile = () => {
  emit("onDownloadFile");
};
</script>

<template>
  <div
    class="py-2 px-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 rounded-b-md flex flex-row gap-2 justify-between"
  >
    <div class="pr-2 flex flex-row gap-3 dark:border-gray-700">
      <UTooltip :text="$t('Select')">
        <!-- <UButton
          @click="storyStore.toggleSelectAll()"
          color="white"
          variant="solid"
          icon="i-bx-select-multiple"
          square
          :ui="{ rounded: 'rounded-md' }"
        ></UButton> -->
        <UDropdown :items="selectOptions" :popper="{ placement: 'bottom-start' }">
          <UButton
            :ui="{ rounded: 'rounded-md' }"
            color="white"
            :label="selecteds.length ? $t('Select ({count})', {
              count: selecteds.length,
            }) : $t('Select')"
            :trailing-icon="selecteds.length ? 'i-fluent-select-all-on-20-regular' : 'i-fluent-select-all-off-20-regular'"
          />
        </UDropdown>
      </UTooltip>
      <UTooltip :text="modeSelectDuration === ModeSelectDurationEnum.AUTO ? $t('Turn Off auto calculate duration') :  $t('Turn On auto calculate duration')">
        <UButton
          @click="toggleModeSelectDuration"
          color="white"
          variant="solid"
          icon="i-material-symbols-edit-audio-rounded"
          square
          :ui="{
            rounded: 'rounded-md',
            color: {
              white: {
                solid:
                  modeSelectDuration === ModeSelectDurationEnum.AUTO
                    ? 'text-gray-900'
                    : 'text-gray-400',
              },
            },
          }"
        ></UButton>
      </UTooltip>
      <UTooltip v-if="activeStory" :text="$t('Preview')">
        <UButton
          color="white"
          variant="solid"
          icon="i-icon-park-outline-preview-open"
          square
          :ui="{ rounded: 'rounded-md' }"
          @click="activeStory = null"
        ></UButton>
      </UTooltip>
      <div class="border-r border-gray-200 dark:border-gray-700"></div>
      <div v-if="mode === 'edit'" class="flex flex-row gap-2">
        <UTooltip :text="$t('Add conversation')">
          <UButton
            @click="storyStore.addStory()"
            color="white"
            variant="solid"
            icon="i-material-symbols-add"
            square
            :ui="{ rounded: 'rounded-md' }"
          ></UButton>
        </UTooltip>
        <!-- <UTooltip :text="$t('Import from csv')">
          <StoryImportCsv mini />
        </UTooltip>
        <UTooltip :text="$t('Import from srt')">
          <StoryImportSrt mini />
        </UTooltip> -->
        <UTooltip v-if="selecteds.length" :text="$t('Batch Update')">
          <UButton
            @click="isOpenBatchUpdateModal = true"
            color="white"
            variant="solid"
            icon="i-carbon-batch-job"
            square
            :ui="{ rounded: 'rounded-md' }"
          ></UButton>
        </UTooltip>
        <UTooltip v-if="selecteds.length" :text="$t('Delete')">
          <UButton
            @click="storyStore.removeSelectedStories()"
            color="white"
            variant="solid"
            icon="i-carbon-trash-can"
            square
            :ui="{ rounded: 'rounded-md', color: { white: { solid: 'text-red-500' } } }"
          ></UButton>
        </UTooltip>
      </div>
    </div>
    <div class="flex flex-row gap-2">
      <UTooltip :text="$t('Delete All')">
        <!-- Remove all icon -->
        <UButton
          @click="storyStore.removeAllStories()"
          color="white"
          variant="solid"
          icon="i-mdi-chat-delete-outline"
          square
          :ui="{ rounded: 'rounded-md', color: { white: { solid: 'text-red-500' } } }"
        >
        </UButton>
      </UTooltip>
      <UTooltip :text="$t('Export to CSV')">
        <!-- Remove all icon -->
        <UButton
          @click="storyStore.exportToCSV()"
          color="white"
          variant="solid"
          icon="i-clarity-export-line"
          square
          :ui="{ rounded: 'rounded-md' }"
        >
        </UButton>
      </UTooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ModeSelectDurationEnum } from "~/types";
const storyStore = useStoryStore();
const {
  activeStory,
  mode,
  selecteds,
  isOpenBatchUpdateModal,
  voicesSummary,
  modeSelectDuration,
  hasItemImportBySrt
} = storeToRefs(storyStore);
const { t } = useI18n();
const { toggleModeSelectDuration } = storyStore;
const { getAccentByValue } = useVoiceLibrary(t);
const selectOptions = computed(() => {
  return [
    [
      {
        label: t("Select All"),
        icon: "i-fluent-select-all-on-16-filled",
        click: () => {
          storyStore.toggleSelectAll();
        },
      },
    ],
    [
      {
        label: t("Select Even"),
        icon: "i-mdi-number-2-box-multiple-outline",
        click: () => {
          storyStore.selectAllEven();
        },
      },
      {
        label: t("Select Odd"),
        icon: "i-mdi-number-3-box-multiple-outline",
        click: () => {
          storyStore.selectAllOdd();
        },
      },
    ],
    voicesSummary.value.map((voice: any) => ({
      label: t('Select by') + ' ' + voice?.speaker_name,
      avatar: {
        src: voice?.avatar?.src,
        icon: getAccentByValue(voice?.accent || '')?.icon || 'i-iconoir-voice-circle',
      },
      click: () => {
        storyStore.selectByVoice(voice?.id);
      },
    })),
    [
      {
        label: t("Unselect"),
        icon: "i-mdi-select-remove",
        click: () => {
          storyStore.unselectAll();
        },
      },
    ],
  ];
});
</script>

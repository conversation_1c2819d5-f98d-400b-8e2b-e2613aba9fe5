<template>
    <div
        class="flex flex-col h-full max-h-full md:pt-2 px-4 pl-8 bg-gray-100 border border-gray-200 rounded-md dark:bg-gray-800 dark:border-gray-600 text-sm text-gray-900 dark:text-gray-100"
    >
        <div ref="resultRef" :class="`flex-1 md:pl-4`">
            <textarea
                id="text-translate-result"
                ref="resultTextArea"
                class="scrollbar-thin w-full pb-6 resize-none overflow-y-auto md:h-full min-h-[238px] px-0 text-sm text-gray-900 bg-gray-100 border-0 dark:bg-gray-800 focus:ring-0 dark:text-white dark:placeholder-gray-500 placeholder-gray-300"
                v-model="translateTextResult[translatingSesionId]"
                readonly
            >
            </textarea>
        </div>

        <div v-if="showAccountVerifyWarning" class="shrink-0">
            <AccountVerifyWarning />
        </div>
        <div v-else-if="isNotEnoughTokens" class="shrink-0">
            <NotEnoughTokensWarning />
        </div>
        <div v-else-if="isHasLimited && showLimitedWarning" class="shrink-0">
            <LimitedWarning @dismiss="translateStore.setShowLimitedWarning(false)" />
        </div>
        <div v-show="translateTextResult[translatingSesionId]" class="shrink-0 pb-1 -mr-3 flex justify-end">
            <div class="mr-0">
                <a
                    id="btn-copy-result"
                    @click="copyResult"
                    data-tooltip-target="tooltip-copy"
                    class="border-0 inline-flex justify-center p-1.5 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                    <svg
                        v-if="isCopied"
                        class="w-5 h-5 text-primary-500 dark:text-primary-600 animate-pulse"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                    >
                        <path
                            d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"
                        />
                    </svg>
                    <span v-else class="material-symbols-outlined"> content_copy </span>
                </a>
                <div
                    id="tooltip-copy"
                    role="tooltip"
                    class="inline-block absolute invisible z-50 py-2 px-3 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300 tooltip"
                >
                    <span>{{ isCopied ? $t('Copied !') : $t('Copy translation') }}</span>
                    <div class="tooltip-arrow" data-popper-arrow></div>
                </div>
            </div>
            <div id="history-item-rate-text" class="flex">
                <HistoryItemRate
                    @onRated="onRated"
                    flat
                    :uuid="resultUuid"
                    :rating="liked ? 'thumbs_up' : disLiked ? 'thumbs_down' : ''"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import LimitedWarning from '~/components/LimitedWarning.vue'
import NotEnoughTokensWarning from '~/components/NotEnoughTokensWarning.vue'
import AccountVerifyWarning from '~/components/AccountVerifyWarning.vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'
import { useAuthStore } from '~/stores/auth'
import HistoryItemRate from './HistoryItemRate.vue'

const translateStore = useTranslateStore()
const authStore = useAuthStore()
const isCopied = ref(false)
const resultTextArea = ref<HTMLTextAreaElement | null>(null)

const { result, translateTextResult, translatingSesionId, isHasLimited, resultUuid, showLimitedWarning, showAccountVerifyWarning, liked, disLiked } =
    storeToRefs(translateStore)
const { isNotEnoughTokens } = storeToRefs(authStore)

const copyResult = () => {
    const el = document.createElement('textarea')
    el.value = translateTextResult.value[translatingSesionId.value]
    document.body.appendChild(el)
    el.select()
    document.execCommand('copy')
    document.body.removeChild(el)
    isCopied.value = true
    setTimeout(() => {
        isCopied.value = false
    }, 1700)
}

watch(
    () => translateTextResult.value[translatingSesionId.value],
    () => {
        //scroll result text area to bottom
        if (resultTextArea.value) {
            resultTextArea.value.scrollTop = resultTextArea.value.scrollHeight
        }

        // reset like/dislike when result changed to empty
        if (!translateTextResult.value[translatingSesionId.value]) {
            liked.value = false
            disLiked.value = false
        }
    }, 
    { immediate: true, deep: true }
)

const onRated = (rated: any) => {
    liked.value = rated.liked
    disLiked.value = rated.disLiked
}
</script>

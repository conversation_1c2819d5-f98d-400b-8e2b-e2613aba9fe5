<script lang="ts" setup>
const appStore = useAppStore()
const { contactEmail } = storeToRefs(appStore)
const config = useRuntimeConfig()
const appVersion = config.public.NUXT_APP_VERSION
const version = ref('')
const authStore = useAuthStore()
const { modals } = storeToRefs(authStore)
onMounted(async () => {
    const { data }: { data: Ref<any> } = await useFetch('/version.json')
    version.value = data.value?.version || appVersion
})
</script>

<template>
    <footer class="shadow">
        <div
            class="w-full mx-auto pb-1 pt-4 md:pb-1 flex flex-col items-end md:flex-row md:flex-inline justify-between px-4"
        >
            <div class="text-sm text-gray-500 dark:text-gray-400 flex flex-col -gap-2">
                <div class="flex flex-row items-center">
                    © Copyright {{ new Date().getFullYear() }}, Text To Speech OpenAI .
                    <a>Version {{ version }}</a>
                    <div
                        class="flex flex-row items-center ml-2 border-l border-gray-300 dark:border-gray-700 pl-2 gap-1 hover:underline cursor-pointer hover:text-primary-500"
                        @click="
                            navigateTo(
                                'https://www.youtube.com/watch?v=OdDdyu2k20A&list=PL8DoYyQx5K5GRv_4q5zL7rCKsNEtXH8dN',
                                {
                                    external: true,
                                    open: {
                                        target: '_blank',
                                    },
                                }
                            )
                        "
                    >
                        <UIcon name="i-logos-youtube-icon" />
                        {{ $t('Guide') }}
                    </div>
                    <div
                        class="flex flex-row items-center ml-2 border-l border-gray-300 dark:border-gray-700 pl-2 gap-1 hover:underline cursor-pointer hover:text-primary-500"
                        @click="
                            navigateTo('/privacy', {
                                open: {
                                    target: '_blank',
                                },
                            })
                        "
                    >
                        <UIcon name="material-symbols:privacy-tip-outline-rounded" class="text-lg" />
                        {{ $t('Privacy Policy') }}
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row items-center gap-2">
                    <div class="text-[12px] border-r border-gray-300 dark:border-gray-700 pr-2">
                        <div>
                            {{
                                $t(
                                    'The website is jointly operated by A2ZAI LTD No:16078579 Registered address at 483 Green Lanes, London, England, N13 4BS'
                                )
                            }}
                        </div>
                        <div>
                            {{ $t('Organization Address: 3100 BOWDOIN ST DES MOINES, IA 50313 United States') }}
                        </div>
                    </div>
                    <div class="block text-[12px] text-gray-500 text-center justify-end items-end dark:text-gray-400">
                        <i18n-t keypath="Contact us" tag="p" class="flex">
                            <a
                                class="font-semibold underline cursor-pointer"
                                :href="'mailto:' + contactEmail"
                                :class="'text-primary-500 dark:text-primary-500'"
                                >{{ contactEmail }}</a
                            >
                        </i18n-t>
                    </div>
                </div>
            </div>
            <TheDiscordButton />
            <!-- <div class="block text-sm text-gray-500 text-center justify-end items-end dark:text-gray-400">
                <i18n-t keypath="Contact us" tag="p" class="flex">
                    <a
                        class="font-semibold underline cursor-pointer"
                        :href="'mailto:' + contactEmail"
                        :class="'text-primary-500 dark:text-primary-500'"
                        >{{ contactEmail }}</a
                    >
                </i18n-t>
            </div> -->
        </div>
    </footer>
</template>

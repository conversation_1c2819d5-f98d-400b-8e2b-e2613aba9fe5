<script setup lang="ts">
const { toggleDashboardSearch, isDashboardSearchModalOpen } = useUIState()
const authStore = useAuthStore()
const config = useRuntimeConfig()
const { user, isSuperUser, isUsingPremiumPlan } = storeToRefs(authStore)
const { t } = useI18n()
const features = config.public.features

const changeMenu = (link: string) => {
    // save the current menu to local storage
    localStorage.setItem('currentMenu', link)
    navigateTo(link)
}

const links = computed(() => {
    return [
        {
            id: 'text',
            label: t('Text'),
            icon: 'i-material-symbols-translate',
            to: '/',
            tooltip: {
                text: t('Player'),
            },
            click: () => changeMenu('/'),
        },
        {
            id: 'text pro',
            label: t('Emotion Text'),
            icon: 'fluent:emoji-sparkle-16-regular',
            to: '/emotion-text',
            show: features.textToSpeechPro || isSuperUser.value,
            click: () => changeMenu('/emotion-text'),
        },
        {
            label: t('Story Maker'),
            icon: 'i-fluent:people-chat-24-regular',
            to: '/story-maker',
            show: features.storyMaker || isSuperUser.value,
            click: () => changeMenu('/story-maker'),
        },
        {
            id: 'documents',
            label: t('Documents'),
            icon: 'i-oui:documents',
            to: '/documents',
            tooltip: {
                text: t('Documents'),
            },
            click: () => changeMenu('/documents'),
        },
        {
            id: 'history',
            label: t('History'),
            icon: 'i-material-symbols:history',
            to: '/history',
            badge: '4',
            tooltip: {
                text: t('History'),
            },
        },
        {
            id: 'api',
            label: t('API'),
            to: user.value ? '/profile/integration/' : config.public.NUXT_DOCS_URL,
            icon: 'i-material-symbols-api',
        },
        // {
        //     id: 'voice-engine',
        //     label: t('Voice Engine'),
        //     icon: 'i-fluent-person-voice-20-regular',
        //     to: '/voice-engine',
        //     tooltip: {
        //         text: t('Voice Engine'),
        //     },
        // },
        {
            id: 'pricing',
            label: t('Pricing Plans'),
            icon: 'i-solar:documents-linear',
            to: '/pricing',
            tooltip: {
                text: t('Price Plans'),
            },
        },
        {
            id: 'privacy',
            label: t('Privacy Policy'),
            icon: 'material-symbols:privacy-tip-outline-rounded',
            to: '/privacy',
            tooltip: {
                text: t('Privacy Policy'),
            },
        },
        {
            id: 'terms',
            label: t('Terms of Service'),
            icon: 'mdi:file-document-check-outline',
            to: '/terms',
            tooltip: {
                text: t('Terms of Service'),
            },
        },
        {
            id: 'faq',
            label: t('Frequently asked questions'),
            icon: 'i-mdi:faq',
            to: '/pricing#faq',
            tooltip: {
                text: t('Frequently asked questions'),
            },
        },
    ]
})

const groups = computed(() => [
    {
        key: 'links',
        label: t('Go to'),
        commands: links.value.map((link) => ({ ...link })),
    },
    {
        key: 'external-links',
        label: t('Other Services'),
        commands: [
            {
                id: 'tranlsate',
                label: t('Translate'),
                icon: 'i-material-symbols:g-translate',
                to: 'https://doctransgpt.com',
                external: true,
                target: '_blank',
            },
        ],
    },
    {
        key: 'help',
        label: t('Help'),
        commands: [
            {
                id: 'manual',
                label: t('Guide'),
                icon: 'i-logos-youtube-icon',
                to: 'https://www.youtube.com/watch?v=OdDdyu2k20A&list=PL8DoYyQx5K5GRv_4q5zL7rCKsNEtXH8dN',
                external: true,
                target: '_blank',
            },
        ],
    },
])
</script>

<template>
    <ClientOnly>
        <UButton
            variant="ghost"
            color="white"
            size="lg"
            icon="i-material-symbols-menu"
            @click="toggleDashboardSearch"
        />
        <LazyUDashboardSearch :groups="groups" />
    </ClientOnly>
</template>

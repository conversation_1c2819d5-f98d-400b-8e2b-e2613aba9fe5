<script setup lang="ts">
const translateStore = useTranslateStore()
const { loadings, showAccountVerifyWarning } = storeToRefs(translateStore)
const { t } = useI18n()
const route = useRoute()
const authStore = useAuthStore()
const { isLoggingIn, user, isOpen } = storeToRefs(authStore)
const links = computed(() => [
    {
        label: t('Player'),
        to: '/',
        icon: 'i-material-symbols-translate',
        active: route.name === 'index' || route.name === 'documents',
    },
    {
        label: t('History'),
        to: '/history',
        icon: 'i-material-symbols-history',
    },
    {
        label: t('Playlists'),
        to: '/playlist',
        icon: 'i-solar-playlist-linear',
    },
])

onMounted(() => {
    if (!user.value) {
        authStore.fetchUserInfo()
    }
})

const { isResendingActivation, showBetaNotification } = storeToRefs(authStore)

const resendDone = ref(false)
const resendableCountdown = ref(15)
const onResendVerifyEmail = async () => {
    resendableCountdown.value = 15
    await authStore.resendVerifyEmail()
    resendDone.value = true
    const timer = setInterval(() => {
        resendableCountdown.value--
        if (resendableCountdown.value <= 0) {
            clearInterval(timer)
            resendDone.value = false
        }
    }, 1000)
}
</script>

<template>
    <div v-if="showBetaNotification" class="bg-primary-400 dark:bg-primary-600 p-4">
        <div
            class="relative max-w-screen-xl mx-auto flex md:flex-row flex-col md:justify-center justify-start md:items-center items-start gap-1 text-sm"
        >
            <div class="font-semibold whitespace-nowrap flex flex-row gap-1 items-center">
                <UIcon name="i-grommet-icons:test" class="text-lg" />
                <div>{{ $t('Beta Version !') }}</div>
            </div>
            <div>
                {{ $t('This is a beta version of the system. Please report any issues to the support team.') }}
            </div>
            <UButton
                icon="i-heroicons-x-mark-20-solid"
                size="sm"
                color="white"
                square
                variant="ghost"
                class="absolute md:right-8 right-0 -top-2"
                @click="showBetaNotification = false"
            />
        </div>
        <div
            v-if="showAccountVerifyWarning"
            class="text-sm text-center mt-0.5 flex flex-row md:justify-center justify-start md:items-center items-start gap-3"
        >
            <div>
                {{
                    $t(
                        'Please verify your account. If you did not receive the verification email, please click the button below to resend the verification email.'
                    )
                }}
            </div>
            <button
                @click="onResendVerifyEmail"
                type="button"
                class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                :disabled="resendDone"
            >
                <span v-if="isResendingActivation">
                    {{ $t('Sending...') }}
                </span>
                <span v-else>
                    {{
                        resendDone
                            ? $t('Resendable in {second} seconds', { second: resendableCountdown })
                            : $t('Resend verify email')
                    }}
                </span>

                <svg
                    v-if="!resendDone && !isResendingActivation"
                    aria-hidden="true"
                    class="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
            </button>
        </div>
    </div>
    <UHeader
        :links="links"
        :ui="{
            button: {
                base: 'hidden',
            },
            wrapper: 'z-30',
        }"
    >
        <template #left>
            <div class="flex items-center space-x-3 flex-row">
                <!-- <a
                    id="drawer-menu"
                    class="inline-flex justify-center p-3 text-gray-500 rounded-full cursor-pointer dark:text-gray-400 dark:hover:text-white hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer"
                    @click="appStore.setShowAppDrawer(true)"
                >
                    <svg
                        class="w-5 h-5 text-gray-500 dark:text-white"
                        aria-hidden="true"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="currentColor"
                        viewBox="0 0 17 14"
                    >
                        <path
                            d="M16 2H1a1 1 0 0 1 0-2h15a1 1 0 1 1 0 2Zm0 6H1a1 1 0 0 1 0-2h15a1 1 0 1 1 0 2Zm0 6H1a1 1 0 0 1 0-2h15a1 1 0 0 1 0 2Z"
                        />
                    </svg>
                </a> -->
                <TheMenu />
                <BaseSoundWave :static="!loadings['createSpeech']" class="cursor-pointer" @click="navigateTo('/')" />
                <span
                    class="text-sm truncate md:text-lg font-normal hidden md:block cursor-pointer"
                    @click="navigateTo('/')"
                >
                    Text To Speech OpenAI
                </span>
            </div>
        </template>

        <template #right>
            <ClientOnly>
                <div class="flex flex-row items-center mt-0">
                    <UColorModeButton class="hidden md:block" />

                    <Languages />
                    <div id="notification-icon" v-if="user" class="mr-3.5">
                        <NotificationBar />
                    </div>
                    <UButton
                        v-if="isLoggingIn"
                        class="ml-2"
                        icon="eos-icons:loading"
                        size="md"
                        color="gray"
                        square
                        variant="solid"
                    />
                    <template v-else>
                        <USlideover v-model="isOpen">
                            <div class="py-6 md:px-6 px-4"><ProfileCard /></div>
                        </USlideover>
                        <div v-if="user" @click="isOpen = true">
                            <UAvatar
                                :alt="user.full_name"
                                size="md"
                                :ui="{ background: 'bg-gray-300 dark:bg-gray-700' }"
                            />
                        </div>

                        <UButton
                            v-else
                            :label="$t('Sign In')"
                            color="primary"
                            variant="solid"
                            trailing-icon="i-heroicons-arrow-right-20-solid"
                            class="ml-2 mb-0"
                            to="/signin"
                        />
                    </template>
                </div>
            </ClientOnly>
        </template>
    </UHeader>
</template>

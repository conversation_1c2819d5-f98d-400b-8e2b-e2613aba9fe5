<template>
    <UHorizontalNavigation
        :ui="{
            base: 'flex-1 sm:flex-none',
            inner: 'w-full sm:w-fit flex items-center justify-between',
            container: 'flex justify-between sm:justify-start w-full sm:min-w-0',
        }"
        :links="links"
        class="border-b border-gray-200 dark:border-gray-800 px-2"
    >
        <template #default="{ link }">
            <span class="relative text-sm truncate hidden sm:block hover:text-primary-500">{{ link.label }}</span>
        </template>
        <template #icon="{ link }">
            <div class="w-full sm:w-fit flex flex-col truncate items-center justify-center">
                <div class="flex items-start gap-1">
                    <UIcon :name="link.icon" class="w-7 h-7 sm:w-5 sm:h-5" />
                    <UBadge class="block sm:hidden relative" variant="solid" v-if="link.badge" size="xs">{{
                        link.badge?.label
                    }}</UBadge>
                </div>
                <span class="relative truncate text-xs sm:hidden hover:text-primary-500">{{ link.label }}</span>
            </div>
        </template>
        <template #badge="{ link }">
            <UBadge class="hidden sm:block relative" variant="subtle" v-if="link.badge?.label" size="xs">
                <div class="flex items-center gap-1">
                    <div class="text-xs">
                        {{ link.badge?.label }}
                    </div>
                </div>
            </UBadge>
        </template>
    </UHorizontalNavigation>
</template>

<script lang="ts" setup>
const { t } = useI18n()
const config = useRuntimeConfig()
const features = config.public.features
const authStore = useAuthStore()
const { isSuperUser, isUsingPremiumPlan } = storeToRefs(authStore)

const changeMenu = (link: string) => {
    // save the current menu to local storage
    localStorage.setItem('currentMenu', link)
}

const links = computed(() => {
    return [
        {
            label: t('Input Text'),
            icon: 'i-material-symbols-translate',
            to: '/',
            show: true,
            click: () => changeMenu('/'),
        },
        {
            label: t('Emotion Text'),
            icon: 'fluent:emoji-sparkle-16-regular',
            to: '/emotion-text',
            show: features.textToSpeechPro || isSuperUser.value,
            click: () => changeMenu('/emotion-text'),
        },
        {
            label: t('Story Maker'),
            icon: 'i-fluent:people-chat-24-regular',
            to: '/story-maker',
            show: features.storyMaker || isSuperUser.value,
            click: () => changeMenu('/story-maker'),
        },
        {
            label: t('Document'),
            icon: 'i-mdi-file-document-outline',
            to: '/documents',
            show: true,
            click: () => changeMenu('/documents'),
        },
    ].filter((item) => item.show)
})
</script>

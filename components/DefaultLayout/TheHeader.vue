<script setup lang="ts">
const props = defineProps({
    fixed: {
        type: Boolean,
        default: false,
    },
})
const config = useRuntimeConfig()
const features = config.public.features
const translateStore = useTranslateStore()
const { loadings, showAccountVerifyWarning } = storeToRefs(translateStore)
const { t } = useI18n()
const route = useRoute()
const authStore = useAuthStore()
const { isLoggingIn, user, isOpen, isSuperUser } = storeToRefs(authStore)
const links = computed(() => {
    return [
        {
            label: t('Vocalize'),
            icon: 'i-material-symbols-translate',
            active: route.name === 'index' || route.name === 'documents' || route.name === 'story-maker',
            click: () => {
                // get current menu from local storage
                const currentMenu = localStorage.getItem('currentMenu')
                navigateTo(currentMenu || '/')
            },
        },
        {
            label: t('History'),
            to: '/history',
            icon: 'i-material-symbols-history',
        },
        {
            label: t('Voice Library'),
            to: '/voice-library',
            icon: 'i-solar-music-library-2-broken',
        },
        {
            label: t('My Voices'),
            to: '/my-voice',
            icon: 'i-solar-music-library-2-broken',
            hide: !(features.myVoice || isSuperUser.value),
        },
        {
            label: t('API'),
            to: user.value ? '/profile/integration/' : config.public.NUXT_DOCS_URL,
            icon: 'i-material-symbols-api',
            hide: !(features.userAPI || isSuperUser.value),
        },
        {
            label: t('Translate'),
            to: 'https://doctransgpt.com',
            icon: 'i-fluent-person-voice-20-regular',
            external: true,
            target: '_blank',
            prefetchedClass: 'external-link',
        },
    ].filter((item) => !item.hide)
})

onMounted(() => {
    authStore.fetchUserInfo()
})
// useAuthReload()

const { isResendingActivation, showBetaNotification } = storeToRefs(authStore)

const resendDone = ref(false)
const resendableCountdown = ref(15)
const onResendVerifyEmail = async () => {
    resendableCountdown.value = 15
    await authStore.resendVerifyEmail()
    resendDone.value = true
    const timer = setInterval(() => {
        resendableCountdown.value--
        if (resendableCountdown.value <= 0) {
            clearInterval(timer)
            resendDone.value = false
        }
    }, 1000)
}

watch(
    () => user.value,
    (value) => {
        if (value && value?.email) {
        } else {
        }
    }
)

onMounted(() => {
    if (user.value && user.value?.email) {
    } else {
    }

    //authStore.refreshToken();
})
</script>

<template>
    <div v-if="showAccountVerifyWarning" class="bg-primary-400 dark:bg-primary-600 p-4">
        <div
            v-if="showAccountVerifyWarning"
            class="text-sm text-center mt-0.5 flex flex-row md:justify-center justify-start md:items-center items-start gap-3"
        >
            <div>
                {{
                    $t(
                        'Please verify your account. If you did not receive the verification email, please click the button below to resend the verification email.'
                    )
                }}
            </div>
            <button
                @click="onResendVerifyEmail"
                type="button"
                class="text-white bg-yellow-800 hover:bg-yellow-900 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-xs px-3 py-1.5 mr-2 text-center inline-flex items-center dark:bg-yellow-300 dark:text-gray-800 dark:hover:bg-yellow-400 dark:focus:ring-yellow-800"
                :disabled="resendDone"
            >
                <span v-if="isResendingActivation">
                    {{ $t('Sending...') }}
                </span>
                <span v-else>
                    {{
                        resendDone
                            ? $t('Resendable in {second} seconds', { second: resendableCountdown })
                            : $t('Resend verify email')
                    }}
                </span>

                <svg
                    v-if="!resendDone && !isResendingActivation"
                    aria-hidden="true"
                    class="ml-2 h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="1.5"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    ></path>
                </svg>
            </button>
        </div>
    </div>
    <UHeader
        class="w-full bg-background/60 backdrop-blur border-gray-200 dark:border-gray-800"
        :class="{
            fixed: fixed && !showAccountVerifyWarning,
        }"
        :links="links"
        :ui="{
            button: {
                base: 'hidden',
            },
            wrapper: 'z-30',
            center: 'main-menu',
        }"
    >
        <template #left>
            <div class="flex items-center space-x-3 flex-row">
                <TheMenu />
                <BaseSoundWave :static="!loadings['createSpeech']" class="cursor-pointer" @click="navigateTo('/')" />
                <span
                    class="text-sm truncate md:text-lg font-normal hidden md:block cursor-pointer"
                    @click="navigateTo('/')"
                >
                    Text To Speech OpenAI
                </span>
            </div>
        </template>

        <template #right>
            <ClientOnly>
                <div class="flex flex-row items-center mt-0">
                    <PopupMobileApp class="mr-0.5"/>
                    <TheDiscordButton class="mr-0.5 md:hidden"/>
                    <UColorModeButton class="hidden md:block mt-1.5" />

                    <Languages />
                    <!-- <div id="notification-icon" v-if="user">
            <NotificationBar />
          </div> -->
                    <div v-if="user" class="mr-3.5">
                        <NotificationBarSupabase />
                    </div>
                    <UButton
                        v-if="isLoggingIn"
                        class="ml-2"
                        icon="eos-icons:loading"
                        size="md"
                        color="gray"
                        square
                        variant="solid"
                    />
                    <div v-else>
                        <USlideover v-model="isOpen">
                            <div class="py-6 md:px-6 px-4 overflow-auto scrollbar-none">
                                <ProfileCard showCloseButton />
                            </div>
                        </USlideover>
                        <div v-if="user" @click="isOpen = true" class="group cursor-pointer">
                            <UAvatar
                                :alt="user.full_name"
                                size="md"
                                :ui="{ background: 'bg-gray-300 dark:bg-gray-700' }"
                                class="group-hover:scale-125 transition-all duration-200"
                            />
                        </div>

                        <UButton
                            v-else
                            :label="$t('Sign In')"
                            color="primary"
                            variant="solid"
                            trailing-icon="i-heroicons-arrow-right-20-solid"
                            class="ml-2 mb-0"
                            to="/signin"
                            size="xs"
                        />
                    </div>
                </div>
            </ClientOnly>
        </template>
    </UHeader>
</template>

<style lang="scss">
.main-menu {
    li:last-child {
        position: relative;
        .iconify {
            right: 2px;
            top: 10px !important;
        }
    }
}
</style>

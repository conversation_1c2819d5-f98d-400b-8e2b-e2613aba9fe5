<script setup lang="ts">
import type { NavItem } from "@nuxt/content";

const navigation = inject<NavItem[]>("navigation", []);

const { header } = useAppConfig();
</script>

<template>
  <UHeader
    :ui="{
      wrapper: 'border-b !lg:border-gray-200 dark:border-gray-800',
    }"
  >
    <template #logo>
      <div class="flex items-center space-x-3 flex-row">
        <BaseSoundWave
          :static="true"
          class="cursor-pointer"
          @click="navigateTo('/docs/api-reference/getting-started')"
        />
        <span
          class="text-sm truncate md:text-lg font-normal hidden md:block cursor-pointer"
          @click="navigateTo('/docs/api-reference/getting-started')"
        >
          Text To Speech OpenAI <UBadge label="Docs" variant="subtle" class="mb-0.5" />
        </span>
      </div>
    </template>

    <template v-if="header?.search" #center>
      <UContentSearchButton class="hidden lg:flex" />
    </template>

    <template #right>
      <UContentSearchButton v-if="header?.search" :label="null" class="lg:hidden" />

      <UColorModeButton v-if="header?.colorMode" />

      <template v-if="header?.links">
        <UButton
          v-for="(link, index) of header.links"
          :key="index"
          v-bind="{ color: 'gray', variant: 'ghost', ...link }"
        />
      </template>
    </template>

    <template #panel>
      <UNavigationTree :links="mapContentNavigation(navigation)" />
    </template>
  </UHeader>
</template>

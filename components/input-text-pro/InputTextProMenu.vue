<template>
    <div
        class="px-4 py-2 border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900 grid grid-cols-3 sm:grid-cols-none sm:flex sm:flex-row gap-2 items-center sm:justify-start w-full sm:min-w-0"
    >
        <div class="text-xs font-semibold hidden md:flex flex-row gap-1 items-center">
            <UIcon name="material-symbols:settings-suggest" class="text-md" />
            {{ $t('Advanced Options:') }}
        </div>
        <UTooltip class="w-full sm:w-fit" :text="$t('Emotion')" :popper="{ placement: 'top' }">
            <BaseShadesSelect v-model="emotion" :width="textareaWidth" />
        </UTooltip>
        <UTooltip class="w-full sm:w-fit" :text="$t('Vibes')" :popper="{ placement: 'top' }">
            <BaseVibesSelect v-model="vibe" :width="textareaWidth" />
        </UTooltip>
        <UTooltip class="w-full sm:w-fit" :text="$t('Custom Prompt')" :popper="{ placement: 'top' }">
            <BaseCustomPromptSelect v-model="custom_prompt" />
        </UTooltip>

        <!-- <UTooltip class="w-full sm:w-fit" :text="$t('Audio Model')" :popper="{ placement: 'top' }">
            <BaseModelSelect v-model="model" />
        </UTooltip> -->
    </div>
</template>

<script lang="ts" setup>
const inputTextProStore = useInputTextProStore()

const { emotion, vibe, custom_prompt, textareaWidth } = storeToRefs(inputTextProStore)
</script>

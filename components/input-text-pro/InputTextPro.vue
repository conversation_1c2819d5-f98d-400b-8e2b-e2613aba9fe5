<template>
    <div class="h-full pb-9">
        <InputTextProMenu />
        <!-- <div class="text-xs text-yellow-500 dark:text-yellow-400 px-4 py-1 border-b border-gray-200 dark:border-gray-800 bg-yellow-50 dark:bg-yellow-900">
            {{$t("This feature is still in beta. There is a fail rate of 5-10%. Please use it with caution and try to remove some complicated emotion or custom prompt if the result is not as expected.")}}
        </div> -->
        <p v-if="errors['createSpeech']" class="text-red-500 p-4 text-sm">
            <div class="flex flex-row gap-2 items-center">
               <div>
                {{ $t('Error') }}: {{ $t(errors['createSpeech'] || '') }}
               </div>
            <UButton
            icon="i-carbon-close-outline"
            size="xs"
            color="gray"
            square
            :padded="false"
            variant="link"
            @click="errors['createSpeech'] = ''"
          />
            </div>
            <div v-if="errors['createSpeech'] === 'NOT_VERIFY_ACCOUNT'" class="text-left mt-2">
                <UButton
                icon="i-ic-baseline-plus"
                size="xs"
                color="gray"
                variant="solid"
                :label="$t('Resend verify email')"
                :trailing="false"
                @click="authStore.resendVerifyEmail()"
              />
            </div>
            <div v-if="['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(errors['createSpeech'])" class="text-left mt-2">
                <UButton
                icon="i-ic-baseline-plus"
                size="xs"
                color="gray"
                variant="solid"
                :label="$t('Buy more credits')"
                :trailing="false"
                @click="navigateTo('/profile/buy-credits')"
              />
            </div>
           
        </p>
        <textarea
            v-else
            ref="textareaRef"
            class="scrollbar-thin w-full pb-10 px-3 md:pl-4 md:pr-2 resize-none overflow-y-auto h-[calc(100%-91px)] min-h-[280px] text-sm text-gray-900 bg-white border-0 dark:bg-gray-900 focus:ring-0 dark:text-white dark:placeholder-gray-500 placeholder-gray-300"
            required
            :placeholder="$t('Enter the text you want to convert to speech here.')"
            v-model="input"
            :maxlength="maxLengthTextPro"
        >
        </textarea>
        <div 
        v-if="!isUsingPremiumPlan"
        @click="navigateTo('/pricing')"
        class="cursor-pointer hover:underline hover:text-red-600 text-sm text-red-500 dark:text-red-400 px-4 py-1 border-b border-gray-200 dark:border-gray-800 bg-red-50 dark:bg-red-900">
            {{$t("Top up credits for more stable and higher-quality Emotion Text processing!")}}       
        </div>
        <div
            v-if="input"
            class="absolute bottom-0 pb-3 pt-1 px-3 bg-white/65 dark:bg-slate-900 rounded-s-md right-3 text-right text-sm font-thin dark:text-gray-300 flex flex-row space-x-4 items-center"
        >
            <div>{{ input?.length }} / {{ maxLengthTextPro }}</div>
            <div @click="inputTextProStore.clearInput()">
                <UButton :padded="false" color="red" variant="link" :label="$t('Clear text')" />
            </div>
        </div>
       
    </div>
</template>

<script lang="ts" setup>
const inputTextProStore = useInputTextProStore()

const { input, textareaRef, errors } = storeToRefs(inputTextProStore)

const authStore = useAuthStore()
const { maxLengthTextPro, isUsingPremiumPlan } = storeToRefs(authStore)
</script>

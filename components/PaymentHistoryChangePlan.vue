<template>
    <li class="mb-10 ml-6">
        <Icon
            :name="displayStatus?.icon || ''"
            class="absolute flex items-center justify-center w-6 h-6 -left-3 ring-2 ring-transparent"
            :class="displayStatus?.color"
        />
        <div class="flex flex-inline items-center justify-between">
            <h3 class="flexmb-1 text-lg font-semibold text-gray-900 dark:text-white">
                {{ $t(props.order_product.id) }}
                <span
                    class="bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 ml-2"
                >
                    {{ $t('Plan change') }}
                </span>
                <span
                    v-if="displayStatus"
                    :class="displayStatus.class"
                >
                    {{ displayStatus.label }}
                </span>
            </h3>
            <div v-if="props.amount_divide_100 && props.isSuccess" class="text-red-400 text-lg">
                - {{ props.amount_divide_100 }}$
            </div>
            <div v-else class="text-gray-400 text-lg">{{ props.amount_divide_100 }}$</div>
        </div>
        <time class="block mb-2 text-sm font-normal leading-none text-gray-400 dark:text-gray-500">{{
            createdAtRawFormat
        }}</time>
        <p class="mb-1 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('Order ID: ') }}{{ props.uuid }}</p>
        <p class="mb-4 text-sm font-normal text-gray-500 dark:text-gray-400">{{ $t('Paypal ID: ') }}{{ props.external_order_id }}</p>
    </li>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import { usePaymentsStore } from '~/stores/payments'
const paymentsStore = usePaymentsStore()
const { tokenUnit } = storeToRefs(paymentsStore)

const { $abbreviatedUnit } = useNuxtApp()
dayjs.extend(utc)
dayjs.extend(relativeTime)

const props = defineProps<{
    id: number
    uuid: string
    user_id: string
    product_id: string
    status: string
    amount: number
    amount_divide_100: number
    quantity: number
    type: string
    payment_method: string
    created_at: string
    updated_at: string
    isSuccess?: boolean
    order_product: {
        id: string
        name: string
        type: string
    }
    external_order_id: string
}>()

const createdAtRawFormat = computed(() => dayjs.unix(dayjs.utc(props.created_at).unix()).format('YYYY-MM-DD HH:mm:ss'))

const displayStatus = computed(() => {
    return paymentStatus(useI18n(), props.status)
})

</script>

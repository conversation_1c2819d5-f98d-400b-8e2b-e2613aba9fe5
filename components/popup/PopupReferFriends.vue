<script setup lang="ts">
import dayjs from 'dayjs'
import QrcodeVue from 'qrcode.vue'

const appStore = useAppStore()
const { showPopupReferral } = storeToRefs(appStore)
const authStore = useAuthStore()
const { user, loadings, errors, isSuperUser } = storeToRefs(authStore)
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig()

onMounted(() => {
    if (!user.value || !(runtimeConfig.public.features.referral || isSuperUser.value)) {
        return
    }
    const lastSeen = localStorage.getItem('lastSeenPopupReferFriends')
    // check if last seen is more than 3 days ago
    const isMoreThan3Days = dayjs().diff(dayjs(lastSeen), 'day') > 3
    if (isMoreThan3Days || !lastSeen) {
        showPopupReferral.value = true
        localStorage.setItem('lastSeenPopupReferFriends', dayjs().toISOString())
    }
})

const copyToClipboard = async () => {
    try {
        await navigator.clipboard.writeText(user.value?.referral_link || '')
        const toast = useToast()
        toast.add({
            title: t('Success'),
            description: t('Link copied to clipboard'),
            color: 'green',
            icon: 'i-heroicons-check-circle-20-solid',
            timeout: 2000,
        })
    } catch (err) {
        console.error('Failed to copy:', err)
    }
}

const isShowQRCode = ref(false)
const toggleQRCode = () => {
    isShowQRCode.value = !isShowQRCode.value
}

const socialShareNetworks = computed(() => {
    return [
        'facebook',
        'x',
        'linkedin',
        'whatsapp',
        'telegram',
        'email',
        'pinterest',
        'reddit',
        'threads',
        'skype',
        'line',
        'viber',
        'bluesky',
        'pocket',
    ]
})
</script>

<template>
    <UModal v-model="showPopupReferral" :ui="{ width: 'sm:max-w-3xl' }">
        <div class="absolute -top-3 -right-3 z-20 bg-gray-100 dark:bg-gray-800 p-1 rounded-full">
            <UButton
                color="white"
                variant="solid"
                icon="i-heroicons-x-mark-20-solid"
                size="xl"
                @click="showPopupReferral = false"
            />
        </div>

        <div class="p-4">
            <ULandingCard :title="$t('Refer friends')" icon="i-mynaui-share" color="primary" orientation="horizontal">
                <QrcodeVue
                    v-if="user?.referral_link && isShowQRCode"
                    class="w-full animate__animated animate__bounceIn"
                    :size="270"
                    :value="user?.referral_link"
                    level="H"
                    render-as="svg"
                />

                <img
                    v-else
                    src="/assets/images/rb_7039.png"
                    class="w-full rounded-md animate__animated animate__flipInY"
                />

                <template #description>
                    <div class="text-sm text-gray-500 dark:text-gray-400 mt-4">
                        <p>
                            {{
                                $t(
                                    'Invite your friends to use our service and get rewards. Share your referral link to your friends and get rewards when they sign up and make a purchase. Earn up to 5% for each completed purchase.'
                                )
                            }}
                        </p>
                        <div v-if="user?.referral_link" class="flex flex-col gap-4 mt-4">
                            <UInput :modelValue="user?.referral_link" readonly />
                            <div class="flex flex-row gap-2 items-center justify-center sm:justify-start">
                                <UButton
                                    icon="i-hugeicons-copy-link"
                                    size="sm"
                                    color="primary"
                                    variant="solid"
                                    class="w-fit"
                                    :label="$t('Copy link')"
                                    :trailing="false"
                                    @click="copyToClipboard"
                                    block
                                />
                                <UButton
                                    icon="i-f7-qrcode-viewfinder"
                                    size="sm"
                                    color="white"
                                    variant="solid"
                                    class="w-fit"
                                    :label="isShowQRCode ? $t('Hide QR Code') : $t('Show QR Code')"
                                    :trailing="true"
                                    @click="toggleQRCode"
                                    block
                                />
                            </div>
                        </div>
                        <UButton
                            v-else
                            class="mt-4"
                            icon="i-ic-twotone-add-link"
                            size="sm"
                            color="primary"
                            variant="solid"
                            :label="$t('Get your referral link')"
                            :trailing="false"
                            :loading="loadings.getReferralLink"
                            @click="authStore.getReferralLink"
                        />
                    </div>
                </template>
            </ULandingCard>
            <UAlert
                v-if="errors.getReferralLink"
                class="mt-4"
                icon="i-ic-round-warning"
                :description="
                    $t(
                        'Make sure you close AdBlocker or any other ad-blocking extensions to see the referral link, or try open the link in incognito mode.'
                    )
                "
                :title="$t('Error has occurred')"
                color="red"
                variant="subtle"
            />
            <div v-if="user?.referral_link" class="flex flex-col w-full mt-4 gap-2 justify-center">
                <UDivider :label="$t('Share to social networks')" />
                <div class="flex flex-row gap-1 mt-0 flex-wrap justify-center">
                    <SocialShare
                        v-for="network in socialShareNetworks"
                        :network="network"
                        :url="user?.referral_link"
                        :title="
                            $t(
                                'TTS OpenAI is a platform that provides AI services for your business needs. Try it now!'
                            )
                        "
                        image="https://ttsopenai.com/assets/images/mobile-app.jpg"
                        :styled="true"
                        :label="false"
                        class="text-gray-50 dark:text-gray-100"
                    />
                </div>
            </div>
        </div>
    </UModal>
</template>

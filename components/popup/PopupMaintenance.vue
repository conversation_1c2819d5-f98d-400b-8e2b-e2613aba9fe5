<script setup lang="ts">
import dayjs from "dayjs";
import type { MaintenanceNotification } from "~/types";
import { formatDatetime } from "~/utils";

const open = ref(false);
const currentMaintenance = ref<MaintenanceNotification | null>(null);
const client = useSupabaseClient();

const fetchMaintenanceNotifications = async (): Promise<MaintenanceNotification[]> => {
  try {
    const now = dayjs().toISOString();

    // Fetch maintenance notifications where:
    // - display_start <= now (notification should be shown)
    // - maintain_start >= now (maintenance hasn't started yet)
    const { data, error } = await client
      .from('notification_maintenances')
      .select('*')
      .lte('display_start', now)
      .gte('maintain_start', now)
      .order('display_start', { ascending: false });

    if (error) {
      console.error('Error fetching maintenance notifications:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching maintenance notifications:', error);
    return [];
  }
};

const getSeenMaintenanceIds = (): number[] => {
  const seenIds = localStorage.getItem("seenMaintenanceIds");
  return seenIds ? JSON.parse(seenIds) : [];
};

const markMaintenanceAsSeen = (id: number) => {
  const seenIds = getSeenMaintenanceIds();
  if (!seenIds.includes(id)) {
    seenIds.push(id);
    localStorage.setItem("seenMaintenanceIds", JSON.stringify(seenIds));
  }
};

onMounted(async () => {
  const maintenanceNotifications = await fetchMaintenanceNotifications();
  const seenIds = getSeenMaintenanceIds();

  // Find the first unseen maintenance notification
  const unseenMaintenance = maintenanceNotifications.find(
    notification => !seenIds.includes(notification.id)
  );

  if (unseenMaintenance) {
    currentMaintenance.value = unseenMaintenance;
    open.value = true;
  }
});

const onGotIt = () => {
  if (currentMaintenance.value) {
    markMaintenanceAsSeen(currentMaintenance.value.id);
  }
  open.value = false;
};
</script>

<template>
  <UDashboardModal
    v-if="currentMaintenance"
    preventClose
    v-model="open"
    :title="currentMaintenance.title || $t('Maintenance notice')"
    :ui="{
      icon: { base: 'text-primary-500 dark:text-primary-400' } as any,
      footer: { base: 'ml-16' } as any
    }"
  >
    <div class="space-y-4">
      <p class="text-gray-600 dark:text-gray-300">
        {{ $t('maintenance_notification_content') }}
      </p>

      <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-2">
        <div class="flex justify-between items-center">
          <span class="font-medium text-sm text-gray-700 dark:text-gray-300">
            {{ $t('Maintenance Start') }}:
          </span>
          <span class="text-sm text-gray-900 dark:text-white">
            {{ formatDatetime(currentMaintenance.maintain_start, 'YYYY/MM/DD HH:mm') }}
          </span>
        </div>

        <div class="flex justify-between items-center">
          <span class="font-medium text-sm text-gray-700 dark:text-gray-300">
            {{ $t('Maintenance End') }}:
          </span>
          <span class="text-sm text-gray-900 dark:text-white">
            {{ formatDatetime(currentMaintenance.maintain_end, 'YYYY/MM/DD HH:mm') }}
          </span>
        </div>
      </div>

      <p class="text-sm text-gray-500 dark:text-gray-400">
        {{ $t('Please arrange your activities accordingly to avoid any disruption.') }}
      </p>
    </div>

    <template #footer>
      <UButton
        icon="i-fluent-emoji-high-contrast-ok-hand"
        class="ml-auto"
        color="white"
        :label="$t('Got it, don’t show again')"
        @click="onGotIt"
      />
    </template>
  </UDashboardModal>
</template>

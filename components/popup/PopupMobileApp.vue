<script setup lang="ts">
// import QrcodeVue from 'qrcode.vue'
import MobileQR from '~/assets/svgs/qr-mobile.svg'
const { isDesktop, isApple } = useDevice()
const appStore = useAppStore()
const { showPopupMobileApp, appleStoreLink, googlePlayLink } = storeToRefs(appStore)
const onMobileAppClick = () => {
    if (isDesktop) {
        showPopupMobileApp.value = true
    } else {
        if (isApple) {
            window.open(appleStoreLink.value, '_blank')
        } else {
            window.open(googlePlayLink.value, '_blank')
        }
    }
}

const linkQrCode = computed(() => {
    return window.location.origin + '/public/mobile-app'
})

const onManualClick = (os: 'apple' | 'google') => {
    if (os === 'apple') {
        window.open(appleStoreLink.value, '_blank')
    } else {
        window.open(googlePlayLink.value, '_blank')
    }
}
</script>

<template>
    <UPopover
        v-model="showPopupMobileApp"
        :ui="{
            ring: 'ring-yellow-500 dark:ring-yellow-400',
        }"
    >
        <UButton
            icon="fluent-emoji-flat:mobile-phone"
            size="xs"
            color="yellow"
            variant="outline"
            :trailing="false"
            @click="onMobileAppClick"
            :label="$t('Get App')"
            class="hidden sm:flex"
        >
        </UButton>
        <UButton
            icon="fluent-emoji-flat:mobile-phone"
            size="xs"
            color="yellow"
            variant="ghost"
            :trailing="false"
            @click="onMobileAppClick"
            class="flex sm:hidden"
            padded
            >
        </UButton>

        <template #panel>
            <div class="p-4">
                <div class="px-4">
                    <!-- <QrcodeVue
                        class="w-full animate__animated animate__bounceIn"
                        :size="270"
                        :value="linkQrCode"
                        level="H"
                        render-as="svg"
                    /> -->

                    <img
                        :src="`/assets/images/qrcode.png`"
                        alt="qr-code"
                        class="w-full max-w-72 mx-auto animate__animated animate__bounceIn"
                    />
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400 text-center mt-4">
                    {{ $t('Scan the QR code to download the app') }}
                </div>
                <UDivider label="OR" class="py-1" />
                <div class="flex flex-col gap-2 w-full mt-1">
                    <UButton
                        icon="logos:apple-app-store"
                        color="white"
                        variant="solid"
                        :label="$t('Get it on App Store')"
                        :trailing="false"
                        @click="onManualClick('apple')"
                        block
                    />
                    <UButton
                        icon="logos:google-play-icon"
                        color="white"
                        variant="solid"
                        :label="$t('Get it on Google Play')"
                        :trailing="false"
                        @click="onManualClick('google')"
                        block
                    />
                </div>
            </div>
        </template>
    </UPopover>
</template>

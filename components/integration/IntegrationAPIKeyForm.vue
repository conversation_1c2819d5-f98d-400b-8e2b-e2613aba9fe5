<template>
    <UModal v-model="open">
        <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
            <template #header>
                <div class="flex flex-row items-center justify-between">
                    <div class="font-semibold text-lg">
                        {{ $t('Create new secret key') }}
                    </div>
                    <UButton color="gray" variant="ghost" icon="i-heroicons-x-mark-20-solid" @click="emits('close')" />
                </div>
            </template>

            <UFormGroup :label="$t('Name')" :hint="$t('Optional')">
                <UInput
                    v-model="apiKeyName"
                    :placeholder="$t('My Test Key')"
                    icon="i-fluent:lock-closed-key-24-regular"
                    :ui="{ rounded: 'rounded-md' }"
                />
            </UFormGroup>
            <template #footer>
                <div class="flex flex-row gap-3 justify-end">
                    <UButton size="sm" color="gray" variant="ghost" @click="emits('close')">
                        {{ $t('Cancel') }}
                    </UButton>
                    <UButton size="sm" color="primary" variant="solid" @click="onSubmit">
                        {{ $t('Create secret key') }}
                    </UButton>
                </div>
            </template>
        </UCard>
    </UModal>
</template>

<script setup lang="ts">

const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['close', 'submit'])

const open = ref(false)
const integrationStore = useIntegrationStore()
const { apiKeyName } = storeToRefs(integrationStore)

watch(
    () => props.isOpen,
    (value) => {
        open.value = value
    }
)

watch(
    () => open.value,
    (value) => {
        if (!value) {
            emits('close')
        }
    }
)

const onSubmit = () => {
    emits('submit')
}
</script>

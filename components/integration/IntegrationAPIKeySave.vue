<template>
    <UModal v-model="open">
        <UCard :ui="{ ring: '', divide: 'divide-y divide-gray-100 dark:divide-gray-800' }">
            <template #header>
                <div class="flex flex-row items-center justify-between">
                    <div class="font-semibold text-lg">
                        {{ $t('Save your key') }}
                    </div>
                    <UButton color="gray" variant="ghost" icon="i-heroicons-x-mark-20-solid" @click="emits('close')" />
                </div>
            </template>
            <div class="text-sm pb-3">
                {{
                    $t(
                        "Please save this secret key somewhere safe and accessible. For security reasons, you won't be able to view it again through your TTSOpenAI account. If you lose this secret key, you'll need to generate a new one."
                    )
                }}
            </div>
            <div class="flex flex-row items-center gap-3">
                <UInput class="flex-1" :ui="{ rounded: 'rounded-md' }" :model-value="secretKey" readonly />
                <UButton @click="copyKey" size="sm" :label="$t('Copy')" color="primary" :ui="{ rounded: 'rounded-lg' }">
                    <template #leading>
                        <UIcon :name="copied ? 'mdi:success' : 'i-mingcute:copy-line'" />
                    </template>
                </UButton>
            </div>
            <template #footer>
                <div class="flex flex-row gap-3 justify-end">
                    <UButton class="px-6" color="black" variant="solid" @click="emits('close')">
                        {{ $t('Done') }}
                    </UButton>
                </div>
            </template>
        </UCard>
    </UModal>
</template>

<script setup lang="ts">
const props = defineProps({
    isOpen: {
        type: Boolean,
        default: false,
    },
    secretKey: {
        type: String,
        default: '',
    },
})
const emits = defineEmits(['close'])

const open = ref(false)

watch(
    () => props.isOpen,
    (value) => {
        open.value = value
    }
)

watch(
    () => open.value,
    (value) => {
        if (!value) {
            emits('close')
        }
    }
)

const copied = ref(false)
const toast = useToast()
const { t } = useI18n()
const copyKey = () => {
    navigator.clipboard.writeText(props.secretKey)
    copied.value = true
    setTimeout(() => {
        copied.value = false
    }, 2000)

    toast.add({
        title: t('Copied'),
        description: t('Your secret key has been copied to your clipboard.'),
    })
}
</script>

<template>
    <UHorizontalNavigation
        :links="links"
        class="border-b border-gray-200 dark:border-gray-800"
        :ui="{
            wrapper: 'justify-between',
            container: '!mx-auto',
        }"
    />
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useHistoryStore } from '~~/stores/history'

const historyStore = useHistoryStore()
const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const { filterBy, currentPage } = storeToRefs(historyStore)

const setFilterBy = (filter: string) => {
    router.push({
        query: {
            filterBy: filter,
        },
    })
    historyStore.setFilterBy(filter)
}

watch(filterBy, async () => {
    // Reset the page to 1 everytime filter changes
    currentPage.value = 1
    await historyStore.filterHistories()
})

watch(
    () => route.query,
    (value) => {
        if (value?.filterBy) {
            historyStore.setFilterBy(value.filterBy as string)
        } else if (!value?.id) {
            historyStore.setFilterBy('all')
        }
    },
    {
        immediate: true,
        deep: true,
    }
)

const links = computed(() => {
    const config = useRuntimeConfig()
    return [
        [
            {
                label: t('All'),
                icon: 'i-material-symbols-deployed-code-history-outline',
                click: () => setFilterBy('all'),
                active: filterBy.value === 'all',
            },
            {
                label: t('Text'),
                icon: 'i-material-symbols-translate',
                click: () => setFilterBy('tts-text'),
                active: filterBy.value === 'tts-text',
            },
            {
                label: t('Emotion Text'),
                icon: 'fluent:emoji-sparkle-16-regular',
                click: () => setFilterBy('tts-text-emotion'),
                active: filterBy.value === 'tts-text-emotion',
            },
            {
                label: t('Documents'),
                icon: 'i-oui:documents',
                click: () => setFilterBy('tts-document'),
                active: filterBy.value === 'tts-document',
            },
            {
                label: t('Stories'),
                icon: 'i-fluent:people-chat-24-regular',
                click: () => setFilterBy('tts-story'),
                active: filterBy.value === 'tts-story',
                hidden: !config.public.features.storyMaker,
            },
        ].filter((item) => !item.hidden),
    ]
})
</script>

<template>
  <!-- @contextmenu.prevent="onContextMenu" -->

  <div
    class="px-4 py-2 text-sm !outline-0 !outline-transparent flex flex-col h-full w-full"
    @click="onClickEditor"
  >
    <div id="speech-studio-text-editor" class="flex h-full w-full break-all">
      <div class="min-w-52 leading-6 w-full select-text">
        <template v-for="(block, index) in inputBlocks">
          <span
            contenteditable="true"
            :placeholder="
            isEmpty && index === 0
                ? $t('Enter the text you want to convert to speech here.')
                : ''
            "
            @contextmenu.prevent="onContextMenu"
            v-if="block.type === 'text'"
            class="!outline-0 !outline-transparent min-w-20 select-text"
            @input="updateInput($event, index)"
            :id="`block_${index}`"
            @click="updateCursorAndBlock(index)"
            @focus="updateCursorAndBlock(index)"
            @contextmenu="updateCursorAndBlock(index)"
            @keydown="updateCursorAndBlock(index, $event)"
            >{{ block.text }}</span
          >
          <span
            v-else-if="block.type === 'break'"
            class="select-text px-1 py-0.5 rounded-md h-fit mx-1 my-1 cursor-pointer hover::scale-105 duration-200"
            :class="{
              'bg-orange-200 dark:bg-orange-900 hover:bg-orange-300 hover:dark:bg-orange-700':
                selectedBreaks.includes(index) || currentBreak === index,
              'bg-primary-100 dark:bg-primary-900 hover:bg-primary-200 dark:hover:bg-primary-700':
                !selectedBreaks.includes(index) && currentBreak !== index,
            }"
            @click="
              onBreakClick({
                event: $event,
                index,
              })
            "
            @focus="currentBreak = index"
            @contextmenu="currentBreak = index"
          >
            <UIcon
              :name="
                selectedBreaks.includes(index)
                  ? 'i-lets-icons-check-fill'
                  : 'i-fluent-clock-pause-20-regular'
              "
              class="h-4"
            />
            {{ $t(block.value) }}
            <span v-if="block.value === 'custom'"
              >({{ formatMsToTime(block.time) }})
            </span>
          </span>
          <br v-if="block.type === 'newline'" />
        </template>
      </div>
    </div>

    <UContextMenu
      id="speech-studio-context-menu"
      v-model="openContextMenu"
      :virtual-element="virtualElement"
      :popper="{ arrow: true, placement: 'bottom', offsetDistance: 20 }"
      :ui="{
        width: 'min-w-48',
      }"
    >
      <SpeechStudioContextMenu />
    </UContextMenu>
  </div>
  <div class="flex flex-row gap-2 items-center justify-end px-3 py-2">
    <div class="text-xs text-right">{{ textLength }} / {{ maxTextLength }}</div>
    <div @click="speechStudioStore.clearInputBlocks()">
      <UButton :padded="false" color="red" variant="link" :label="$t('Clear all')" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTextSelection } from "@vueuse/core";
import { useMouse, useWindowScroll } from "@vueuse/core";
import { update } from "lodash";
import { useSpeechStudioStore } from "~/stores/speech-studio";
const state = useTextSelection();
const speechStudioStore = useSpeechStudioStore();
const {
  currentBlock,
  inputBlocks,
  currentCursor,
  currentBreak,
  openContextMenu,
  customBreakValue,
  selectedBreaks,
  textLength,
  maxTextLength,
  isEmpty,
} = storeToRefs(speechStudioStore);
const translateStore = useTranslateStore();
const { speed } = storeToRefs(translateStore);
const { x, y } = useMouse();
const { y: windowY } = useWindowScroll();

const virtualElement = ref({ getBoundingClientRect: () => ({}) });
const isEditable = ref(true);
let clearCurrentBreak = null as any;
watch(
  () => openContextMenu.value,
  (value: boolean) => {
    if (!value) {
      clearCurrentBreak = setTimeout(() => {
        if (!openContextMenu.value) {
          currentBreak.value = -1;
        }
      }, 400);
    } else if (clearCurrentBreak !== null) {
      clearTimeout(clearCurrentBreak);
    }
  }
);

function getCaretPositionInContentEditable(element: HTMLElement) {
  let caretOffset = 0;
  let sel = window.getSelection();

  if (sel.rangeCount > 0) {
    let range = sel.getRangeAt(0);
    let preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(element);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    caretOffset = preCaretRange.toString().length;
  }

  return caretOffset;
}

function onContextMenu(mouseEvent: MouseEvent) {
  const top = unref(y) - unref(windowY);
  const left = unref(x);

  virtualElement.value.getBoundingClientRect = () => ({
    width: 0,
    height: 0,
    top,
    left,
  });

  // get speech-studio-text-editor
  const element = document.getElementById("block_" + currentBlock.value);
  const caretPosition = getCaretPositionInContentEditable(element);
  currentCursor.value = caretPosition;
  currentBreak.value = -1;

  // check if the current cursor is at the start and the block before is a break, do not open context menu
  if (
    currentCursor.value === 0 &&
    inputBlocks.value[currentBlock.value - 1]?.type === "break"
  ) {
    return;
  }

  openContextMenu.value = true;
}

const updateInput = (event: InputEvent, index: number) => {
  if (textLength.value >= maxTextLength.value) {
    event.preventDefault();
    return;
  }

  if (event.isComposing || event.shiftKey) {
    return;
  }

  const text = (event.target as HTMLDivElement).innerText;
  const block = inputBlocks.value[index];
  block.text = text;

  updateCursorAndBlock(index);
};

const onClickEditor = (event: any) => {
  // if click outside id block_x, focus on the last block, and not focusing on input
  if (
    !event.target.id.includes("block_") &&
    !event.target.id.includes("speech-studio-context-menu") &&
    !event.target.nodeName.includes("INPUT")
  ) {
    const element = document.getElementById("block_" + currentBlock.value);
    element?.focus();
  }
};

const onBreakClick = ({ event, index }: { event: MouseEvent; index: number }) => {
  // check if click with shift key, then mark the break to selectedBreaks
  if (event.shiftKey) {
    if (selectedBreaks.value.includes(index as never)) {
      selectedBreaks.value = selectedBreaks.value.filter(
        (selected: any) => selected !== index
      );
    } else {
      selectedBreaks.value = [...selectedBreaks.value, index as never];
    }

    // if currentBreak is not -1 and not in selectedBreaks, then add to selectedBreaks
    if (
      currentBreak.value >= 0 &&
      !selectedBreaks.value.includes(currentBreak.value as never)
    ) {
      selectedBreaks.value = [...selectedBreaks.value, currentBreak.value as never];
    }
    return;
  }

  if (selectedBreaks.value.length > 0) {
    if (!selectedBreaks.value.includes(index as never)) {
      selectedBreaks.value = [...selectedBreaks.value, index as never];
    }
  }

  currentBreak.value = index;

  const top = unref(y) - unref(windowY);
  const left = unref(x);

  virtualElement.value.getBoundingClientRect = () => ({
    width: 0,
    height: 0,
    top,
    left,
  });

  if (inputBlocks.value[index].value === "custom") {
    customBreakValue.value = inputBlocks.value[index].time;
  }
  openContextMenu.value = true;
};

const updateCursorAndBlock = (index: number, event?: any) => {
  currentBlock.value = index;

  const element = document.getElementById("block_" + currentBlock.value);
  const caretPosition = getCaretPositionInContentEditable(element);
  currentCursor.value = caretPosition;
  //   currentBreak.value = -1;
  // if keydown and key is Backspace and cursor is at the start, move cursor to the end of the previous block or remove the block if it is a break
  if (event && event.key === "Backspace" && currentCursor.value === 0) {
    // if current block's text is empty, remove the block
    if (inputBlocks.value[currentBlock.value].text === "") {
      speechStudioStore.removeBlock(currentBlock.value);
    }
    if (["break", "newline"].includes(inputBlocks.value[currentBlock.value - 1]?.type)) {
      speechStudioStore.removeBlock(currentBlock.value - 1);
      currentBlock.value = currentBlock.value - 2 >= 0 ? currentBlock.value - 2 : 0;
      currentCursor.value = inputBlocks.value[currentBlock.value].text.length;
    } else {
      currentBlock.value = currentBlock.value - 1 >= 0 ? currentBlock.value - 1 : 0;
      currentCursor.value = inputBlocks.value[currentBlock.value].text.length;
    }
    // focus on the last block
    nextTick(() => {
      const element = document.getElementById("block_" + currentBlock.value);
      element?.focus();
      // move cursor to the end of the last block
      const range = document.createRange();
      const sel = window.getSelection();
      range.selectNodeContents(element as Node);
      range.collapse(false);
      sel?.removeAllRanges();
      sel?.addRange(range);
    });
  }

  // if keydown and key is Enter, then add new line
  if (event && event.key === "Enter") {
    console.log("🚀 ~ updateCursorAndBlock ~ event:", event);
    // if not an japanese keyboard, add new line
    if (!event.isComposing) {
      speechStudioStore.addNewLineAfterCurrentCursor();
    } else {
      event.preventDefault();
    }
  }
};

defineShortcuts({
  meta_b: {
    usingInput: true,
    handler: () => {
      // get last break inside inputBlocks and add a new break to current cursor, if no break, add a new break default
      const lastBreak = inputBlocks.value
        .map((block, index) => ({ ...block, index }))
        .filter((block) => block.type === "break" && block.value !== "custom")
        .pop();
      if (!lastBreak) {
        speechStudioStore.addBreakAfterCurrentCursor("medium");
      } else {
        speechStudioStore.addBreakAfterCurrentCursor(lastBreak.value, lastBreak.time);
      }
    },
  },
  meta_shift_1: {
    usingInput: true,
    handler: () => {
      speechStudioStore.addBreakAfterCurrentCursor("custom", 1000);
    },
  },
  meta_shift_2: {
    usingInput: true,
    handler: () => {
      speechStudioStore.addBreakAfterCurrentCursor("custom", 2000);
    },
  },
  meta_shift_3: {
    usingInput: true,
    handler: () => {
      speechStudioStore.addBreakAfterCurrentCursor("custom", 3000);
    },
  },
  meta_shift_4: {
    usingInput: true,
    handler: () => {
      speechStudioStore.addBreakAfterCurrentCursor("custom", 4000);
    },
  },
  meta_shift_5: {
    usingInput: true,
    handler: () => {
      speechStudioStore.addBreakAfterCurrentCursor("custom", 5000);
    },
  },
  meta_a: {
    usingInput: true,
    handler: () => {
      selectedBreaks.value = inputBlocks.value
        .map((block, index) => ({ ...block, index }))
        .filter((block) => block.type === "break" && block.value !== "custom")
        .map((block) => block.index);

      // range all text
      // const range = document.createRange();
      // const sel = window.getSelection();
      // range.selectNodeContents(
      //   document.getElementById("speech-studio-text-editor") as Node
      // );
      // sel?.removeAllRanges();
      // sel?.addRange(range);
    },
  },

  escape: {
    usingInput: true,
    handler: () => {
      openContextMenu.value = false;
      selectedBreaks.value = [];
    },
  },
});

onMounted(() => {
  // focus on the last block
  currentBlock.value = inputBlocks.value.length - 1;
  // focus cursor to the end of the last block
  currentCursor.value = inputBlocks.value[currentBlock.value].text.length;
  const element = document.getElementById("block_" + currentBlock.value);
  element?.focus();
});

const onSelectText = (event: MouseEvent) => {
  const element = event.target as HTMLElement;
  const selection = window.getSelection();
  const range = document.createRange();
  range.selectNodeContents(element);
  selection?.removeAllRanges();
  selection?.addRange(range);
  // check if the current cursor is at the start and the block before is a break, select the break
  if (
    currentCursor.value === 0 &&
    inputBlocks.value[currentBlock.value - 1]?.type === "break"
  ) {
    currentBreak.value = currentBlock.value - 1;
  }
};
</script>

<style lang="scss">
[contenteditable="true"]:empty:before {
  content: attr(placeholder);
  display: block; /* For Firefox */
  color: #cbd5e0;
}

.dark [contenteditable="true"]:empty:before {
  color: #4b5563;
}

/* */
</style>

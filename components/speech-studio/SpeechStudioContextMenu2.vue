<script setup lang="ts">
const { t } = useI18n();
import { toLower } from "lodash";
import { SpeechStudioBreakType, useSpeechStudioV2Store } from "~/stores/speech-studio_v2";
const speechStudioStore = useSpeechStudioV2Store();
const {
  openContextMenu,
  selectedBreakValue,
  selectedBreakRef,
  currentBreakRef
} = storeToRefs(speechStudioStore);

const customBreakValue = ref(parseInt(selectedBreakRef.value?.getAttribute('data-break') || '0'));

const links = computed(() => {
  return [
    {
      label: t("Add break"),
      icon: "i-fluent-clock-pause-20-regular",
      key: "break",
      defaultOpen: true,
      tooltip: {
        text: t("Insert a predefined or custom pause between words"),
        shortcuts: ["⌘", "B"],
      },
      children: [
        {
          label: t("Standard"),
          defaultOpen: selectedBreakRef.value && !selectedBreakRef.value.textContent?.includes("Custom"),
          children: ["x-weak", "weak", "medium", "strong", "x-strong"].map((item) => ({
            label: t(item),
            active: toLower(selectedBreakRef.value?.textContent || '') === toLower(item),
            click: () => {
              nextTick(() => {
                if (selectedBreakValue.value && selectedBreakRef.value) {
                  selectedBreakRef.value.textContent = item
                  selectedBreakRef.value.setAttribute('data-break', item)
                  selectedBreakRef.value.setAttribute('data-break-type', SpeechStudioBreakType.STANDARD)
                  selectedBreakValue.value = item
                } else {
                  speechStudioStore.addBreak(item, SpeechStudioBreakType.STANDARD);
                }
              });

              openContextMenu.value = false;
              currentBreakRef.value = null
            },
          })),
        },
      ],
    },
    {
      label: t("Pronunciation"),
      icon: "i-ri-user-voice-line",
    },
    {
      label: t("Volume"),
      icon: "i-ri-volume-up-line",
    },
  ];
});

const customBreak = ref();
const focusCustomBreak = () => {

  nextTick(() => {
    customBreak.value?.focus();
  });
};
const onCustomBreak = () => {
  nextTick(() => {
    if (selectedBreakRef.value && selectedBreakValue.value) {
      const vlu = `Custom ${formatMsToTime(Number(customBreakValue.value))}`
      selectedBreakRef.value.textContent = vlu
      selectedBreakRef.value.setAttribute('data-break', customBreakValue.value.toString())
      selectedBreakRef.value.setAttribute('data-break-type', SpeechStudioBreakType.CUSTOM)
      selectedBreakValue.value = vlu
    } else {
      speechStudioStore.addBreak(customBreakValue.value.toString(), SpeechStudioBreakType.CUSTOM);
    }
    openContextMenu.value = false;
    currentBreakRef.value = null
  });
};
</script>

<template>
  <div class="">
    <UAccordion :items="links" :ui="{ wrapper: 'flex flex-col w-full' }">
      <template #default="{ item, index, open }">
        <UTooltip
          :text="item.tooltip?.text"
          :shortcuts="item.tooltip?.shortcuts"
          :popper="{ placement: 'right' }"
          :prevent="!item.tooltip?.text"
          class="w-full"
          :ui="{
            width: 'w-fit max-w-screen-xl',
          }"
        >
          <UButton
            color="gray"
            variant="ghost"
            block
            class="border-b border-gray-200 dark:border-gray-700"
            :ui="{ rounded: 'rounded-none', padding: { sm: 'p-3' } }"
          >
            <template #leading>
              <div class="w-6 h-6 flex items-center justify-center -my-1">
                <UIcon :name="item.icon" class="w-4 h-4" />
              </div>
            </template>

            <span class="truncate">{{ item.label }}</span>

            <template #trailing>
              <UIcon
                name="i-heroicons-chevron-right-20-solid"
                class="w-5 h-5 ms-auto transform transition-transform duration-200"
                :class="[open && 'rotate-90']"
              />
            </template>
          </UButton>
        </UTooltip>
      </template>
      <template #item="{ item }">
        <div v-if="item.key === 'break'" class="border-b dark:border-b-gray-700">
          <UDashboardSidebarLinks
            :links="item.children"
            :ui="{
              label: 'text-sm pl-2',
            }"
          >
          </UDashboardSidebarLinks>
          <UAccordion
            :items="[
              {
                label: 'Custom',
                slot: 'custom',
                defaultOpen: selectedBreakRef && selectedBreakRef.textContent && selectedBreakRef.textContent.includes('Custom'),
                tooltip: {
                  text: $t('Enter a custom pause time in milliseconds'),
                  shortcuts: ['⌘', '1-5'],
                },
              },
            ]"
          >
            <template #default="{ item, index, open }">
              <UTooltip
                :text="item.tooltip?.text"
                :shortcuts="item.tooltip?.shortcuts"
                :popper="{ placement: 'right' }"
                :prevent="!item.tooltip?.text"
                class="w-full"
                :ui="{
                  width: 'w-fit max-w-screen-xl',
                }"
              >
                <UButton
                  block
                  color="gray"
                  variant="ghost"
                  class="border-b border-gray-200 dark:border-gray-700"
                  :ui="{ rounded: 'rounded-none', padding: { md: 'pr-2.5' } }"
                  @click="focusCustomBreak"
                >
                  <span
                    class="truncate pl-1.5 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:before:bg-gray-50 dark:hover:before:bg-gray-800/50"
                    >{{ $t(item.label) }}</span
                  >

                  <template #trailing>
                    <UIcon
                      name="i-heroicons-chevron-right-20-solid"
                      class="w-5 h-5 ms-auto transform transition-transform duration-200 text-gray-500 dark:text-gray-400"
                      :class="[open && 'rotate-90']"
                    />
                  </template>
                </UButton>
              </UTooltip>
            </template>
            <template #custom>
              <div class="text-gray-900 dark:text-white text-center px-4">
                <UForm @submit="onCustomBreak">
                  <UInput
                    ref="customBreak"
                    size="xs"
                    autofocus
                    :ui="{
                      base: 'text-right',
                    }"
                    v-model="customBreakValue"
                    type="number"
                  >
                    <template #trailing>
                      <span class="text-gray-500 dark:text-gray-400 text-xs">ms</span>
                    </template>
                  </UInput>
                </UForm>
              </div>
            </template>
          </UAccordion>
          <div v-if="selectedBreakRef && selectedBreakRef.getAttribute('data-break') !== '0'" class="pl-0.5">
            <UButton
              color="red"
              variant="ghost"
              :label="$t('Remove break')"
              :trailing="false"
              class="w-full"
              :ui="{
                rounded: 'rounded-none',
                padding: {
                  md: 'pl-4 pt-1',
                },
              }"
              @click="
                selectedBreakRef?.parentElement?.remove();
                openContextMenu = false;
              "
            />
          </div>
        </div>
        <div v-else class="text-sm pl-4 pt-3">
          {{ $t("Coming soon!") }}
        </div>
      </template>
    </UAccordion>
  </div>
</template>

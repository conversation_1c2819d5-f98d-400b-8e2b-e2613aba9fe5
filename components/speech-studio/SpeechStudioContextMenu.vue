<script setup lang="ts">
const { t } = useI18n();
import { useSpeechStudioStore } from "~/stores/speech-studio";
const speechStudioStore = useSpeechStudioStore();
const {
  inputBlocks,
  openContextMenu,
  currentCursor,
  currentBlock,
  currentBreak,
  currentBreakValue,
  customBreakValue,
  selectedBreaks,
} = storeToRefs(speechStudioStore);

const links = computed(() => {
  return [
    {
      label: currentBreak.value >= 0 ? t("Update break") : t("Add break"),
      icon: "i-fluent-clock-pause-20-regular",
      key: "break",
      defaultOpen: currentBreak.value >= 0,
      tooltip: {
        text: t("Insert a predefined or custom pause between words"),
        shortcuts: ["⌘", "B"],
      },
      children: [
        {
          label: t("Standard"),
          defaultOpen: currentBreak.value >= 0 && currentBreakValue.value !== "custom",
          children: ["x-weak", "weak", "medium", "strong", "x-strong"].map((item) => ({
            label: t(item),
            active: currentBreakValue.value === item,
            click: () => {
              // if selectedBreaks has a value, update all selected breaks
              if (selectedBreaks.value.length > 0) {
                selectedBreaks.value.forEach((index) => {
                  inputBlocks.value[index].value = item;
                });
                selectedBreaks.value = [];
              } else {
                // if currentBreak has a value, update the break
                if (currentBreak.value >= 0) {
                  inputBlocks.value[currentBreak.value].value = item;
                } else {
                  // break current block into two blocks, break at current cursor, push break after current block
                  speechStudioStore.addBreakAfterCurrentCursor(item);
                }
              }

              openContextMenu.value = false;
            },
          })),
        },
      ],
    },
    {
      label: t("Pronunciation"),
      icon: "i-ri-user-voice-line",
    },
    {
      label: t("Volume"),
      icon: "i-ri-volume-up-line",
    },
  ];
});

const customBreak = ref();
const focusCustomBreak = () => {
  console.log("🚀 ~ nextTick ~ customBreak.value:", customBreak.value);

  nextTick(() => {
    customBreak.value?.focus();
  });
};

const onCustomBreak = () => {
  if (customBreakValue.value) {
    // if selectedBreaks has a value, update all selected breaks
    if (selectedBreaks.value.length > 0) {
      selectedBreaks.value.forEach((index) => {
        inputBlocks.value[index].time = customBreakValue.value;
        inputBlocks.value[index].value = "custom";
      });
      selectedBreaks.value = [];
    }
    // if currentBreak has a value, update the break
    else if (currentBreak.value >= 0) {
      inputBlocks.value[currentBreak.value].time = customBreakValue.value;
      inputBlocks.value[currentBreak.value].value = "custom";
    } else {
      // break current block into two blocks, break at current cursor, push break after current block
      speechStudioStore.addBreakAfterCurrentCursor(
        "custom",
        customBreakValue.value as number
      );
    }

    openContextMenu.value = false;
  }
};

const onRemoveBreak = () => {
  if (selectedBreaks.value.length > 0) {
    // remove all selected breaks from inputBlocks
    inputBlocks.value = inputBlocks.value.filter(
      (block, index) => !selectedBreaks.value.includes(index)
    );
    selectedBreaks.value = [];
  } else {
    inputBlocks.value.splice(currentBreak.value, 1);
  }

  openContextMenu.value = false;
};
</script>

<template>
  <div class="">
    <UAccordion :items="links" :ui="{ wrapper: 'flex flex-col w-full' }">
      <template #default="{ item, index, open }">
        <UTooltip
          :text="item.tooltip?.text"
          :shortcuts="item.tooltip?.shortcuts"
          :popper="{ placement: 'right' }"
          :prevent="!item.tooltip?.text"
          class="w-full"
          :ui="{
            width: 'w-fit max-w-screen-xl',
          }"
        >
          <UButton
            color="gray"
            variant="ghost"
            block
            class="border-b border-gray-200 dark:border-gray-700"
            :ui="{ rounded: 'rounded-none', padding: { sm: 'p-3' } }"
          >
            <template #leading>
              <div class="w-6 h-6 flex items-center justify-center -my-1">
                <UIcon :name="item.icon" class="w-4 h-4" />
              </div>
            </template>

            <span class="truncate">{{ item.label }}</span>

            <template #trailing>
              <UIcon
                name="i-heroicons-chevron-right-20-solid"
                class="w-5 h-5 ms-auto transform transition-transform duration-200"
                :class="[open && 'rotate-90']"
              />
            </template>
          </UButton>
        </UTooltip>
      </template>
      <template #item="{ item }">
        <div v-if="item.key === 'break'" class="border-b dark:border-b-gray-700">
          <UDashboardSidebarLinks
            :links="item.children"
            :ui="{
              label: 'text-sm pl-2',
            }"
          >
          </UDashboardSidebarLinks>
          <UAccordion
            :items="[
              {
                label: 'Custom',
                slot: 'custom',
                defaultOpen: currentBreakValue === 'custom',
                tooltip: {
                  text: $t('Enter a custom pause time in milliseconds'),
                  shortcuts: ['⌘', '1-5'],
                },
              },
            ]"
          >
            <template #default="{ item, index, open }">
              <UTooltip
                :text="item.tooltip?.text"
                :shortcuts="item.tooltip?.shortcuts"
                :popper="{ placement: 'right' }"
                :prevent="!item.tooltip?.text"
                class="w-full"
                :ui="{
                  width: 'w-fit max-w-screen-xl',
                }"
              >
                <UButton
                  block
                  color="gray"
                  variant="ghost"
                  class="border-b border-gray-200 dark:border-gray-700"
                  :ui="{ rounded: 'rounded-none', padding: { md: 'pr-2.5' } }"
                  @click="focusCustomBreak"
                >
                  <span
                    class="truncate pl-1.5 text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:before:bg-gray-50 dark:hover:before:bg-gray-800/50"
                    >{{ $t(item.label) }}</span
                  >

                  <template #trailing>
                    <UIcon
                      name="i-heroicons-chevron-right-20-solid"
                      class="w-5 h-5 ms-auto transform transition-transform duration-200 text-gray-500 dark:text-gray-400"
                      :class="[open && 'rotate-90']"
                    />
                  </template>
                </UButton>
              </UTooltip>
            </template>
            <template #custom>
              <div class="text-gray-900 dark:text-white text-center px-4">
                <UForm @submit="onCustomBreak">
                  <UInput
                    ref="customBreak"
                    size="xs"
                    autofocus
                    :ui="{
                      base: 'text-right',
                    }"
                    v-model="customBreakValue"
                  >
                    <template #trailing>
                      <span class="text-gray-500 dark:text-gray-400 text-xs">ms</span>
                    </template>
                  </UInput>
                </UForm>
              </div>
            </template>
          </UAccordion>
          <div v-if="currentBreak >= 0" class="pl-0.5">
            <UButton
              color="red"
              variant="ghost"
              :label="$t('Remove break')"
              :trailing="false"
              class="w-full"
              :ui="{
                rounded: 'rounded-none',
                padding: {
                  md: 'pl-4 pt-1',
                },
              }"
              @click="onRemoveBreak"
            />
          </div>
        </div>
        <div v-else class="text-sm pl-4 pt-3">
          {{ $t("Coming soon!") }}
        </div>
      </template>
    </UAccordion>
  </div>
</template>

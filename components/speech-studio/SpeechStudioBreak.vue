<script setup lang="ts">
import { useSpeechStudioV2Store } from '@/stores/speech-studio_v2'

const speechStudioV2Store = useSpeechStudioV2Store()

const { currentBreakRef, openContextMenu } = storeToRefs(speechStudioV2Store)

const props = defineProps<{
    value: string
    dataBreak: string
    breakType: string
}>()

const emit = defineEmits<{
    (event: 'click', value: { value: string; breakRef: HTMLSpanElement | null; breakType: string }): void
}>()

const onBreakClick = () => {
    emit('click', {
        value: props.value,
        breakRef: breakRef.value,
        breakType: props.breakType,
    })
}

watch(openContextMenu, (newVal) => {
    if (!newVal) {
        currentBreakRef.value = null
    }
})

const breakRef = ref<HTMLSpanElement | null>(null)
</script>

<template>
    <span
        class="speech-studio-break px-1 py-0.5 bg-primary-100 dark:bg-primary-900 rounded-md h-fit mx-1 my-1 hover:bg-primary-200 dark:hover:bg-primary-700 cursor-pointer hover::scale-105"
        contenteditable="false"
        @click="onBreakClick"
        :class="{
            '!bg-orange-200 dark:bg-orange-900 hover:bg-orange-300 hover:dark:bg-orange-700':
                currentBreakRef === breakRef,
        }"
    >
        <svg
            data-v-d58c1c1d=""
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            aria-hidden="true"
            role="img"
            class="icon h-4"
            width="1em"
            height="1em"
            viewBox="0 0 20 20"
        >
            <path
                fill="currentColor"
                d="M11 1a8 8 0 1 1-.589 15.979q.237-.469.383-.982q.102.003.206.003a7 7 0 1 0-6.997-6.794q-.513.146-.982.383A8 8 0 0 1 11 1m-.5 3a.5.5 0 0 1 .492.41L11 4.5V9h2.5a.5.5 0 0 1 .09.992L13.5 10h-3a.5.5 0 0 1-.492-.41L10 9.5v-5a.5.5 0 0 1 .5-.5M10 14.5a4.5 4.5 0 1 1-9 0a4.5 4.5 0 0 1 9 0M4 17a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 0-1 0v4a.5.5 0 0 0 .5.5m3 0a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 0-1 0v4a.5.5 0 0 0 .5.5"
            ></path>
        </svg>
        <span ref="breakRef" :data-break="dataBreak" :data-break-type="breakType">{{ value }}</span>
    </span>
</template>

<template>
  <div
    class="px-4 py-2 text-sm !outline-0 !outline-transparent h-full w-full"
  >
    <div
      id="speech-studio-text-editor"
      class="h-full w-full break-all"
      :placeholder="$t('Please enter your text here')"
      ref="editorRef"
      contenteditable="true"
      @contextmenu.prevent="onContextMenu"
      @click="speechStudioV2Store.saveCursorPosition()"
      @keydown="onKeyDown"
      @input="onInput"
    >
      <!-- Existing content will be here -->
    </div>


    <UContextMenu
      id="speech-studio-context-menu"
      v-model="openContextMenu"
      :virtual-element="virtualElement"
      :popper="{ arrow: true, placement: 'bottom', offsetDistance: 20 }"
      :ui="{
        width: 'min-w-48',
      }"
    >
      <SpeechStudioContextMenu2 />
    </UContextMenu>
  </div>
  <div class="flex flex-row gap-2 items-center justify-end px-3 py-2">
    <div @click="console.log('clear')">
      <UButton :padded="false" color="red" variant="link" :label="$t('Clear all')" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, computed } from 'vue';
import { useMouse, useWindowScroll } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { useNuxtApp } from '#app';
import { useSpeechStudioV2Store } from '~/stores/speech-studio_v2';

const nuxtApp = useNuxtApp();
const $t = computed(() => nuxtApp.$t || ((key: string) => key));

provide('$t', $t);

const speechStudioV2Store = useSpeechStudioV2Store();
const {
  openContextMenu,
  editorRef: editorRefState,
  virtualElement: virtualElementState,
} = storeToRefs(speechStudioV2Store);

const editorRef = ref<HTMLDivElement | null>(null);

const { x, y } = useMouse();
const { y: windowY } = useWindowScroll();

const virtualElement = ref({ getBoundingClientRect: () => ({}) });

function onContextMenu(mouseEvent: MouseEvent) {
  const top = unref(y) - unref(windowY);
  const left = unref(x);

  virtualElement.value.getBoundingClientRect = () => ({
    width: 0,
    height: 0,
    top,
    left,
  });

  openContextMenu.value = true;
}

function onKeyDown(event: KeyboardEvent) {
  speechStudioV2Store.saveCursorPosition();
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    document.execCommand('insertLineBreak');
    speechStudioV2Store.saveCursorPosition();
  }
}

function onInput() {
  speechStudioV2Store.saveCursorPosition();
}


defineShortcuts({
  meta_b: {
    usingInput: true,
    handler: () => {
      speechStudioV2Store.addBreak("medium", SpeechStudioBreakType.STANDARD);
    },
  },
  meta_shift_1: {
    usingInput: true,
    handler: () => {
      speechStudioV2Store.addBreak("10000", SpeechStudioBreakType.CUSTOM);
    },
  },
  meta_shift_2: {
    usingInput: true,
    handler: () => {
      speechStudioV2Store.addBreak("20000", SpeechStudioBreakType.CUSTOM);
    },
  },
  meta_shift_3: {
    usingInput: true,
    handler: () => {
      speechStudioV2Store.addBreak("30000", SpeechStudioBreakType.CUSTOM);
    },
  },
  meta_shift_4: {
    usingInput: true,
    handler: () => {
      speechStudioV2Store.addBreak("40000", SpeechStudioBreakType.CUSTOM);
    },
  },
  meta_shift_5: {
    usingInput: true,
    handler: () => {
      speechStudioV2Store.addBreak("50000", SpeechStudioBreakType.CUSTOM);
    },
  },

  escape: {
    usingInput: true,
    handler: () => {
      openContextMenu.value = false;
    },
  },
});


onMounted(() => {
  editorRefState.value = editorRef.value;
  virtualElementState.value = virtualElement.value
});

</script>



<style lang="css">
[contenteditable="true"]:empty:before {
  content: attr(placeholder);
  pointer-events: none;
  display: block; /* For Firefox */
  color: #aaa;
}

/* */

div[contenteditable="true"] {
  border: none;
}

div[contenteditable="true"]:focus {
  border: none;
}

div[contenteditable="true"]:focus-visible {
  border: none;
  outline: none;
}
</style>

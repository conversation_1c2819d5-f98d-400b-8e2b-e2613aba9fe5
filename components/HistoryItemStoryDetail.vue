<script lang="ts" setup>
import { saveAs } from 'file-saver'
import { watch } from 'vue'
import { AccessEnum, type BlockItem } from '~/types'
import { ModalsContainer, useModal } from 'vue-final-modal'
import DownloadConfirmation from '~/base-components/DownloadConfirmation.vue'

const props = defineProps({
    details: Object,
    uuid: String,
    isInputPro: Boolean,
})
const authStore = useAuthStore()
const historyStore = useHistoryStore()

const { showRetryBlockModal, onDownloadAll } = historyStore
const { loadingBtnDownload, errors } = storeToRefs(historyStore)
const notificationsStore = useNotificationsStore()
const { loadings } = storeToRefs(notificationsStore)
const { canAccess } = storeToRefs(authStore)
const { t } = useI18n()
// Columns
const columns = computed(() => [
    {
        key: 'name',
        label: t('Block name'),
        sortable: false,
    },
    {
        key: 'input',
        label: t('Input text'),
        sortable: false,
    },
    {
        key: 'voice_id',
        label: t('Voice ID'),
        sortable: false,
    },
    {
        key: 'speed',
        label: t('Speed'),
        sortable: false,
    },
    {
        key: 'duration',
        label: t('Duration'),
        sortable: false,
    },
    {
        key: 'used_credit',
        label: t('Used credit'),
        sortable: false,
    },
    {
        key: 'status',
        label: t('Status'),
        sortable: false,
    },
    {
        key: 'actions',
        label: t('Audio'),
        sortable: false,
    },
])

const selected = ref([])

const selectedColumns = ref(columns.value.map((column: any) => column.key))
const columnsTable = computed(() => columns.value.filter((column) => selectedColumns.value.includes(column.key)))

const search = ref('')

const resetFilters = () => {
    search.value = ''
}

const statusBlocks = (status: number) => {
    switch (status) {
        case 1:
            return t('Processing')
        case 2:
            return t('Completed')
        case 3:
            return t('Failed')
        case 4:
            return t('Retrying')
    }
}
// Pagination
const sort = ref({ column: 'id', direction: 'asc' as const })
const page = ref(1)
const pageCount = ref(10)
const pageTotal = computed(() => props.details?.blocks.length)
const pageFrom = computed(() => (page.value - 1) * pageCount.value + 1)
const pageTo = computed(() => Math.min(page.value * pageCount.value, pageTotal.value))

const rows = computed(() => {
    return props.details?.blocks?.slice(pageFrom.value - 1, pageTo.value)
})

const audioElm = ref(null) as Ref<null | any>
const selectedBlock = ref(null) as Ref<null | any>
const isPlaying = ref(false)
const onPlayAudio = (block: any) => {
    selectedBlock.value = block
    nextTick(() => {
        if (audioElm.value) {
            if (isPlaying.value) {
                audioElm.value.pause()
                isPlaying.value = false
            } else {
                audioElm.value.play()
                isPlaying.value = true
            }
        }
    })
}

const onPausedAudio = () => {
    audioElm.value.pause()
}

const onPlay = () => {
    isPlaying.value = true
}

const onPause = () => {
    isPlaying.value = false
}

const onDownloadFile = (block: any) => {
    // if (block?.media_url) {
    //     saveAs(block?.media_url, `${block?.name || block?.id}.mp3`)
    // }
    notificationsStore.downloadFile(block?.uuid, block?.media_url, block)
}

watch(pageCount, () => {
    page.value = 1
})

// Retry
const onRetry = (block: BlockItem) => {
    showRetryBlockModal(block, props.uuid as string, props.isInputPro)
}

const toast = useToast()

const { open, close } = useModal({
    component: DownloadConfirmation,
    attrs: {
        title: t('Have you been OK with all the sections or not?'),
        async onConfirm(isReplaceAudio: boolean) {
            console.log('🚀 ~ onConfirm ~ isReplaceAudio:', isReplaceAudio)
            close()
            const result = await onDownloadAll(selected.value, props.uuid as string, isReplaceAudio)
            if (result) {
                toast.add({
                    title: 'Request success',
                    description: t(
                        'Your request has been sent successfully. Please wait for a moment. We will send you an email when the download is ready.'
                    ),
                    color: 'green',
                    timeout: 30000,
                })
            } else {
                toast.add({
                    title: t('Download blocks'),
                    description: t('Download blocks failed. Please try again.'),
                    color: 'red',
                    timeout: 30000,
                })
            }
        },
        onCancel() {
            close()
        },
    },
})

// Download all
// const onDownload = async () => {

// const { open, close } = useModal({
// component: DownloadConfirmation,
// attrs: {
//     title: t("Have you been OK with all the sections or not?"),
//     async onConfirm() {
//         close()
//         await onDownloadAll(selected.value)
//     },
//     onCancel() {
//         close()
//     },
// },
// })
// }

const isActiveDownloadAll = computed(() => {
    return selected.value.length > 0
})

const checkAllElm = ref(null) as Ref<null | any>

const onChangeCheckbox = (target: Event) => {
    const elm = target.target as HTMLInputElement
    const isCheckedAll = elm.getAttribute('aria-label') === 'Select all'
    if (isCheckedAll) {
        checkAllElm.value = elm
    }
    if (isCheckedAll && selected.value.length > 0) {
        selected.value = props.details?.blocks
        setTimeout(() => {
            checkAllElm.value.checked = true
        }, 100)
    } else {
        checkAllElm.value.checked = selected.value.length === props.details?.blocks.length
    }
}
</script>

<template>
    <UCard
        class="w-full"
        :ui="{
            base: '',
            ring: '',
            divide: 'divide-y divide-gray-200 dark:divide-gray-700',
            header: { padding: 'px-4 py-5' },
            body: { padding: '', base: 'divide-y divide-gray-200 dark:divide-gray-700' },
            footer: { padding: 'p-4' },
        }"
    >
        <template #header>
            <h2
                class="font-semibold text-xl text-gray-900 dark:text-white leading-tight flex flex-row justify-between items-center"
            >
                <div>
                    {{ $t('Blocks') }}
                </div>
                <audio
                    v-if="selectedBlock?.media_url"
                    :key="selectedBlock?.id"
                    ref="audioElm"
                    controls
                    class="rounded-full bg-primary-300"
                    :controlsList="canAccess(AccessEnum.IS_DOWNLOAD) ? '' : 'nodownload'"
                    @play="onPlay"
                    @pause="onPause"
                >
                    <source :src="selectedBlock?.media_url" type="audio/mpeg" />
                    Your browser does not support the audio element.
                </audio>
            </h2>
        </template>

        <!-- Filters -->
        <!-- <div class="flex items-center justify-between gap-3 px-4 py-3">
      <UInput
        v-model="search"
        icon="i-heroicons-magnifying-glass-20-solid"
        placeholder="Search..."
      />
    </div> -->

        <!-- Header and Action buttons -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-2 p-2 justify-between">
            <div class="flex items-center gap-1.5">
                <span class="text-sm leading-5">{{ t('Rows per page') }}:</span>
                <USelect v-model="pageCount" :options="[5, 10, 20, 50, 100]" class="me-2 w-20" size="xs" />
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-3 gap-2 p-2 justify-between">
                <div class="md:col-span-2 md:text-end">
                    <UTooltip
                        :text="selected.length === 1 ? $t('Select 2 or more blocks to merge and download') : ''"
                        :prevent="selected.length !== 1"
                    >
                        <UButton
                            :loading="loadingBtnDownload"
                            @click="open"
                            icon="i-ooui-table-merge-cells"
                            color="gray"
                            size="xs"
                            :disabled="selected.length === 1"
                        >
                            {{ selected.length ? t('Merge and download') : t('Merge and download all') }}
                        </UButton>
                    </UTooltip>
                </div>
                <div class="col-span-1 flex md:justify-end">
                    <USelectMenu
                        v-model="selectedColumns"
                        :options="columns"
                        multiple
                        optionAttribute="label"
                        valueAttribute="key"
                        size="xs"
                        :uiMenu="{ container: 'w-full', width: 'w-screen max-w-64' }"
                        class="text-end"
                    >
                        <UButton icon="i-heroicons-view-columns" color="gray" size="xs">
                            {{ t('Columns') }}
                        </UButton>
                    </USelectMenu>
                </div>
            </div>
        </div>

        <!-- Table -->
        <!-- :ui="{
            td: { base: 'max-w-[100px] truncate' },
            default: { checkbox: { color: 'gray' } },
        }" -->
        <div class="relative overflow-auto w-full">
            <UTable
                :ui="{
                    default: { checkbox: { color: 'gray', class: 'checkbox-all' } },
                }"
                v-model:sort="sort"
                v-model="selected"
                :rows="rows"
                :columns="columnsTable"
                sort-mode="manual"
                class="w-full"
                @change="onChangeCheckbox"
            >
                <template #name-data="{ row }">
                    <div class="max-w-32 truncate">
                        <span>{{ row.name }}</span>
                    </div>
                </template>
                <template #input-data="{ row }">
                    <div class="max-w-40 truncate">
                        <span>{{ row.input }}</span>
                    </div>
                </template>
                <template #status-data="{ row }">
                    <UBadge
                        size="xs"
                        :color="row.status === 1 ? 'gray' : row.status === 2 ? 'green' : 'red'"
                        variant="solid"
                        >{{ statusBlocks(row.status) }}
                    </UBadge>
                </template>
                <template #actions-data="{ row }">
                    <div class="flex flex-row gap-2 items-center">
                        <UButton
                            v-if="row.status === 2"
                            :icon="
                                isPlaying && selectedBlock?.id === row.id
                                    ? 'i-heroicons-pause-solid'
                                    : 'i-heroicons-play-solid'
                            "
                            size="2xs"
                            color="gray"
                            variant="solid"
                            :ui="{ rounded: 'rounded-full' }"
                            square
                            @click="onPlayAudio(row)"
                        />
                        <UButton
                            v-if="row.status === 2"
                            :icon="loadings[row.uuid] ? 'eos-icons:loading' : 'i-material-symbols-download'"
                            size="2xs"
                            color="gray"
                            variant="solid"
                            :ui="{ rounded: 'rounded-full' }"
                            square
                            @click="onDownloadFile(row)"
                        />
                        <UButton
                            v-if="row.status === 3 || row.status === 2"
                            :icon="'i-material-symbols-replay'"
                            size="2xs"
                            color="orange"
                            variant="solid"
                            :ui="{ rounded: 'rounded-full' }"
                            square
                            @click="onRetry(row)"
                        />
                    </div>
                </template>
            </UTable>
        </div>
        <!-- Number of rows & Pagination -->
        <template #footer>
            <div class="flex flex-wrap justify-between items-center">
                <div>
                    <span class="text-sm leading-5">
                        {{ t('Showing') }}
                        <span class="font-medium">{{ pageFrom }}</span>
                        {{ t('to') }}
                        <span class="font-medium">{{ pageTo }}</span>
                        {{ t('of') }}
                        <span class="font-medium">{{ pageTotal }}</span>
                        {{ t('results') }}
                    </span>
                </div>

                <UPagination
                    v-model="page"
                    :page-count="pageCount"
                    :total="pageTotal"
                    :ui="{
                        wrapper: 'flex items-center gap-1',
                        rounded: '!rounded-full min-w-[32px] justify-center',
                        default: {
                            activeButton: {
                                variant: 'outline',
                            },
                        },
                    }"
                />
            </div>
        </template>
    </UCard>
</template>

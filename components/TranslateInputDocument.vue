<template>
    <TranslateInputFiles />
</template>

<script setup lang="ts">
import { watch, ref, onMounted, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useTranslateStore } from '~/stores/translate'

import _ from 'lodash'
import SuggestPrompts from './SuggestPrompts.vue'
const translateStore = useTranslateStore()
const { t } = useI18n()
const { inputText, customPrompt, showCustomPrompt, translateOptions, loadings } = storeToRefs(translateStore)

const links = [
    {
        label: t('Input Document'),
        icon: 'i-mdi-file-text-multiple',
    },
]
//watch inputText
// watch(inputText, (value) => {
//     translateStore.clearResultText()
// })
</script>

<template>
    <div class="text-base text-yellow-400 gap-2 flex flex-row items-center justify-between w-full">
        <div class="gap-2 flex flex-row items-center">
            <UIcon name="i-eos-icons-loading" class="text-lg" />
            <div>
                {{ $t("We're processing your audio. Please wait a moment.") }}
            </div>
            <UTooltip :text="$t(`We will send you an email when it's ready.`)">
                <UIcon name="i-mingcute-question-fill" class="text-sm" />
            </UTooltip>
        </div>
        <UButton
            v-if="runtimeConfig.public.features.stopProcessing || isSuperUser"
            class="ml-auto"
            icon="gravity-ui:circle-stop"
            size="sm"
            color="red"
            variant="soft"
            :label="$t('Stop processing')"
            :trailing="false"
            @click="onStopProcessing"
        />
    </div>
</template>

<script setup lang="ts">
const { t } = useI18n()
const props = defineProps<{
    item: any
}>()

const historyStore = useHistoryStore()
const dialogsStore = useDialogsStore()
const runtimeConfig = useRuntimeConfig()
const authStore = useAuthStore()
const { isSuperUser } = storeToRefs(authStore)

const onStopProcessing = () => {
    dialogsStore.showConfirmDialog({
        title: t('Stop processing'),
        message: t('Are you sure you want to stop processing this history?'),
        confirmButtonText: t('Stop processing'),
        cancelButtonText: t('Cancel'),
        onConfirm: async () => {
            await historyStore.stopProcessingHistory(props.item?.id, props.item?.uuid)
            await historyStore.filterHistories()
        },
    })
}
</script>
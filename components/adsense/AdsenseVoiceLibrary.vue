<template>
  <ins
    class="adsbygoogle"
    style="display: block"
    data-ad-format="fluid"
    data-ad-layout-key="-h9+1j+4x-46-1s"
    data-ad-client="ca-pub-4149002346699299"
    data-ad-slot="1480306193"
  ></ins>
</template>

<script setup lang="ts">
onMounted(() => {
  if (process.client) {
    const script = document.createElement("script");
    // (adsbygoogle = window.adsbygoogle || []).push({});
    script.innerHTML = `(adsbygoogle = window.adsbygoogle || []).push({});`;
    // append script tag to body
    document.body.appendChild(script);
  }
});
</script>

<template>
  <ins
    class="adsbygoogle"
    style="display: inline-block; width: 130px; height: 600px"
    data-ad-client="ca-pub-4149002346699299"
    :data-ad-slot="adSlot"
  ></ins>
</template>

<script setup lang="ts">
const props = defineProps({
  adSlot: {
    type: Number,
    default: 6853457344,
  },
});
onMounted(() => {
  if (process.client) {
    const script = document.createElement("script");
    // (adsbygoogle = window.adsbygoogle || []).push({});
    script.innerHTML = `(adsbygoogle = window.adsbygoogle || []).push({});`;
    // append script tag to body
    document.body.appendChild(script);
  }
});
</script>

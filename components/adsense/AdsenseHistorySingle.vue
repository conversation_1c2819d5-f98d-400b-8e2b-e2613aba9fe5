<template>
  <ins
    class="adsbygoogle"
    style="display: block"
    data-ad-format="autorelaxed"
    data-ad-client="ca-pub-4149002346699299"
    data-ad-slot="5687020809"
  ></ins>
</template>

<script setup lang="ts">
const props = defineProps({
  adSlot: {
    type: String,
    default: "6853457344",
  },
});
onMounted(() => {
  if (process.client) {
    const script = document.createElement("script");
    // (adsbygoogle = window.adsbygoogle || []).push({});
    script.innerHTML = `(adsbygoogle = window.adsbygoogle || []).push({});`;
    // append script tag to body
    document.body.appendChild(script);
  }
});
</script>

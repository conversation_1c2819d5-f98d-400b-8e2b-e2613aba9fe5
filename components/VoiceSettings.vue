<template>
    <UCard class="" :ui="{ body: { padding: '!p-0' } }">
        <div class="w-full">
            <UHorizontalNavigation :links="links" class="border-b border-gray-200 dark:border-gray-800" />
            <div
                class="py-4 pb-36 md:pb-10 h-full overflow-auto md:max-h-[calc(100vh-300px)] flex flex-col space-y-4 scrollbar-thin"
            >
                <UFormGroup v-if="!isUsingFreePlan" :label="$t('Quality')" :ui="{ label: { wrapper: 'px-4' } }" required>
                    <div class="px-3">
                        <div class="flex items-center space-x-2">
                            <UButton
                                v-for="option in modelOptions"
                                :key="option.value"
                                :color="model === option.value ? 'primary' : 'gray'"
                                :variant="model === option.value ? 'solid' : 'outline'"
                                :disabled="!usableModels.includes(option.value)"
                                @click="model = option.value"
                                class="flex-1"
                            >
                                {{ $t(option.label) }}
                            </UButton>
                        </div>
                    </div>
                    <div class="text-xs px-4 pt-1 text-gray-500">
                        <div v-if="model === modelOptions[0].value">
                            {{ $t('With High Quality, 1,000 characters will cost 1,000 credits.') }}
                        </div>
                        <div v-else-if="model === modelOptions[1].value">
                            {{ $t('With HD Quality, 1,000 characters will cost 2,000 credits.') }}
                        </div>
                        <div v-else>
                            {{ $t('With High Quality Plus, 1,000 characters will cost 1,000 credits.') }}
                        </div>
                    </div>
                </UFormGroup>
                <UFormGroup v-else :label="$t('Quality')" :ui="{ label: { wrapper: 'px-4' } }" required>
                    <div class="px-3">
                        <div class="flex items-center">
                            <UButton
                                color="primary"
                                variant="solid"
                                class="w-full"
                                disabled
                            >
                                {{ $t('High Quality Plus') }}
                            </UButton>
                        </div>
                    </div>
                    <div class="text-xs px-4 pt-1 text-gray-500">
                        {{ $t('With High Quality Plus, 1,000 characters will cost 1,000 credits.') }}
                    </div>
                </UFormGroup>
                <UFormGroup :label="$t('Voice')" :ui="{ label: { wrapper: 'px-4' } }" required>
                    <div v-for="voice in voiceOptions">
                        <BaseVoiceItem
                            v-bind="voice"
                            :active="voiceSelected === voice.value"
                            @select="voiceSelected = $event"
                        />
                    </div>
                </UFormGroup>
                <div class="sm:hidden fixed bottom-4 w-full text-center">
                    <UButton
                        @click="emits('close')"
                        icon="i-icon-park-solid:check-one"
                        size="xl"
                        color="primary"
                        variant="solid"
                        :label="$t('OK')"
                        class="w-32 justify-center"
                        :trailing="false"
                    />
                </div>
            </div>
        </div>
    </UCard>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
const translateStore = useTranslateStore()
const authStore = useAuthStore()
const { usableModels, isUsingFreePlan } = storeToRefs(authStore)
const { speedOptions, speed, loadings, modelOptions, voiceSelected, model, voiceOptions } = storeToRefs(translateStore)

// Force free users to use the new tts-2 model
if (isUsingFreePlan.value) {
    model.value = 'tts-2'
}
const { t } = useI18n()
const props = defineProps({
    showCloseButton: {
        type: Boolean,
        default: false,
    },
})
const emits = defineEmits(['close'])
const links = computed(() => {
    const _link = [
        [
            {
                label: t('Voice Settings'),
                icon: 'i-icon-park-solid-voice-one',
            },
        ],
    ] as any

    if (props.showCloseButton) {
        _link.push([
            {
                icon: 'carbon:close-filled',
                click: () => {
                    emits('close')
                },
            },
        ])
    }

    return _link
})
</script>

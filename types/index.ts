import type { ParsedContent } from '@nuxt/content'
import type { <PERSON><PERSON>, Badge, <PERSON> } from '#ui/types'

export interface BlogPost extends ParsedContent {
  title: string
  description: string
  date: string
  image?: HTMLImageElement
  badge?: Badge
  authors?: ({
    name: string
    description?: string
    avatar: Avatar
  } & Link)[]
}

type Content = Record<string, string>

interface Basic {
    title: string
    description?: string
}

interface Link {
    url: string
    title?: string
    icon: string
}

interface ContentIntroduction {
    title: string
    description: string
    icon?: string
}

interface AudioSetting {
    title: string
    description: string
    icon: string
    tags: string[]
}

interface PoweredCutResearch {
    image: string
    title: string
    description: string
    date_at: string
    url: string
}

interface Review {
    name: string
    image: string
    sub_title: string
    avatar: string
    review: string
    url: string
}

interface AiVoiceGeneratorLanguageContent {
    title: string
    description: string
    image: string
    link: Link
}

interface AiVoiceLabCard {
    title: string
    description: string
    image: string
    link: Link
}

export interface iContent {
    id?: string
    name: string
    page: string
    section: string
    basic: Basic
    content: Content[] | Content | any

    // frontend
    loading?: boolean
}

export interface iContentAiTextToSpeechBlock extends iContent {
    content: {
        image: string
        link: Link
        content_introduction: ContentIntroduction[]
        audio_setting: AudioSetting[]
    }
}

export interface iContentDubbingStudioBlock extends iContent {
    content: {
        video_src: string
        video_tag: string
        introduction: ContentIntroduction[]
    }
    basic: Basic & {
        link: Link
    }
}

export interface iContentChangeVoiceSpeechToSpeechBlock extends iContent {
    content: {
        video_src: string
        video_tag: string
        video_created_by: string
        video_target_link: {
            title: string
            url: string
        }
        introduction: ContentIntroduction[]
    }
    basic: Basic & {
        link: Link
    }
}

export interface iContentLongFormVoiceGenerationBlock extends iContent {
    content: {
        video_src: string
        video_tag: string
        introduction: ContentIntroduction[]
    }
    basic: Basic & {
        link: Link
    }
}

export interface iContentPoweredCutResearchBlock extends iContent {
    content: PoweredCutResearch[]
}

export interface iContentReviewBlock extends iContent {
    content: Review[]
    basic: Basic & {
        image: string
    }
}

export interface iContentAiVoiceGeneratorLanguageBlock extends iContent {
    content: AiVoiceGeneratorLanguageContent[]
    basic: Basic & {
        link: Link
    }
}

export interface iContentAiVoiceLabBlock extends iContent {
    content: {
        card: AiVoiceLabCard[]
        introduction: ContentIntroduction[]
    }
    basic: Basic & {
        link: Link
    }
}

export interface iContentFaqBlock extends iContent {
    content: ContentIntroduction[]
}



// My Voice
export interface MyVoice {
    training_type: MyVoiceType
    speaker_name: string
    gender: string
    age: string
    accent: string
    description: string
    inputFile: File | null
    audio_path: string
}
export const enum MyVoiceType {
    INSTANT_VOICE_CLONING = 'instance_voice',
    PROFESSIONAL_VOICE_CLONING = 'professional_voice',
}

export interface MyVoiceUploadResponse {
    http_method: string
    url: string
    s3_file_path: string
    file_name_origin: string
}

export interface BlockItem {
    uuid: string
    id: string
    name: string
    input: string
    speed: number
    voice_id: string
    voice: Object
    accent?: string
}

// Voice
export enum VoiceTypeEnum {
    OPENAI_VOICE = 'openai_voice',
    SYSTEM_VOICE = 'system_voice',
    USER_VOICE = 'user_voice',
    FAVORITE_VOICE = 'favorite_voice',
}

export enum TrainingStatusEnum {
    COMPLETED = 'completed',
    STARTED = 'started',
    FAILED = 'failed',
}

export enum AccessEnum {
    IS_USE_HD = 'is_use_hd',
    IS_DOWNLOAD = 'is_download',
    IS_UP_FILE_TO_CONVERT = 'is_up_file_to_convert',
    IS_CREATE_VOICE = 'is_create_voice',
    IS_ACCESS_VOICE_LIB = 'is_access_voice_lib',
    IS_CREATE_STORY = 'is_create_story',
    ADS_DISPLAY = 'adsd',
    IS_NO_ADVERTISEMENT = 'is_no_advertisement',
    IS_A_FEW_ADVERTISEMENT = 'is_a_few_advertisement',
    // NO_ADVERTISEMENT_EXPIRE_AT = 'no_advertisement_expire_at',
}

export enum ModeSelectDurationEnum {
    AUTO = 'auto',
    MANUAL = 'manual',
}
export interface Notification {
    created_at: string
    data: string
    event_type: string
    history_uuid: string
    id: number
    seen: boolean
    status: number
    user_uuid: string
    success: boolean
}

export interface MaintenanceNotification {
    id: number
    title: string
    content: string
    display_start: string
    maintain_start: string
    maintain_end: string
}



<!DOCTYPE html>
<title>Site Maintenance</title>
<link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,700" rel="stylesheet" />
<style>
    html,
    body {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
    * {
        box-sizing: border-box;
    }
    body {
        text-align: center;
        padding: 0;
        background: #275e58;
        color: #fff;
        font-family: Open Sans;
    }
    h1 {
        font-size: 50px;
        font-weight: 100;
        text-align: center;
    }
    body {
        font-family: Open Sans;
        font-weight: 100;
        font-size: 20px;
        color: #fff;
        text-align: center;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
    }
    article {
        display: block;
        width: 700px;
        padding: 50px;
        margin: 0 auto;
    }
    a {
        color: #fff;
        font-weight: bold;
    }
    a:hover {
        text-decoration: none;
    }
    svg {
        width: 75px;
        margin-top: 1em;
    }
</style>

<style>
    #tsparticles {
        width: 100%;
        height: 100%;
        position: absolute;
    }
    #coding {
        width: 77%;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    #left,
    #right {
        animation: typing 0.2s cubic-bezier(0.18, 0.85, 0.72, 0.22) infinite alternate;
        transform-origin: center;
        transform-box: fill-box;
    }

    @keyframes typing {
        0% {
            transform: rotateZ(-10deg);
        }
        100% {
            transform: rotateZ(10deg);
        }
    }
    @keyframes move-head {
        0% {
            transform: translateY(0px);
        }
        100% {
            transform: translateY(5px);
        }
    }
    #head {
        animation: move-head 1s cubic-bezier(0.41, 0.16, 0.24, 0.71) infinite alternate;
        transform-origin: center;
        transform-box: fill-box;
    }
</style>

<style>
    .wrapper {
        text-align: center;
        display: grid;
        place-items: center;
        font-size: large;
    }
    .typing-demo {
        width: 22ch;
        animation: typing 2s steps(22), blink 0.5s step-end infinite alternate;
        white-space: nowrap;
        overflow: hidden;
        border-right: 3px solid;
        font-size: 2em;
    }

    @keyframes typing {
        from {
            width: 0;
        }
    }

    @keyframes blink {
        50% {
            border-color: transparent;
        }
    }
</style>

<div id="tsparticles"></div>

<article>
    <svg id="coding" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 374.02 251.41">
        <title>animation</title>
        <circle cx="185.76" cy="122.34" r="122.34" style="fill: #54b2d4" />
        <g id="head">
            <path
                d="M293.3,317.35a17.34,17.34,0,0,0-2.09-9.64c-4.65-7.93-9-23.37-15.67-38.16a25.7,25.7,0,0,0-3.67-5.93,83.49,83.49,0,0,0-9.23-9.5c-10.71-9.27-23.46-11.58-26.32-11.79V242h.23c0,.29-.09.3-.17.31s-.2,0,.65.72v93.1c-.9,14.32,9.85,25.85,22.94,25.56h0A22.65,22.65,0,0,0,277.86,352c6-8.45,13.77-21.12,15.21-32.16A24.49,24.49,0,0,1,293.3,317.35Z"
                transform="translate(-56.67 -185.6)"
                style="stroke: #0d1932; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                d="M248.42,238.85a53.18,53.18,0,0,0-23.69,11.47,89.19,89.19,0,0,0-12.61,13.91,27,27,0,0,0-2.92,5c-6.14,13.67-10.52,27.33-14.8,34.63-1.9,3.24-2.35,7.11-1.82,11.28,1.42,11.25,10,24.38,16.57,33a23.67,23.67,0,0,0,15.64,9.49c14.29,1.83,26.49-10.36,26.49-25.72V239.51A15.87,15.87,0,0,1,248.42,238.85Z"
                transform="translate(-56.67 -185.6)"
                style="stroke: #0d1932; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <polygon
                points="194.41 162.44 176.12 162.44 177.1 178.03 195.39 178.03 194.41 162.44"
                style="fill: #fd916f; stroke: #0d1932; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                d="M251.75,359.28c0,2.1-4.07,3.8-9.08,3.8s-9.37-2.5-9.37-4.6,4.35-2.37,9.37-2.37S251.75,357.19,251.75,359.28Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #fd916f; stroke: #0d1932; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                d="M221.82,311.3c0,5.55-3.93,10-8.78,10s-10.15-6.62-10.15-12.16,1.75-7.92,6.59-7.92S221.82,305.76,221.82,311.3Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #c17b62"
            />
            <path
                d="M264.73,311.3c0,5.55,3.92,10,8.77,10s10.15-6.62,10.15-12.16-1.74-7.92-6.59-7.92S264.73,305.76,264.73,311.3Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #c17b62"
            />
            <path
                d="M278.22,295.32c0,28-15.54,56-34.7,56s-34.72-27.92-34.72-56,15.55-44.83,34.72-44.83S278.22,267.28,278.22,295.32Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #d9ad89"
            />
            <path
                d="M293.88,315.63a.71.71,0,0,0,0-.25c0-.43-.07-.86-.11-1.32l-.1-1.12a126.37,126.37,0,0,0-2.16-13.23c-7.74-34.51-15.18-49.47-21-54.44-2.19-1.87-10.42-6.42-17.09-7.44a27.8,27.8,0,0,0-3.36-.5,1,1,0,0,1-.34-.08l-4.87-2.32a3.2,3.2,0,0,0-4.55,2.4h0l-.16,0c-.12,0,.42-1.23.37-1.23s-.64,1.21-.64,1.21v0c-35.28,4.87-46.79,41.86-47.12,76.27a.91.91,0,0,0,1.34.8c30.55-16.38,26.73-19.21,61.76-15.9,9.09.86,13.59-.19,21.46,2.75,8.88,3.32,14.13,8.81,16.24,16.08C293.64,316.78,293.75,316.2,293.88,315.63Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #36476f; stroke: #0e1a32; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                id="earphones"
                d="M278,314.79s4.81,44.64-35.56,44.8-33.86-44.8-33.86-44.8"
                transform="translate(-56.67 -185.6)"
                style="fill: none; stroke: #fff; stroke-linecap: round; stroke-miterlimit: 10; stroke-width: 4px"
            />
        </g>
        <g id="rest">
            <path
                id="body"
                d="M389.24,435c-30.83-63.54-77.64-72.2-77.64-72.2V362H184.14s-44.9,7-78.7,72.09H189.7Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #36476f; stroke: #0e1a32; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <rect
                x="122.21"
                y="231.3"
                width="131.76"
                height="14.6"
                style="fill: #fff; stroke: #000; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                d="M310.69,426.63H179.59c-2.47,0-4.5-2.32-4.5-5.16V332.75H315.18v88.72C315.18,424.31,313.16,426.63,310.69,426.63Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #565c68; stroke: #000; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                d="M316,431.5a2.36,2.36,0,0,1-2.27,2.43H177.05a2.36,2.36,0,0,1-2.27-2.43h0c0-1.35,1-2.44,2-4.87H313.44c1.49,2.43,2.51,3.52,2.51,4.87Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #606c71; stroke: #000; stroke-miterlimit: 10; stroke-width: 4px"
            />
            <path
                d="M293.72,335s16.61,49.37-10.26,57-45.19,11.19-54.86,10.18-21.89-10.18-29.52-16.8-19.53-16.61-20.2-23.32,4.71-24.85,22.82-27.07S293.72,335,293.72,335Z"
                transform="translate(-56.67 -185.6)"
                style="fill: #373e46"
            />
            <ellipse cx="187.01" cy="197.24" rx="6.27" ry="6.71" style="fill: #989fa4" />
        </g>
        <line
            id="left"
            x1="118.43"
            y1="232.25"
            x2="73.23"
            y2="224.9"
            style="fill: #fff; stroke: #0d1932; stroke-miterlimit: 10; stroke-width: 4px"
        />
        <line
            id="right"
            x1="257.88"
            y1="232.25"
            x2="303.07"
            y2="224.9"
            style="fill: #fff; stroke: #0d1932; stroke-miterlimit: 10; stroke-width: 4px"
        />
        <line
            y1="249.4"
            x2="374.02"
            y2="249.4"
            style="fill: none; stroke: #0e1a32; stroke-miterlimit: 10; stroke-width: 4px"
        />
    </svg>
    <div class="wrapper">
        <div class="typing-demo">We&rsquo;ll be back soon!</div>
    </div>
    <div>
        <p>
            Sorry for the inconvenience.
            <br />
            We&rsquo;re performing some maintenance at the moment.
        </p>

        <p>&mdash; The Text To Speech development Team</p>
    </div>
</article>
<script src="https://cdn.jsdelivr.net/npm/tsparticles@1.18.3/dist/tsparticles.min.js"></script>

<script>
    // set timer to reload page after 30 seconds
    setInterval(() => {
        location.reload()
    }, 30000)
    
    tsParticles.load('tsparticles', {
        fullScreen: false,
        fpsLimit: 60,
        particles: {
            number: { value: 80, density: { enable: true, value_area: 800 } },
            color: { value: ['#06d6a0', '#ffa3a5', '#ffbf81', '#ffdc5e'] },
            shape: { type: ['circle'], stroke: { width: 0, color: '#fff' }, polygon: { nb_sides: 5 } },
            opacity: {
                value: 1,
                random: false,
                anim: { enable: false, speed: 1, opacity_min: 0.1, sync: false, minimumValue: 0.1 },
            },
            size: {
                value: 8,
                random: true,
                anim: { enable: false, speed: 10, size_min: 10, sync: false, minimumValue: 10 },
            },
            line_linked: { enable: true, distance: 150, color: '#808080', opacity: 0.4, width: 1 },
            move: {
                enable: true,
                speed: 2,
                direction: 'none',
                random: false,
                straight: false,
                out_mode: 'out',
                bounce: false,
                attract: { enable: false, rotateX: 600, rotateY: 1200 },
            },
        },
        interactivity: {
            detect_on: 'canvas',
            events: { onhover: { enable: true, mode: 'grab' }, onclick: { enable: true, mode: 'push' }, resize: true },
            modes: {
                grab: { distance: 140, line_linked: { opacity: 1 } },
                bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 2 },
                repulse: { distance: 200, duration: 0.4 },
                push: { particles_nb: 4 },
                remove: { particles_nb: 2 },
            },
        },
        retina_detect: true,
    })
</script>
